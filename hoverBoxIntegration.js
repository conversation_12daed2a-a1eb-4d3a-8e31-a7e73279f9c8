/**
 * hoverBoxIntegration.js
 * 
 * This file provides integration between content-script.js and hoverBox.js
 * to prevent duplicate hover boxes and animation message boxes when buttons are reinjected.
 */

// Global state for tracking hover box integration
window.velocityHoverBoxIntegration = {
  initialized: false,
  lastButtonId: null,
  messageBoxCount: 0
};

/**
 * Initialize or reinitialize the hover box with proper cleanup
 */
function initializeOrReinitializeHoverBox() {
  // First clean up any existing hover boxes
  cleanupHoverBoxes();
  
  // Then initialize the hover box if the function is available
  if (typeof window.initializeVelocityHoverBox === 'function') {
    try {
      window.initializeVelocityHoverBox();
      window.velocityHoverBoxIntegration.initialized = true;
      // console.log("[Velocity] Hover box initialized successfully");
    } catch (e) {
      // console.error("[Velocity] Error initializing hover box:", e);
    }
  } else {
    console.warn("[Velocity] initializeVelocityHoverBox function not available");
  }
}

/**
 * Clean up all hover boxes and reset state
 */
function cleanupHoverBoxes() {
  // Clean up hover box state
  if (window.velocityHoverBoxState) {
    if (window.velocityHoverBoxState.activeHoverBox) {
      const hoverBox = window.velocityHoverBoxState.activeHoverBox;
      if (hoverBox && hoverBox.parentNode) {
        hoverBox.parentNode.removeChild(hoverBox);
      }
    }
    
    // Reset hover box state
    window.velocityHoverBoxState.activeHoverBox = null;
    window.velocityHoverBoxState.initialized = false;
    
    // Clear any hover timeouts
    if (window.velocityHoverBoxState.hoverTimeout) {
      clearTimeout(window.velocityHoverBoxState.hoverTimeout);
      window.velocityHoverBoxState.hoverTimeout = null;
    }
  }
  
  // Also clean up any stray hover boxes that might not be tracked in the state
  document.querySelectorAll('.velocity-hover-box').forEach(box => {
    if (box && box.parentNode) {
      box.parentNode.removeChild(box);
    }
  });
}

/**
 * Clean up all message boxes
 */
function cleanupMessageBoxes() {
  // Remove any existing message boxes
  document.querySelectorAll('.velocity-message-box').forEach(box => {
    if (box && box.parentNode) {
      box.parentNode.removeChild(box);
    }
  });
  
  // Reset message box count
  window.velocityHoverBoxIntegration.messageBoxCount = 0;
}

/**
 * Clean up everything related to hover boxes and message boxes
 */
function cleanupAll() {
  cleanupHoverBoxes();
  cleanupMessageBoxes();
  window.velocityHoverBoxIntegration.initialized = false;
}

/**
 * Track a new button and set up hover box for it
 * @param {HTMLElement} button - The button element
 */
function trackButton(button) {
  if (!button) return;
  
  // Generate a unique ID for the button if it doesn't have one
  if (!button.id) {
    button.id = 'velocity-button-' + Date.now();
  }
  
  // If this is a new button, reinitialize the hover box
  if (window.velocityHoverBoxIntegration.lastButtonId !== button.id) {
    window.velocityHoverBoxIntegration.lastButtonId = button.id;
    initializeOrReinitializeHoverBox();
  }
}

/**
 * Monitor for new message boxes and prevent duplicates
 */
function setupMessageBoxMonitoring() {
  // Use a mutation observer to detect new message boxes
  const observer = new MutationObserver(mutations => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (const node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE && 
              node.classList && 
              node.classList.contains('velocity-message-box')) {
            
            // Count message boxes
            window.velocityHoverBoxIntegration.messageBoxCount++;
            
            // If we have more than one message box, remove the extras
            if (window.velocityHoverBoxIntegration.messageBoxCount > 1) {
              const messageBoxes = document.querySelectorAll('.velocity-message-box');
              // Keep only the most recently added message box
              for (let i = 0; i < messageBoxes.length - 1; i++) {
                if (messageBoxes[i] && messageBoxes[i].parentNode) {
                  messageBoxes[i].parentNode.removeChild(messageBoxes[i]);
                }
              }
              window.velocityHoverBoxIntegration.messageBoxCount = 1;
            }
          }
        }
      }
    }
  });
  
  // Start observing the document body for message box additions
  observer.observe(document.body, { 
    childList: true, 
    subtree: true 
  });
  
  return observer;
}

// Export functions to global scope
window.velocityHoverBoxIntegration = window.velocityHoverBoxIntegration || {};
window.velocityHoverBoxIntegration.initializeOrReinitializeHoverBox = initializeOrReinitializeHoverBox;
window.velocityHoverBoxIntegration.cleanupHoverBoxes = cleanupHoverBoxes;
window.velocityHoverBoxIntegration.cleanupMessageBoxes = cleanupMessageBoxes;
window.velocityHoverBoxIntegration.cleanupAll = cleanupAll;
window.velocityHoverBoxIntegration.trackButton = trackButton;

// Set up message box monitoring when the script loads
const messageBoxObserver = setupMessageBoxMonitoring();
window.velocityHoverBoxIntegration.messageBoxObserver = messageBoxObserver;

// console.log("[Velocity] HoverBoxIntegration.js loaded successfully");
