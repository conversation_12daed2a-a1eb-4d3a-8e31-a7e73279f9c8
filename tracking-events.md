# Extension Tracking Events Documentation

This document lists all tracking events implemented in the extension, including both Google Analytics 4 (GA4) and Mixpanel events, along with their triggers and locations.

## Analytics Platforms Used

- **Google Analytics 4 (GA4)** - Implemented via `ga4-measurement-protocol.js`
- **Mixpanel** - Implemented via `mixpanel-init.js` and `popup.js`

## Event Categories

### 1. Extension Lifecycle Events

#### Extension Installation
- **Event Name (GA4)**: `extension_installed`
- **Trigger**: When extension is first installed
- **Location**: `background.js` (line 109)
- **Properties**: 
  - `reason`: Installation reason
  - `language`: Browser UI language

#### Extension Opened
- **Event Name (Mixpanel)**: `Extension Opened`
- **Trigger**: When extension popup is opened
- **Location**: `popup.js` (line 39)
- **Properties**:
  - `referrer`: Document referrer
  - `screen_width`: Window width
  - `screen_height`: Window height
  - `url`: Current URL
  - `entry_point`: "popup"

#### Extension Toggle
- **Event Name (Mixpanel)**: `Extension Toggle`
- **Trigger**: When extension is enabled/disabled via toggle
- **Location**: `popup.js` (line 1352)
- **Properties**:
  - `enabled`: Boolean toggle state
  - `platform`: Selected platform
  - `style`: Selected style
  - `user_type`: "free", "paid", or "anonymous"

### 2. Session Tracking Events

#### Session Started
- **Event Name (Mixpanel)**: `Session Started`
- **Trigger**: When extension popup opens
- **Location**: `popup.js` (line 48)
- **Properties**:
  - `entry_point`: "extension_icon"
  - `platform`: Selected platform
  - `style`: Selected style
  - `timestamp`: ISO timestamp

#### Session Ended
- **Event Name (Mixpanel)**: `Session Ended`
- **Trigger**: When popup window is closed (beforeunload)
- **Location**: `popup.js` (line 1414)
- **Properties**:
  - `duration_ms`: Session duration in milliseconds
  - `actions_performed`: Number of clicks during session
  - `platform`: Selected platform
  - `style`: Selected style

#### Platform Session Start
- **Event Name (GA4)**: `platform_session_start`
- **Trigger**: When user starts session on a platform
- **Location**: `background.js` (line 84)
- **Properties**:
  - `platform`: Platform name
  - `timestamp_millis`: Timestamp in milliseconds

#### Platform Session End
- **Event Name (GA4)**: `platform_session_end`
- **Trigger**: When platform session ends
- **Location**: `background.js` (line 96)
- **Properties**:
  - `platform`: Platform name
  - `engagement_time_msec`: Session duration
  - `button_clicks`: Number of button clicks

### 3. Button and UI Interaction Events

#### Button Injected
- **Event Name (GA4)**: `button_injected`
- **Event Name (Mixpanel)**: `Button Injected`
- **Trigger**: When enhance button is injected into platform page
- **Location**: `background.js` (line 235, 240) and `content-script.js` (line 871)
- **Properties**:
  - `platform`: Platform name
  - `url`: Page URL

#### Platform Button Clicked
- **Event Name (Mixpanel)**: `Platform Button Clicked`
- **Trigger**: When enhance button is clicked on platform page
- **Location**: `content-script.js` (line 1528)
- **Properties**:
  - `platform`: Platform name
  - `prompt_length`: Length of current prompt

#### Enhance Button Clicked
- **Event Name (GA4)**: `enhance_button_clicked`
- **Trigger**: When enhance button is clicked
- **Location**: `background.js` (line 250)
- **Properties**:
  - `platform`: Platform name

#### Generate Button Clicked
- **Event Name (Mixpanel)**: `Generate Button Clicked`
- **Trigger**: When generate button is clicked in popup
- **Location**: `popup.js` (line 1636)
- **Properties**:
  - `style`: Selected style
  - `platform`: Selected platform
  - `prompt_length`: Length of prompt text
  - `word_count`: Number of words in prompt
  - `has_text`: Boolean if prompt has text
  - `contains_url`: Boolean if prompt contains URLs
  - `contains_code`: Boolean if prompt contains code blocks
  - `has_bullet_points`: Boolean if prompt has bullet points
  - `has_numbered_list`: Boolean if prompt has numbered lists

#### Profile Button Clicked
- **Event Name (Mixpanel)**: `Profile Button Clicked`
- **Trigger**: When profile button is clicked
- **Location**: `popup.js` (line 2688)
- **Properties**:
  - `user_type`: "paid"
  - `user_id`: User ID
  - `user_name`: User name
  - `platform`: Selected platform
  - `style`: Selected style

#### Login Button Clicked
- **Event Name (Mixpanel)**: `Login Button Clicked`
- **Trigger**: When login button is clicked from trial finished popup
- **Location**: `trail-finished.js` (line 371)
- **Properties**:
  - `source`: "free_trial_notification"
  - `free_trial_status`: "expired"
  - `free_trial_uses`: 3

### 4. Content and Enhancement Events

#### Enhancement Success
- **Event Name (GA4)**: `enhance_prompt_success`
- **Event Name (Mixpanel)**: `Enhancement Success`
- **Trigger**: When prompt enhancement is successful
- **Location**: `background.js` (line 274) and `popup.js` (line 2165)
- **Properties**:
  - `platform`: Platform name
  - `original_length`: Original prompt length
  - `enhanced_length`: Enhanced prompt length
  - `processing_time_ms`: Processing time
  - `tokens_used`: Tokens deducted
  - `user_type`: User type
  - `free_usage_count`: Free usage count (if applicable)

#### Enhancement Error
- **Event Name (GA4)**: `enhance_prompt_error`
- **Trigger**: When prompt enhancement fails
- **Location**: `background.js` (line 290)
- **Properties**:
  - `platform`: Platform name
  - `error_message`: Error message

#### Copied Prompt
- **Event Name (Mixpanel)**: `Copied Prompt`
- **Trigger**: When copy button is clicked
- **Location**: `popup.js` (line 1838)
- **Properties**:
  - `type`: "Original"
  - `prompt_length`: Text length
  - `word_count`: Word count
  - `has_style`: Boolean if style is selected
  - `style`: Selected style
  - `platform`: Selected platform
  - `contains_url`: Boolean if contains URLs
  - `contains_code`: Boolean if contains code

#### LLM Link Clicked
- **Event Name (Mixpanel)**: `LLM Link Clicked`
- **Trigger**: When LLM platform link is clicked
- **Location**: `popup.js` (line 3376)
- **Properties**:
  - `llm_provider`: Platform name
  - `url`: Platform URL
  - `prompt_length`: Response content length

### 5. User Management Events

#### Free Trial Usage
- **Event Name (Mixpanel)**: `Free Trial Usage`
- **Trigger**: When free user uses the service
- **Location**: `popup.js` (line 1955)
- **Properties**:
  - `usage_count`: Current usage count
  - `remaining_uses`: Remaining free uses
  - `is_last_use`: Boolean if this is the last free use
  - `style`: Selected style
  - `platform`: Selected platform
  - `prompt_length`: Prompt length

#### Free Trial Completed
- **Event Name (Mixpanel)**: `Free Trial Completed`
- **Trigger**: When free trial is exhausted
- **Location**: `popup.js` (line 1966)
- **Properties**:
  - `total_uses`: Total uses completed
  - `timestamp`: ISO timestamp

### 6. Technical Events

#### Content Script Injected
- **Event Name (GA4)**: `content_script_injected`
- **Trigger**: When content scripts are successfully injected
- **Location**: `background.js` (line 779)
- **Properties**:
  - `url`: Tab URL
  - `trigger`: "tab_updated"

#### Content Script Injection Failed
- **Event Name (GA4)**: `content_script_injection_failed`
- **Trigger**: When content script injection fails
- **Location**: `background.js` (line 786)
- **Properties**:
  - `url`: Tab URL
  - `error`: Error message

#### Extension Popup Opened
- **Event Name (GA4)**: `extension_popup_opened`
- **Trigger**: When extension popup is opened programmatically
- **Location**: `background.js` (line 724)
- **Properties**:
  - `trigger`: Trigger source

#### Prompt Characteristics
- **Event Name (GA4)**: `prompt_characteristics`
- **Trigger**: When prompt characteristics are analyzed
- **Location**: `background.js` (line 708)
- **Properties**:
  - `platform`: Platform name
  - `promptLength`: Prompt length
  - `wordCount`: Word count
  - `sentenceCount`: Sentence count
  - `hasBulletPoints`: Boolean
  - `hasNumberedList`: Boolean

### 7. Error and Refinement Events

#### Prompt Refinement Error
- **Event Name (Mixpanel)**: `Prompt Refinement Error`
- **Trigger**: When prompt refinement fails
- **Location**: `promptReview.js` (line 1342)
- **Properties**:
  - `error_message`: Error message
  - `stage`: Refinement stage where error occurred
  - `platform`: Platform name

## Event Tracking Infrastructure

### Mixpanel Configuration
- **Token**: Configured in popup.js
- **Initialization**: `popup.js` line 16-64
- **Super Properties**: Set automatically with user context
- **User Identification**: Automatic for logged-in users

### GA4 Configuration
- **Measurement ID**: Configured in `ga4-measurement-protocol.js`
- **API Secret**: Configured for server-side tracking
- **Client ID**: Auto-generated and persisted
- **Session Management**: Automatic session tracking

### Cross-Component Communication
- Events from content scripts are forwarded to popup via `chrome.runtime.sendMessage`
- Background script forwards Mixpanel events to popup for tracking
- All events include contextual properties like platform, user type, and timestamps

## Supported Platforms
The extension tracks events across these platforms:
- ChatGPT (chat.openai.com, chatgpt.com)
- Claude (claude.ai)
- Gemini (gemini.google.com)
- Perplexity (perplexity.ai)
- Grok (grok.com)
- Bolt (bolt.new)
- V0 (v0.dev)
- Gamma (gamma.app)
- Mistral (chat.mistral.ai, mistral.ai)
- Lovable (lovable.dev)
- Replit (replit.com)
