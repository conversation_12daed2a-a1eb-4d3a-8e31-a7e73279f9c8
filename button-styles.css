/* Velocity Button Styles */
.velocity-button-container {
  position: fixed;
  z-index: 10000;
  pointer-events: auto;
  cursor: move;
}

.velocity-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: black;
  height: 36px;
  width: 36px;
  transition: opacity 0.2s;
  border: none;
  outline: none;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  cursor: pointer;
  pointer-events: auto;
  z-index: 10;
}

.velocity-button:hover {
  opacity: 0.7;
}

.velocity-button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Animation classes */
.velocity-loading-animation,
.velocity-half-circle-glow,
.velocity-inner-pulse-bounce,
.velocity-inner-pulse-bounce-shake,
.velocity-idle-typing-effect,
.velocity-success-idle-effect,
.velocity-multi-ring-container,
.velocity-multi-ring,
.velocity-splash,
.velocity-highlight-pulse,
.velocity-enhanced-highlight,
.velocity-enhanced-scale {
  transition: all 0.3s ease;
}

/* Enhanced highlight animations */
@keyframes velocity-enhanced-highlight {
  0% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
  30% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
  70% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
  100% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
}

@keyframes velocity-enhanced-scale {
  0% { transform: scale(1); }
  30% { transform: scale(1.03); }
  70% { transform: scale(1.03); }
  100% { transform: scale(1); }
}

.velocity-enhanced-highlight {
  animation: velocity-enhanced-highlight 1s ease-in-out forwards;
  border-color: #0088cb !important;
  transition: all 0.3s ease;
}

.velocity-enhanced-scale {
  animation: velocity-enhanced-scale 1s ease-in-out forwards;
}

.text-pop-effect {
  animation: text-pop 0.3s ease-in-out;
}

@keyframes text-pop {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Custom injected button styles */
.custom-injected-button {
  position: fixed;
  z-index: 10000;
  pointer-events: auto;
  cursor: move;
}