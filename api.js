
// const PROMPT_BASE_URL = "https://thinkvelocity.in/post";
export async function enhancePromptRequest(message, role) {
  // //console.log("Enhance Prompt Request:", message);
  try {
    const response = await fetch(
      "https://thinkvelocity.in/python-backend-D/enhance",

      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: message.prompt,
          style: message.style,
          role: role || "user",
        }),
      }
    );
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    // console.error("Enhance prompt error:", error);
    return { success: false, error: error.message };
  }
}

export async function EnhancePromptV2(message, role) {
  //console.log("[Velocity DEBUG] EnhancePromptV2 function called with:", {
  //   promptLength: message.prompt?.length || 0,
  //   writing_style: message.writing_style,
  //   style: message.style, // Log style for debugging
  //   llm: message.llm,
  //   role: role
  // });
  
  try {
    // Validate required parameters
    if (!message.prompt) {
      console.error("[Velocity DEBUG] Missing required parameter: prompt");
      return { success: false, error: "Missing required parameter: prompt" };
    }
    
    //console.log("[Velocity DEBUG] Preparing to fetch from vel-prompt/enhance endpoint");
    const requestBody = {
      prompt: message.prompt,
      writing_style: message.writing_style || message.style || "Descriptive", // Fallback to style if writing_style not provided
      llm: message.llm || ""
    };
    //console.log("[Velocity DEBUG] Request body:", requestBody);
    
    const response = await fetch(
      "https://thinkvelocity.in/vel-prompt/enhance",
      // "http://127.0.0.1:2000/enhance",
      {
        method: "POST",
        headers: {
          "Authorization": await getAuthorizationKey(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      }
    );
    //console.log("ajaaaaaaaaaaa",response)

    
    //console.log("[Velocity DEBUG] API response status:", response.status);
    
    if (!response.ok) {
      let errorMessage = `HTTP error! Status: ${response.status}`;
      try {
        const errorText = await response.text();
        console.error("[Velocity DEBUG] API error response:", errorText);
        try {
          // Try to parse the error as JSON
          const errorJson = JSON.parse(errorText);
          if (errorJson.message || errorJson.error) {
            errorMessage = errorJson.message || errorJson.error;
          }
        } catch (parseError) {
          // If not JSON, use the raw text
          if (errorText && errorText.length < 100) {
            errorMessage = errorText;
          }
        }
      } catch (textError) {
        console.error("[Velocity DEBUG] Failed to read error response text:", textError);
      }
      throw new Error(errorMessage);
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("[Velocity DEBUG] Failed to parse response as JSON:", jsonError);
      const rawText = await response.text();
      //console.log("[Velocity DEBUG] Raw response text:", rawText.substring(0, 200));
      throw new Error("Invalid JSON response from API");
    }
    
    //console.log("[Velocity DEBUG] API response data:", {
    //   dataType: typeof data,
    //   hasEnhancedPrompt: data.enhanced_prompt ? "yes" : "no",
    //   dataKeys: Object.keys(data),
    //   sample: typeof data === 'string' ? data.substring(0, 50) : null
    // });
    
    // Handle case where the response is just a string (the enhanced prompt directly)
    if (typeof data === 'string') {
      data = { enhanced_prompt: data };
    }
    
    return { success: true, data };
  } catch (error) {
    console.error("[Velocity DEBUG] EnhancePromptV2 error:", error);
    console.error("[Velocity DEBUG] Error stack:", error.stack);
    return { success: false, error: error.message };
  }
}

export async function promptAnalysis(message) {
  try {
    // Create request body with the prompt
    const requestBody = {
      query: message.prompt,
      platform: message.platform || "unknown",
      style: message.style || "default"
    };
    
    //console.log("[Velocity DEBUG] promptMetricsAnalysis request body:", requestBody);
    
    const response = await fetch("https://thinkvelocity.in/python-backend-D/analyze", {
      method: "POST",
      headers: {
        "Authorization": await getAuthorizationKey(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // Parse the API response
    const apiResponse = await response.json();
    //console.log("[Velocity DEBUG] Raw API response:", apiResponse);
    
    // Return the raw API response without transformation
    // This preserves the original structure including framework_analysis
    return { success: true, data: apiResponse };
  } catch (error) {
    console.error("[Velocity DEBUG] promptMetricsAnalysis error:", error);
    return { success: false, error: error.message };
  }
}

export async function validateCredits(message) {
  // //console.log("🔍 validateCredits called with message:", message);
  try {
    const storage = await chrome.storage.local.get(["userId", "token"]);
    const userId = storage.userId;
    const token = storage.token;
    // //console.log("👤 User authentication data:", { userId, tokenLength: token?.length });

    if (!userId || !token) {
      // console.error("❌ Authentication failed: Missing userId or token");
      throw new Error("User authentication required");
    }

    // Get feature credits first
    // //console.log("📡 Fetching credits from new API endpoint...");
    const creditsResponse = await fetch(
      "https://thinkvelocity.in/backend-V1-D/token/fetch-credits", // Updated endpoint
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const creditsData = await creditsResponse.json();
    // //console.log("📋 Credits data received from new API:", creditsData);
    
    if (!creditsData.success) {
      // console.error("❌ Credits API error:", creditsData.message || "Failed to fetch credits");
      throw new Error(creditsData.message || "Failed to fetch credits");
    }

    let totalRequiredCredits = 0;

    // Basic prompt credits
    const basicPromptCredit = creditsData.credits.basic_prompt; // Updated to match new response structure
    // //console.log("🔢 Basic prompt credit from new API:", basicPromptCredit);
    
    if (!basicPromptCredit) {
      // console.error("❌ Basic prompt feature not found in new API response");
      throw new Error("Basic prompt feature not found");
    }
    totalRequiredCredits += basicPromptCredit;
    // //console.log("🧮 Total required credits (after basic):", totalRequiredCredits);

    // Style credits if style is selected
    if (message.style && message.style !== "") {
      const styleCredit = creditsData.credits.style_prompt;
      // //console.log("🎨 Style credit from new API:", styleCredit);
      if (!styleCredit) {
        // console.error("❌ Style feature not found in new API response");
        throw new Error("Style feature not found");
      }
      totalRequiredCredits += styleCredit;
      // //console.log("🧮 Total required credits (after style):", totalRequiredCredits);
    }

    // Platform credits if platform is selected
    if (message.platform && message.platform !== "") {
      const platformCredit = creditsData.credits.platform;
      // //console.log("📱 Platform credit from new API:", platformCredit);
      if (!platformCredit) {
        // console.error("❌ Platform feature not found in new API response");
        throw new Error("Platform feature not found");
      }
      totalRequiredCredits += platformCredit;
      // //console.log("🧮 Total required credits (after platform):", totalRequiredCredits);
    }

    // Get user's token balance
    // //console.log("📡 Fetching user balance from new API endpoint...");
    const balanceResponse = await fetch(
      `https://thinkvelocity.in/backend-V1-D/token/fetch-tokens/${userId}`, // Updated endpoint
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    const balanceData = await balanceResponse.json();
    // //console.log("💰 User balance data from new API:", balanceData);
    
    if (!balanceData.success) {
      // console.error("❌ Balance API error:", balanceData.message || "Failed to fetch tokens");
      throw new Error(balanceData.message || "Failed to fetch tokens");
    }

    const availableTokens = balanceData.tokens; // Updated to match new response structure
    // //console.log("📊 Available tokens from new API:", availableTokens);

    if (availableTokens < totalRequiredCredits) {
      // console.error("❌ Insufficient tokens:", {
      //   available: availableTokens,
      //   required: totalRequiredCredits
      // });
      throw new Error("Insufficient tokens available");
    }

    // //console.log("✅ Credit validation successful with new APIs");
    return {
      success: true,
      requiredCredits: totalRequiredCredits,
      availableTokens: availableTokens,
    };
  } catch (error) {
    // console.error("❌ Credit validation failed:", error);
    throw error;
  }
}

export async function deductCredits(creditsToDeduct) {
  // //console.log("🔽 deductCredits called with credits to deduct:", creditsToDeduct);
  try {
    const storage = await chrome.storage.local.get(["userId", "token"]);
    const userId = storage.userId;
    const token = storage.token;
    // //console.log("👤 User data for deduction:", { userId, tokenAvailable: !!token });
    
    if (!userId || !token) {
      // console.error("❌ Authentication failed in deductCredits");
      throw new Error("User authentication required");
    }
  
    // Fetch current token balance (optional, since the new endpoint doesn't require it)
    // //console.log("📡 Fetching current balance before deduction from new API...");
    const balanceResponse = await fetch(
      `https://thinkvelocity.in/backend-V1-D/token/fetch-tokens/${userId}`, // Updated endpoint
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    const balanceData = await balanceResponse.json();
    // //console.log("💰 Current balance data from new API:", balanceData);
    
    if (!balanceData.success) {
      // console.error("❌ Balance API error:", balanceData.message || "Failed to fetch tokens");
      throw new Error(balanceData.message || "Failed to fetch tokens");
    }
  
    // Deduct tokens using the new endpoint
    // //console.log("📡 Deducting tokens with new API endpoint...");
    const requestBody = {
      user_id: userId,
      amount: creditsToDeduct
    };
    // //console.log("📤 Deduction request body:", requestBody);
    
    const deductResponse = await fetch(
      "https://thinkvelocity.in/backend-V1-D/token/deduct-tokens", // Updated endpoint
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      }
    );
  
    const deductData = await deductResponse.json();
    // //console.log("✅ Deduction response from new API:", deductData);
    
    if (!deductData.success) {
      // console.error("❌ Deduction API error:", deductData.message || "Failed to deduct tokens");
      throw new Error(deductData.message || "Failed to deduct tokens");
    }
    
    // //console.log("✅ Credits successfully deducted with new API");
    return { success: true, data: deductData };
  } catch (error) {
    // console.error("❌ Error in deductCredits:", error);
    throw error;
  }
}

export async function savePromptToHistory(message) {
  // //console.log("📝 savePromptToHistory called with message:", message);
  try {
    const storage = await chrome.storage.local.get(["userId", "token"]);
    const userId = storage.userId;
    const token = storage.token;
    // //console.log("👤 User authentication data:", { userId, tokenLength: token?.length });

    if (!userId || !token) {
      // console.error("❌ Authentication failed: Missing userId or token in savePromptToHistory");
      throw new Error("User authentication required");
    }

    // Log request details
    const requestBody = {
      user_id: userId,
      prompt: message.prompt, // Updated field name from prompt_text to prompt
      ai_type: message.platform,
      style: message.style,
      tokens_used: message.tokens_used || 0, // Add tokens_used, default to 0 if not provided
    };
    // //console.log("📤 savePromptToHistory - Request body:", requestBody);
    // //console.log("🔗 Using new API endpoint: https://thinkvelocity.in/post/prompt/save-prompt");

    const response = await fetch(
      "https://thinkvelocity.in/backend-V1-D/prompt/save-prompt", // Updated endpoint
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      }
    );

    // Log response details
    const data = await response.json();
    // //console.log("📥 savePromptToHistory - Response status:", response.status);
    // //console.log("📥 savePromptToHistory - Response data:", data);

    if (!response.ok) {
      // console.error("❌ HTTP error in savePromptToHistory:", {
      //   status: response.status,
      //   message: data.message || "Unknown error"
      // });
      throw new Error(
        `HTTP error! Status: ${response.status}, Message: ${
          data.message || "Unknown error"
        }`
      );
    }

    if (!data.success) {
      // console.error("❌ API error in savePromptToHistory:", data.message || "Failed to save prompt: Unknown error");
      throw new Error(data.message || "Failed to save prompt: Unknown error");
    }

    // //console.log("✅ Prompt successfully saved to history with ID:", data.prompt_id || "unknown");
    return data;
  } catch (error) {
    // console.error("❌ Error saving prompt to history:", {
    //   error: error.message,
    //   stack: error.stack,
    // });
    throw error;
  }
}

export async function saveResponseToHistory(
  enhancedPrompt,
  originalPromptId,
  message
) {
  // //console.log("💬 saveResponseToHistory called with:", {
  //   enhancedPromptLength: enhancedPrompt?.length,
  //   originalPromptId,
  //   message
  // });
  
  try {
    const storage = await chrome.storage.local.get(["userId", "token"]);
    const userId = storage.userId;
    const token = storage.token;
    // //console.log("👤 User authentication data:", { userId, tokenLength: token?.length });

    if (!userId || !token) {
      // console.error("❌ Authentication failed: Missing userId or token in saveResponseToHistory");
      throw new Error("User authentication required");
    }

    const requestBody = {
      user_id: userId,
      enhanced_prompt: enhancedPrompt, // Updated field name from prompt_text to enhanced_prompt
      input_prompt_id: originalPromptId, // Updated field name from original_prompt_id to input_prompt_id
      ai_type: message.platform,
      style: message.style,
      // tokens_used field is removed as per the new API
    };
    
    // //console.log("📤 saveResponseToHistory - Request body:", {
    //   ...requestBody,
    //   enhanced_prompt: enhancedPrompt?.substring(0, 100) + (enhancedPrompt?.length > 100 ? "..." : "")
    // });
    // //console.log("🔗 Using new API endpoint: https://thinkvelocity.in/post/prompt/save-response");

    const response = await fetch(
      "https://thinkvelocity.in/backend-V1-D/prompt/save-response", // Updated endpoint
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      }
    );

    const data = await response.json();
    // //console.log("📥 saveResponseToHistory - Response status:", response.status);
    // //console.log("📥 saveResponseToHistory - Response data:", data);
    
    if (!response.ok) {
      // console.error("❌ HTTP error in saveResponseToHistory:", {
      //   status: response.status,
      //   message: data.message || "Unknown error"
      // });
      throw new Error(
        `HTTP error! Status: ${response.status}, Message: ${
          data.message || "Unknown error"
        }`
      );
    }

    if (!data.success) {
      // console.error("❌ API error in saveResponseToHistory:", data.message || "Failed to save response: Unknown error");
      throw new Error(data.message || "Failed to save response: Unknown error");
    }

    // //console.log("✅ Response successfully saved to history with ID:", data.response_id || "unknown");
    return data;
  } catch (error) {
    // console.error("❌ Error saving response to history:", {
    //   error: error.message,
    //   stack: error.stack
    // });
    throw error;
  }
}

export async function addContext(message) {
  //console.log("[DEBUG] addContext API function called with:", {
  //   hasPrompt: !!message.prompt,
  //   promptLength: message.prompt?.length || 0,
  //   hasEnhancedPrompt: !!message.enhanced_prompt,
  //   enhancedPromptLength: message.enhanced_prompt?.length || 0,
  //   qa_pairs: message.qa_pairs
  // });
  
  try {
    // Determine which parameter to use for the prompt
    const promptToUse = message.enhanced_prompt || message.prompt || "";
    
    //console.log("[DEBUG] Using prompt for API call:", {
    //   source: message.enhanced_prompt ? "enhanced_prompt" : (message.prompt ? "prompt" : "none"),
    //   length: promptToUse.length,
    //   sample: promptToUse.substring(0, 50) + "..."
    // });
    
    // Create request body
    const requestBody = {
      prompt: promptToUse,
      qa_pairs: message.qa_pairs,
    };
    
    //console.log("[API] Request body:", JSON.stringify(requestBody, null, 2));
    
    // Make the request
    const response = await fetch(
      "https://thinkvelocity.in/python-backend-D/refine",
      {
        method: "POST",
        headers: {
          "Authorization": await getAuthorizationKey(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      }
    );
    
    // Log response status
    //console.log("[API] Response status:", response.status, response.statusText);
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // Try to read as text first
    const textData = await response.text();
    //console.log("[API] Response as text:", textData);
    //console.log("[API] Text length:", textData.length);
    
    // If text is empty, handle as special case
    if (!textData || textData.trim() === '') {
      //console.log("[API] Response body is empty - using original prompt as fallback");
      
      // Return the original prompt as the enhanced prompt
      return { 
        success: true, 
        data: {
          enhanced_prompt: message.enhanced_prompt,
          message: "No refinements were made to the prompt."
        }
      };
    }
    
    // Try to parse as JSON if not empty
    try {
      const jsonData = JSON.parse(textData);
      //console.log("[API] Parsed JSON data:", jsonData);
      return { success: true, data: jsonData };
    } catch (jsonError) {
      console.error("[API] JSON parsing error:", jsonError);
      
      // If not valid JSON, treat the text as the enhanced prompt
      return { 
        success: true, 
        data: {
          enhanced_prompt: textData,
          message: "Received text response (not JSON)"
        }
      };
    }
  } catch (error) {
    console.error("[API] Add context error:", error);
    return { success: false, error: error.message };
  }
}

async function getAuthorizationKey() {
  // Option 1: Store in chrome.storage.local (still accessible but not directly visible in code)
  const result = await chrome.storage.local.get(['apiAuthKey']);
  if (result.apiAuthKey) {
    return result.apiAuthKey;
  }
  
  // If not found, fetch from your secure backend or use a fallback
  // This is a temporary fallback that should be removed in production
  return "a1cacd98586a0e974faad626dd85f3f4b4fe120b710686773300f2d8c51d63bf";
}


