/* Quality Indicator Styles */
.velocity-quality-indicator {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  bottom: 12px;
  left: -6px;
  border: 2px solid #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 10000;
}

/* Quality states */
.velocity-quality-idle {
  background-color: #008ACB; /* blue for idle state */
}

.velocity-quality-bad {
  background-color: #ff4d4d; /* Red for bad quality */
}

.velocity-quality-ok {
  background-color: #ffcc00; /* Yellow for ok quality */
}

.velocity-quality-good {
  background-color: #00cc66; /* Green for good quality */
}

/* Pulse animation for active analysis */
@keyframes qualityPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.velocity-quality-analyzing {
  animation: qualityPulse 1.5s infinite ease-in-out;
}

/* Tooltip styles */
.velocity-quality-tooltip {
  position: absolute;
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  top: -30px;
  right: -5px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10001;
}

.velocity-quality-indicator:hover .velocity-quality-tooltip {
  opacity: 1;
}

/* Arrow for tooltip */
.velocity-quality-tooltip:after {
  content: "";
  position: absolute;
  top: 100%;
  right: 10px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}
