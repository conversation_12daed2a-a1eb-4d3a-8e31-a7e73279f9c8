// analysisBox.js

function createProgressIndicator(value, maxValue, color, label, isDarkMode) {
  const currentURL = window.location.href;
  const platformKey = Object.keys(window.platforms || {}).find((key) =>
    window.platforms[key].urlPattern.test(currentURL)
  ) || 'default';

  const getValueColor = (val) => {
    if (val <= 4) return '#EF4444';
    if (val <= 7) return '#EAB308';
    return '#22C55E';
  };

  const dimensions = {
    numberOfDashes: 24,
    radius: 42,
    center: { x: 52, y: 52 },
    viewBox: "0 0 104 104",
    containerWidth: "102px",
    containerHeight: "52px",
    valueSize: "1.5rem",
    labelSize: "1rem"
  };

  let dashes = '';
  for (let i = 0; i < dimensions.numberOfDashes; i++) {
    const angle = (Math.PI * i) / (dimensions.numberOfDashes - 1);
    const x1 = dimensions.center.x + (dimensions.radius - 4) * Math.cos(angle);
    const y1 = dimensions.center.y + (dimensions.radius - 4) * Math.sin(angle);
    const x2 = dimensions.center.x + dimensions.radius * Math.cos(angle);
    const y2 = dimensions.center.y + dimensions.radius * Math.sin(angle);

    const progress = value / maxValue;
    const isActive = i / (dimensions.numberOfDashes - 1) <= progress;

    dashes += `
      <line
        x1="${x1}"
        y1="${y1}"
        x2="${x2}"
        y2="${y2}"
        stroke="${isActive ? getValueColor(value) : (isDarkMode ? '#4b5563' : '#d1d5db')}"
        stroke-width="2"
        stroke-linecap="round"
      />
    `;
  }

  if (platformKey === 'gemini') {
    return `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        width: auto;
        box-sizing: border-box;
        text-align: center;
        position: relative;
        padding: 0 2px;
      " data-metric="${label.toLowerCase()}">
        <div style="
          position: relative;
          width: ${dimensions.containerWidth};
          height: ${dimensions.containerHeight};
          display: flex;
          justify-content: center;
          align-items: center;
        ">
          <svg viewBox="${dimensions.viewBox}" style="
            position: absolute;
            overflow: visible;
            width: 100%;
            height: 100%;
            top: 50%;
            transform: translateY(50%) scale(2) rotate(180deg);
          ">
            ${dashes}
          </svg>
        </div>
        <div style="width: 100%; text-align: center; margin-top: 0.25rem;">
          <span style="
            font-size: ${dimensions.valueSize};
            font-weight: 600;
            color: ${getValueColor(value)};
            cursor: pointer;
            padding: 0;
            margin: 0;
          ">${value}</span>
        </div>
        <span style="
          margin-top: 0.25rem;
          font-size: ${dimensions.labelSize};
          color: ${isDarkMode ? '#9ca3af' : '#6b7280'};
        ">${label}</span>
      </div>
    `;
  }

  return `
    <div style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" data-metric="${label.toLowerCase()}">
      <div style="position: relative; width: ${dimensions.containerWidth}; height: ${dimensions.containerHeight};">
        <svg viewBox="${dimensions.viewBox}" style="position: absolute; transform: rotate(180deg); overflow: visible;">
          ${dashes}
        </svg>
      </div>
      <div style="width: 100%; text-align: center; margin-top: -1.5rem;">
        <span style="font-size: ${dimensions.valueSize}; font-weight: 600; color: ${getValueColor(value)}; cursor: pointer; padding: 0; margin: 0;">${value}</span>
      </div>
      <span style="margin-top: 0.25rem; font-size: ${dimensions.labelSize}; color: ${isDarkMode ? '#9ca3af' : '#6b5563'};">${label}</span>
    </div>
  `;
}

function createCategoryHighlight(category, color, isDarkMode) {
  return `
    <div class="category-highlight" data-category="${category.toLowerCase()}" style="
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: ${color};
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 2;
    "></div>
  `;
}

function getLowestMetricAndRecommendation(data) {
  if (!data || !data.analysis || !data.analysis.metrics || !data.analysis.recommendations) {
    return { lowestMetric: null, lowestRecommendation: null };
  }
  
  const metrics = data.analysis.metrics;
  const metricNames = ["Context", "Action", "Result", "Example"];
  let lowestMetric = { name: metricNames[0], value: parseInt(metrics[metricNames[0]]) || 0 };
  
  metricNames.forEach(name => {
    const value = parseInt(metrics[name]) || 0;
    if (value < lowestMetric.value) {
      lowestMetric = { name, value };
    }
  });
  //console.log("lowestMetric", lowestMetric);
  
  let lowestRecommendation = null;
  if (lowestMetric.name === "Context") {
    lowestRecommendation = data.analysis.recommendations.context_improvement;
  } else if (lowestMetric.name === "Action") {
    lowestRecommendation = data.analysis.recommendations.action_improvement;
  } else if (lowestMetric.name === "Result") {
    lowestRecommendation = data.analysis.recommendations.result_improvement;
  } else if (lowestMetric.name === "Example") {
    lowestRecommendation = data.analysis.recommendations.example_improvement;
  }
  
  return { lowestMetric, lowestRecommendation };
}

function createAnalysisBox() {
  if (typeof window.platformStyles === 'undefined') {
    window.platformStyles = {
      default: {
        boxCssLight: `background: #ffffff; border: 1px solid #e5e7eb; color: #1f2937;`,
        boxCssDark: `background: #1f2937; border: 1px solid #374151; color: #e5e7eb;`,
        closeButtonCssLight: `color: #6b7280;`,
        closeButtonCssDark: `color: #d1d5db;`,
        analysisSectionCssLight: `padding: 0.5rem; background: #f9fafb;`,
        analysisSectionCssDark: `padding: 0.5rem; background: #111827;`,
        sectionTitleCssLight: `color: #1f2937; font-weight: 600;`,
        sectionTitleCssDark: `color: #f3f4f6; font-weight: 600;`,
        recommendationsSectionCssLight: `padding: 0.5rem; background: #f3f4f6;`,
        recommendationsSectionCssDark: `padding: 0.5rem; background: #1f2937;`,
        recommendationTitleCssLight: `color: #374151; font-weight: 600;`,
        recommendationTitleCssDark: `color: #e5e7eb; font-weight: 600;`
      }
    };
    console.warn("platformStyles not found, using default fallback styles.");
  }

  const analysisBox = document.createElement("div");
  analysisBox.classList.add("velocity-analysis-box");

  const baseStyles = `
    position: fixed;
    z-index: 99999;
    cursor: move;
    user-select: none;
    max-width: 416px;
    width: 100%;
    overflow: hidden;
    border-radius: 1.2rem;
    font-family: 'Amenti Black', 'Cosmic Sans', sans-serif;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    will-change: transform;
    padding: 0.5rem;
  `;

  let isBoxVisible = false;

  function applyStyles(isDarkMode) {
    const currentURL = window.location.href;
    const platformKey = Object.keys(window.platforms || {}).find((key) =>
      window.platforms[key].urlPattern.test(currentURL)
    );
    const styles = window.platformStyles[platformKey] || window.platformStyles.default;

    const currentOpacity = analysisBox.style.opacity;
    const currentVisibility = analysisBox.style.visibility;
    const currentPointerEvents = analysisBox.style.pointerEvents;

    analysisBox.style.cssText = `
      ${isBoxVisible ? `opacity: ${currentOpacity};` : 'opacity: 0;'}
      transition: opacity 0.3s;
      ${isBoxVisible ? `pointer-events: ${currentPointerEvents};` : 'pointer-events: none;'}
      ${isBoxVisible ? `visibility: ${currentVisibility};` : 'visibility: hidden;'}
      ${isDarkMode ? styles.boxCssDark : styles.boxCssLight}
      ${baseStyles}
    `;

    const closeButton = analysisBox.querySelector("button");
    if (closeButton) {
      closeButton.style.cssText = `
        position: absolute;
        top: 6px;
        right: 6px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 2px;
        line-height: 1;
        ${isDarkMode ? styles.closeButtonCssDark : styles.closeButtonCssLight}
      `;
    }
  }

  chrome.storage.local.get(["darkMode"], (result) => {
    const isDarkMode = result.darkMode === true;
    applyStyles(isDarkMode);
  });

  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === "local" && "darkMode" in changes) {
      applyStyles(changes.darkMode.newValue);
    }
  });

  let isDragging = false;
  let currentX = window.innerWidth / 2 - 208;
  let currentY = window.innerHeight / 2 - 100;
  let initialX;
  let initialY;
  let xOffset = currentX;
  let yOffset = currentY;

  function dragStart(e) {
    if (e.target.tagName === "BUTTON") return;
    isDragging = true;

    const rect = analysisBox.getBoundingClientRect();
    currentX = rect.left;
    currentY = rect.top;

    initialX = e.type === "touchstart" ? e.touches[0].clientX - currentX : e.clientX - currentX;
    initialY = e.type === "touchstart" ? e.touches[0].clientY - currentY : e.clientY - currentY;
  }

  function drag(e) {
    if (!isDragging) return;
    e.preventDefault();

    if (e.type === "touchmove") {
      currentX = e.touches[0].clientX - initialX;
      currentY = e.touches[0].clientY - initialY;
    } else {
      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;
    }

    xOffset = currentX;
    yOffset = currentY;

    analysisBox.style.transform = `translate(0, 0)`;
    analysisBox.style.left = `${currentX}px`;
    analysisBox.style.top = `${currentY}px`;
  }

  function dragEnd() {
    initialX = currentX;
    initialY = currentY;
    isDragging = false;
  }

  analysisBox.addEventListener("mousedown", dragStart, false);
  analysisBox.addEventListener("touchstart", dragStart, false);
  document.addEventListener("mousemove", drag, false);
  document.addEventListener("touchmove", drag, false);
  document.addEventListener("mouseup", dragEnd, false);
  document.addEventListener("touchend", dragEnd, false);

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === "style" && analysisBox.style.opacity === "1") {
        const rect = analysisBox.getBoundingClientRect();
        currentX = rect.left;
        currentY = rect.top;
      }
    });
  });

  observer.observe(analysisBox, { attributes: true, attributeFilter: ['style'] });

  const closeButton = document.createElement("button");
  closeButton.innerHTML = "×";
  closeButton.setAttribute('aria-label', 'Close analysis window');
  closeButton.setAttribute('tabindex', '0');
  closeButton.addEventListener("click", (event) => {
    event.preventDefault();
    event.stopPropagation();
    hideAnalysisBox();
  });
  closeButton.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' || e.key === 'Space') {
      hideAnalysisBox();
    }
  });
  analysisBox.appendChild(closeButton);

  const hideAnalysisBox = () => {
    isBoxVisible = false;
    analysisBox.style.opacity = "0";
    analysisBox.style.pointerEvents = "none";
    analysisBox.style.visibility = "hidden";
  };

  analysisBox.updateContent = (data) => {
    //console.log("Received data in updateContent:", data);

    const currentURL = window.location.href;
    const platformKey = Object.keys(window.platforms || {}).find((key) =>
      window.platforms[key].urlPattern.test(currentURL)
    );

    while (analysisBox.childNodes.length > 1) {
      analysisBox.removeChild(analysisBox.lastChild);
    }

    chrome.storage.local.get(["darkMode"], (result) => {
      const isDarkMode = result.darkMode === true;
      const styles = window.platformStyles[platformKey] || window.platformStyles.default;

      const analysisData = data.analysis;

      const analysisSection = document.createElement("div");
      analysisSection.style.cssText = isDarkMode ? styles.analysisSectionCssDark : styles.analysisSectionCssLight;
      const analysisTitle = document.createElement("h2");
      analysisTitle.textContent = "Your Prompt Analysis";
      analysisTitle.style.cssText = `
        ${isDarkMode ? styles.sectionTitleCssDark : styles.sectionTitleCssLight}
        margin-top: 1rem;
        padding-top: 0.6rem;
        margin-bottom: 0.2rem;
        font-size: 1.2rem;
        text-align: left;
        padding-left: 0.5rem;
      `;
      analysisSection.appendChild(analysisTitle);

      const metrics = analysisData.metrics || {
        Context: 0,
        Action: 0,
        Result: 0,
        Example: 0
      };

      const indicatorContainer = document.createElement("div");
      indicatorContainer.style.cssText = platformKey === 'gemini' ? `
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        max-width: 406px;
        margin: right;
        position: relative;
        padding-left: 1.1rem;
      ` : `
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        width: 100%;
        max-width: 406px;
        margin: right;
        position: relative;
        padding-left: 1.1rem;
      `;

      const metricData = [
        { value: parseInt(metrics.Context) || 0, label: "Context", color: "#F97316" },
        { value: parseInt(metrics.Action) || 0, label: "Action", color: "#22C55E" },
        { value: parseInt(metrics.Result) || 0, label: "Result", color: "#EF4444" },
        { value: parseInt(metrics.Example) || 0, label: "Example", color: "#EAB308" }
      ];

      let currentSlide = 0;
      const recData = [
        analysisData.recommendations?.context_improvement || "The prompt lacks any background information or situation explanation. Include relevant context.",
        analysisData.recommendations?.action_improvement || "Define a clear action or step required in the prompt.",
        analysisData.recommendations?.result_improvement || "The expected outcome is not defined. Specify what result or outcome is expected.",
        analysisData.recommendations?.example_improvement || "No examples are provided in the prompt. Include relevant examples to clarify."
      ].filter(rec => rec);

      metricData.forEach((metric, index) => {
        const indicator = document.createElement("div");
        indicator.style.cssText = `
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          position: relative;
          transition: transform 0.3s ease;
          z-index: 1;
        `;
        indicator.innerHTML = createProgressIndicator(metric.value, 10, metric.color, metric.label, isDarkMode);
        indicator.innerHTML += createCategoryHighlight(metric.label, metric.color, isDarkMode);

        indicator.addEventListener("click", () => {
          updateSlide(index);
          updateHighlight(metric.label.toLowerCase());
        });

        indicatorContainer.appendChild(indicator);
      });

      analysisSection.appendChild(indicatorContainer);
      analysisBox.appendChild(analysisSection);

      const recommendationsSection = document.createElement("div");
      recommendationsSection.style.cssText = isDarkMode ? styles.recommendationsSectionCssDark : styles.recommendationsSectionCssLight;

      if (!data || typeof metricData !== 'object') {
        console.warn("Invalid data provided to updateContent, using fallback data");
        data = {
          analysis: {
            metrics: {
              Context: 0,
              Action: 0,
              Result: 0,
              Example: 0
            },
            recommendations: {
              context_improvement: "Add more background information to provide necessary context.",
              action_improvement: "Specify a clearer action or request in your prompt.",
              result_improvement: "Define the expected outcome or deliverable more precisely.",
              example_improvement: "Include relevant examples to illustrate what you're looking for."
            }
          }
        };
      } else if (!data.analysis) {
        console.warn("No 'analysis' property in data, treating data as analysis object:", data);
        data = { analysis: data };
      }

      const recommendationsTitle = document.createElement("h3");
      recommendationsTitle.textContent = "Recommendations";
      recommendationsTitle.style.cssText = `
        ${isDarkMode ? styles.recommendationTitleCssDark : styles.recommendationTitleCssLight}
        font-size: 0.9rem;
        margin-bottom: 0.3rem;
        padding-top: 0.2rem;
        text-align: left;
        padding-left: 0.5rem;
      `;
      recommendationsSection.appendChild(recommendationsTitle);

      const paginationContainer = document.createElement("div");
      paginationContainer.style.cssText = `
        position: relative;
        height: 80px;
        overflow: hidden;
      `;

      const slidesContainer = document.createElement("div");
      slidesContainer.style.cssText = `
        display: flex;
        transition: transform 0.3s ease-in-out;
        height: 100%;
      `;

      recData.forEach((text, index) => {
        const slide = document.createElement("div");
        slide.style.cssText = `
          flex: 0 0 100%;
          width: 100%;
          padding: 0 0.4rem;
          box-sizing: border-box;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
        `;

        const textElement = document.createElement("div");
        textElement.style.cssText = `
          font-size: 0.9rem;
          color: ${isDarkMode ? '#d1d5db' : '#4b5563'};
          line-height: 1.3;
          text-align: left;
          max-width: 100%;
          word-wrap: break-word;
          padding: 0 0.5rem;
        `;
        textElement.textContent = text;
        slide.appendChild(textElement);

        slidesContainer.appendChild(slide);
      });

      paginationContainer.appendChild(slidesContainer);

      const dotsContainer = document.createElement("div");
      dotsContainer.style.cssText = `
        display: flex;
        justify-content: center;
        gap: 0.3rem;
        margin-top: 0.3rem;
      `;

      recData.forEach((_, index) => {
        const dot = document.createElement("div");
        dot.style.cssText = `
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: ${index === 0 ? (isDarkMode ? '#60a5fa' : '#2563EB') : (isDarkMode ? '#4b5563' : '#d1d5db')};
          cursor: pointer;
        `;
        dot.addEventListener("click", () => {
          updateSlide(index);
          const categories = ["context", "action", "result", "example"];
          updateHighlight(categories[index]);
        });
        dotsContainer.appendChild(dot);
      });

      let autoRotationInterval;
      const AUTO_ROTATION_DELAY = 1500;

      function startAutoRotation() {
        stopAutoRotation();
        autoRotationInterval = setInterval(() => {
          const nextSlide = (currentSlide + 1) % recData.length;
          updateSlide(nextSlide);
          const categories = ["context", "action", "result", "example"];
          updateHighlight(categories[nextSlide]);
        }, AUTO_ROTATION_DELAY);
      }

      function stopAutoRotation() {
        if (autoRotationInterval) {
          clearInterval(autoRotationInterval);
          autoRotationInterval = null;
        }
      }

      analysisBox.addEventListener('mouseenter', stopAutoRotation);
      analysisBox.addEventListener('mouseleave', startAutoRotation);

      function updateSlide(newIndex) {
        currentSlide = Math.max(0, Math.min(newIndex, recData.length - 1));
        slidesContainer.style.transform = `translateX(-${currentSlide * 100}%)`;

        const dots = dotsContainer.children;
        for (let i = 0; i < dots.length; i++) {
          dots[i].style.background = i === currentSlide ?
            (isDarkMode ? '#60a5fa' : '#2563EB') :
            (isDarkMode ? '#4b5563' : '#d1d5db');
        }
      }

      function updateHighlight(activeCategory) {
        const allMetrics = indicatorContainer.querySelectorAll('[data-metric]');
        allMetrics.forEach(element => {
          const metricContainer = element.closest('div[style*="position: relative"]');
          if (metricContainer) {
            metricContainer.style.transform = 'translateY(0)';
            metricContainer.style.transition = 'transform 0.3s ease, filter 0.3s ease box-shadow 0.3s ease';
            metricContainer.style.zIndex = '1';
            metricContainer.style.filter = 'none';
            metricContainer.style.boxShadow = 'none';
          }
          const valueText = element.querySelector('span[style*="font-size: 1.5rem"]');
          if (valueText) {
            valueText.style.textShadow = 'none';
            valueText.style.transition = 'text-shadow 0.3s ease';
          }
        });

        const highlights = indicatorContainer.querySelectorAll('.category-highlight');
        highlights.forEach(highlight => {
          highlight.style.opacity = "0";
        });

        const activeHighlight = indicatorContainer.querySelector(`.category-highlight[data-category="${activeCategory}"]`);
        if (activeHighlight) {
          activeHighlight.style.opacity = "1";
          const activeMetric = indicatorContainer.querySelector(`[data-metric="${activeCategory}"]`);
          if (activeMetric) {
            const metricContainer = activeMetric.closest('div[style*="position: relative"]');
            if (metricContainer) {
              const valueText = activeMetric.querySelector('span[style*="font-size: 1.5rem"]');
              let glowColor = '#3b82f6';
              if (valueText) {
                const computedStyle = window.getComputedStyle(valueText);
                glowColor = computedStyle.color;
                valueText.style.transition = 'text-shadow 0.3s ease';
              }
              metricContainer.style.transform = 'translateY(-5px)';
              metricContainer.style.boxShadow = `0 0 15px rgba(0, 0, 0, 0.1)`;
              metricContainer.style.transition = 'transform 0.3s ease, filter 0.3s ease, box-shadow 0.3s ease';
              metricContainer.style.zIndex = '3';
            }
          }
        }
      }

      recommendationsSection.appendChild(paginationContainer);
      recommendationsSection.appendChild(dotsContainer);
      analysisBox.appendChild(recommendationsSection);

      startAutoRotation();
      updateSlide(0);
      updateHighlight("context");

      isBoxVisible = true;
      analysisBox.style.opacity = "1";
      analysisBox.style.pointerEvents = "auto";
      analysisBox.style.visibility = "visible";
    });
  };

  window.addEventListener('resize', () => {
    currentX = Math.min(currentX, window.innerWidth - analysisBox.offsetWidth);
    currentY = Math.min(currentY, window.innerHeight - analysisBox.offsetHeight);
    analysisBox.style.left = `${currentX}px`;
    analysisBox.style.top = `${currentY}px`;
  });

  return analysisBox;
}

// Attach to window for global access
window.createAnalysisBox = createAnalysisBox;