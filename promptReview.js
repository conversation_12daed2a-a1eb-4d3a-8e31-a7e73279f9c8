// promptReview.js - Updated with enhanced context suggestions display and fixed refine prompt functionality

function getLowestMetricAndRecommendation(data) {
  // Extract the analysis data from the nested structure
  let analysisData = null;

  if (data && data.analysis) {
    // Direct structure
    analysisData = data.analysis;
  } else if (data && data.data && data.data.analysis) {
    // Nested under data
    analysisData = data.data.analysis;
  } else if (data && data.data && data.data.data && data.data.data.analysis) {
    // Deeply nested
    analysisData = data.data.data.analysis;
  } else if (data && data.metrics && data.recommendations) {
    // Metrics and recommendations directly under data
    analysisData = {
      metrics: data.metrics,
      recommendations: data.recommendations,
      framework_analysis: data.framework_analysis
    };
  } else if (data && data.data && data.data.metrics && data.data.recommendations) {
    // Metrics and recommendations under data.data
    analysisData = {
      metrics: data.data.metrics,
      recommendations: data.data.recommendations,
      framework_analysis: data.data.framework_analysis
    };
  }

  if (!analysisData || !analysisData.metrics) {
    return {
      lowestMetric: null,
      lowestRecommendation: null
    };
  }

  const metrics = analysisData.metrics;
  const metricMapping = {
    Context: {
      key: 'context_improvement',
      defaultRec: "Add more background information and context to your prompt."
    },
    Action: {
      key: 'action_improvement',
      defaultRec: "Include more specific details about what you want to accomplish."
    },
    Result: {
      key: 'result_improvement',
      defaultRec: "Clarify the expected outcome or result you're looking for."
    },
    Example: {
      key: 'example_improvement',
      defaultRec: "Provide examples or specific requirements to make your request clearer."
    }
  };

  const metricValues = Object.entries(metrics).map(([key, value]) => ({
    name: key,
    value: parseInt(value) || 0
  }));

  const lowestMetric = metricValues.reduce((min, current) =>
    current.value < min.value ? current : min,
    metricValues[0]
  );

  if (!lowestMetric) {
    return {
      lowestMetric: null,
      lowestRecommendation: null
    };
  }

  // Check if recommendations exist in the data
  const recommendations = analysisData.recommendations || {};
  const mapping = metricMapping[lowestMetric.name];

  // Use API recommendation if available, otherwise use default
  const lowestRecommendation = recommendations[mapping.key] || mapping.defaultRec;

  return {
    lowestMetric: {
      name: lowestMetric.name,
      value: lowestMetric.value
    },
    lowestRecommendation: lowestRecommendation
  };
}

function createCircleProgressIndicator(value, maxValue, color, label, isDarkMode) {
  const getValueColor = (val) => {
    if (val <= 4) return '#EF4444'; // Red for low scores
    if (val <= 7) return '#EAB308'; // Yellow for medium scores
    return '#22C55E'; // Green for high scores
  };

  // Use value-based color for all metrics
  const valueColor = getValueColor(value);

  // Calculate the circle properties
  const circleRadius = 18;
  const circumference = 2 * Math.PI * circleRadius;
  const progressOffset = circumference - (value / maxValue) * circumference;
  const center = { x: 22, y: 22 };

  // Background stroke color based on dark mode
  const bgStrokeColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)';
  // Text color for label based on dark mode
  const labelColor = isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)';

  return `
    <div class="metric-indicator" data-metric="${label.toLowerCase()}" style="
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
      min-width: 60px;
      padding: 8px;
      border-radius: 8px;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    ">
      <div style="
        position: relative;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <svg viewBox="0 0 44 44" style="
          position: absolute;
          width: 100%;
          height: 100%;
        ">
          <!-- Background circle -->
          <circle
            cx="${center.x}"
            cy="${center.y}"
            r="${circleRadius}"
            stroke="${bgStrokeColor}"
            stroke-width="4"
            fill="none"
          />
          <!-- Progress circle -->
          <circle
            cx="${center.x}"
            cy="${center.y}"
            r="${circleRadius}"
            stroke="${valueColor}"
            stroke-width="4"
            fill="none"
            stroke-dasharray="${circumference}"
            stroke-dashoffset="${progressOffset}"
            transform="rotate(-90, ${center.x}, ${center.y})"
          />
        </svg>
        <span style="
          position: relative;
          font-size: 16px;
          font-weight: 600;
          color: ${valueColor};
        ">${value}</span>
      </div>
      <span style="
        font-size: 12px;
        margin-top: 4px;
        color: ${labelColor};
      ">${label}</span>
    </div>
  `;
}

function createCategoryHighlight(category, color, isDarkMode) {
  return `
    <div class="category-highlight" data-category="${category.toLowerCase()}" style="
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: ${color};
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 2;
    "></div>
  `;
}

function createAnalysisUI() {
  const container = document.createElement("div");
  container.className = "analysis-ui-container";

  // Colors based on the provided color palette
  const colors = {
    mainLight: 'hsl(190, 100%, 47%)',       // Light blue - main color
    mainDark: 'hsl(190, 100%, 37%)',         // Darker blue for dark mode
    backgroundLight: 'hsl(190, 95%, 90%)',   // Very light blue background
    backgroundDark: 'hsl(197, 40%, 14%)',    // Dark blue-gray background
    foregroundLight: '#0B0B0B',              // Almost black
    foregroundDark: '#ffffff'                // White
  };

  container.style.cssText = `
    width: 100%;
    max-width: 350px;
    background: white;
    height: fit-content;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-family: system-ui, -apple-system, sans-serif;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 1000000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  `;

  // Store context and dark mode state
  let isDarkMode = false;
  const contextObj = {
    qa_pairs: [],
    enhanced_prompt: '',
  };
  //console.log("[DEBUG] contextObj initialized:", contextObj);

  // Add a helper function to track changes to contextObj
  function updateContextObj(property, value, source) {
    //console.log(`[DEBUG] Updating contextObj.${property} from ${source}:`, {
    //   oldValue: contextObj[property]?.substring ? contextObj[property].substring(0, 30) + "..." : contextObj[property],
    //   newValue: value?.substring ? value.substring(0, 30) + "..." : value
    // });
    contextObj[property] = value;
  }

  function updateDarkModeStyles() {
    chrome.storage.local.get(["darkMode"], (result) => {
      isDarkMode = result.darkMode === true;

      // Apply appropriate colors based on dark/light mode using the theme colors from the image
      const mainColor = isDarkMode ? '#0c3545' : '#e8f4f8';
      const bgColor = isDarkMode ? '#1a2327' : '#ffffff';
      const foregroundColor = isDarkMode ? '#ffffff' : '#333333';
      const borderColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
      const textPrimaryColor = isDarkMode ? '#ffffff' : '#333333';
      const textSecondaryColor = isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)';
      const inputBgColor = isDarkMode ? '#2a363c' : '#ffffff';
      const tabsBgColor = isDarkMode ? '#2a363c' : '#e8f4f8';
      const tabActiveTextColor = isDarkMode ? '#ffffff' : '#333333';
      const tabInactiveTextColor = isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)';
      const inputBorderColor = isDarkMode ? '#3a464c' : '#e0e0e0';

      // Apply new styles
      container.style.background = bgColor;
      container.style.color = textPrimaryColor;
      container.style.border = isDarkMode ? 'none' : `1px solid ${borderColor}`;

      headerBar.style.background = bgColor;
      headerTitle.style.color = textPrimaryColor;
      closeButton.style.color = textSecondaryColor;
      tabContainer.style.background = tabsBgColor;

      // Update tab styling based on active tab
      if (activeTab === "elaborate") {
        elaborateTab.style.background = isDarkMode ? '#1a2327' : '#ffffff';
        elaborateTab.style.color = tabActiveTextColor;
        analysisTab.style.background = 'transparent';
        analysisTab.style.color = tabInactiveTextColor;
      } else {
        analysisTab.style.background = isDarkMode ? '#1a2327' : '#ffffff';
        analysisTab.style.color = tabActiveTextColor;
        elaborateTab.style.background = 'transparent';
        elaborateTab.style.color = tabInactiveTextColor;
      }

      // Update content areas
      metricsSection.style.background = bgColor;
      if (overallStrength) {
        overallStrength.style.color = '#EAB308'; // Keep consistent amber color for strength label
      }
      if (strengthLabel) {
        strengthLabel.style.color = isDarkMode ? '#ffffff' : '#111827'; // Update strength label color based on dark mode
      }

      // Update combined container if it exists
      if (combinedContainer) {
        combinedContainer.style.border = `1px solid ${borderColor}`;
      }
      if (recommendationsContainer) {
        recommendationsContainer.style.borderTop = `1px solid ${borderColor}`;
        recommendationsContainer.style.background = isDarkMode ? 'rgba(255, 255, 255, 0)' : 'rgba(0, 0, 0, 0.03)';
      }

      // Update input areas
      inputSections.querySelectorAll('div').forEach(section => {
        if (section.labelElement) {
          section.labelElement.style.color = textPrimaryColor;
        }
        if (section.inputElement) {
          section.inputElement.style.color = textPrimaryColor;
          section.inputElement.style.background = inputBgColor;
          section.inputElement.style.borderColor = inputBorderColor;
        }
      });

      // Update recommendations area
      if (recommendationsTitle) {
        recommendationsTitle.style.color = textPrimaryColor;
      }
      if (recommendationsContent) {
        recommendationsContent.style.background = bgColor;
        recommendationsContent.style.color = textSecondaryColor;
      }

      // If we have current data, refresh the UI
      if (container.currentData) {
        container.updateAnalysis(container.currentData);
      }
    });
  }

  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === "local" && "darkMode" in changes) {
      updateDarkModeStyles();
    }
  });

  const headerBar = document.createElement("div");
  headerBar.style.cssText = `
    padding: 10px 16px;
    background: white;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  `;

  const headerTitle = document.createElement("div");
  headerTitle.textContent = "Improve Accuracy";
  headerTitle.style.cssText = `
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  `;

  const closeButton = document.createElement("button");
  closeButton.innerHTML = "×";
  closeButton.style.cssText = `
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    line-height: 1;
  `;
  closeButton.addEventListener("click", (e) => {
    e.preventDefault();
    window.togglePromptReviewBox(false);

    // Track UI closing analytics
    if (typeof sendGAEvent === 'function') {
      sendGAEvent('prompt_review', 'close', 'analysis_ui', 1);
    }
  });

  headerBar.appendChild(headerTitle);
  headerBar.appendChild(closeButton);
  container.appendChild(headerBar);

  const tabContainer = document.createElement("div");
  tabContainer.style.cssText = `
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f3f4f6;
    padding: 4px;
    border-radius: 25px;
    margin: 6px 16px;
    gap: 4px;
  `;

  let activeTab = "elaborate";

  const elaborateTab = document.createElement("div");
  elaborateTab.textContent = "Elaborate";
  elaborateTab.style.cssText = `
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  `;

  const analysisTab = document.createElement("div");
  analysisTab.textContent = "Analysis";
  analysisTab.style.cssText = `
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
  `;

  const contentContainer = document.createElement("div");
  contentContainer.style.cssText = `
    height: auto;
    min-height: 200px;
    position: relative;
    overflow: visible;
  `;

  elaborateTab.addEventListener("click", () => {
    if (activeTab !== "elaborate") {
      if (typeof sendGAEvent === 'function') {
        sendGAEvent('prompt_review', 'tab_change', 'elaborate', 1);
      }
      activeTab = "elaborate";
      if (isDarkMode) {
        elaborateTab.style.background = "#ffffff";
        elaborateTab.style.color = "#000000";
      } else {
        elaborateTab.style.background = "#ffffff";
        elaborateTab.style.color = "#000000";
      }
      analysisTab.style.background = "transparent";
      analysisTab.style.color = isDarkMode ? "#9ca3af" : "#6b7280";

      inputSections.style.display = "flex";
      metricsSection.style.display = "none";

      setTimeout(() => ensureScrollingEnabled(container), 50);
    }
  });

  analysisTab.addEventListener("click", () => {
    if (activeTab !== "analysis") {
      if (typeof sendGAEvent === 'function') {
        sendGAEvent('prompt_review', 'tab_change', 'analysis', 1);
      }
      activeTab = "analysis";
      if (isDarkMode) {
        analysisTab.style.background = "#ffffff";
        analysisTab.style.color = "#000000";
      } else {
        analysisTab.style.background = "#ffffff";
        analysisTab.style.color = "#000000";
      }
      elaborateTab.style.background = "transparent";
      elaborateTab.style.color = isDarkMode ? "#9ca3af" : "#6b7280";

      inputSections.style.display = "none";
      metricsSection.style.display = "block";

      setTimeout(() => ensureScrollingEnabled(container), 50);
    }
  });

  tabContainer.appendChild(elaborateTab);
  tabContainer.appendChild(analysisTab);
  container.appendChild(tabContainer);

  makeDraggable(container);

  // Create analysis section with new UI design
  const metricsSection = document.createElement("div");
  metricsSection.style.cssText = `
    display: none;
    padding: 0 16px 16px 16px;
    background: white;
    height: auto;
    box-sizing: border-box;
    overflow: auto;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  `;

  // Add overall strength indicator
  const strengthContainer = document.createElement("div");
  strengthContainer.style.cssText = `
    display: flex;
    align-items: center;
    margin: 12px 4px;
  `;

  const strengthLabel = document.createElement("div");
  strengthLabel.textContent = "Overall Strength:";
  strengthLabel.style.cssText = `
    font-size: 14px;
    font-weight: 500;
  `;

  const overallStrength = document.createElement("div");
  overallStrength.textContent = "Getting There";
  overallStrength.style.cssText = `
    margin-left: 6px;
    font-size: 16px;
    font-weight: 500;
    color: #EAB308;
  `;

  strengthContainer.appendChild(strengthLabel);
  strengthContainer.appendChild(overallStrength);
  metricsSection.appendChild(strengthContainer);

  // Create the metrics indicator container with circles instead of dashes
  const metricsIndicatorContainer = document.createElement("div");
  metricsIndicatorContainer.style.cssText = `
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 5px;
    width: 100%;
    position: relative;
    border-radius: 8px;
    background: transparent;
  `;

  // Changed recommendation section to be combined in one larger div
  const combinedContainer = document.createElement("div");
  combinedContainer.style.cssText = `
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
  `;

  // Add metrics section to the combined container
  combinedContainer.appendChild(metricsIndicatorContainer);

  // Create recommendation container that appears below metrics
  const recommendationsContainer = document.createElement("div");
  recommendationsContainer.style.cssText = `
    padding: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  `;

  const metricHeader = document.createElement("div");
  metricHeader.style.cssText = `
    display: flex;
    align-items: center;
    margin:8px 0px;
  `;

  const metricIcon = document.createElement("div");
  metricIcon.innerHTML = `
    <img
      id="metric-icon-img"
      src="chrome-extension://${chrome.runtime.id}/assets/clarity.png"
      width="12"
      height="12"
      alt="Metric icon"
    />
  `;
  metricIcon.style.cssText = `
    width: 20px;
    height: 20px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  const recommendationsTitle = document.createElement("div");
  recommendationsTitle.textContent = "Clarity";
  recommendationsTitle.style.cssText = `
    font-size: 14px;
    font-weight: 600;
    color: #111827;
  `;

  metricHeader.appendChild(metricIcon);
  metricHeader.appendChild(recommendationsTitle);

  const recommendationsContent = document.createElement("div");
  recommendationsContent.style.cssText = `
    font-size: 13px;
    color: #4b5563;
    line-height: 1.5;
  `;

  recommendationsContainer.appendChild(metricHeader);
  recommendationsContainer.appendChild(recommendationsContent);

  // Add recommendation section to the combined container
  combinedContainer.appendChild(recommendationsContainer);

  // Add the combined container to the metrics section
  metricsSection.appendChild(combinedContainer);

  const inputSections = document.createElement("div");
  inputSections.style.cssText = `
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 16px;
    padding-bottom: 16px;
    display: flex;
    flex-direction: column;
    color: #00547B;
    height: auto;
    box-sizing: border-box;
    overflow: visible;
  `;

  contentContainer.appendChild(metricsSection);
  contentContainer.appendChild(inputSections);
  container.appendChild(contentContainer);

  container.updateAnalysis = (data) => {
    container.currentData = data;

    // Clear the metrics container
    while (metricsIndicatorContainer.firstChild) {
      metricsIndicatorContainer.removeChild(metricsIndicatorContainer.firstChild);
    }

    // Extract the analysis data from the nested structure
    let analysisData = null;

    if (data && data.analysis) {
      // Direct structure
      analysisData = data.analysis;
    } else if (data && data.data && data.data.analysis) {
      // Nested under data
      analysisData = data.data.analysis;
    } else if (data && data.data && data.data.data && data.data.data.analysis) {
      // Deeply nested
      analysisData = data.data.data.analysis;
    } else if (data && data.metrics && data.recommendations) {
      // Metrics and recommendations directly under data
      analysisData = {
        metrics: data.metrics,
        recommendations: data.recommendations,
        framework_analysis: data.framework_analysis
      };
    } else if (data && data.data && data.data.metrics && data.data.recommendations) {
      // Metrics and recommendations under data.data
      analysisData = {
        metrics: data.data.metrics,
        recommendations: data.data.recommendations,
        framework_analysis: data.data.framework_analysis
      };
    }

    //console.log("[Velocity] Extracted analysis data:", analysisData);

    // Log the metrics and recommendations for debugging
    if (analysisData && analysisData.metrics) {
      //console.log("[Velocity] Metrics:", analysisData.metrics);
      //console.log("[Velocity] Recommendations:", analysisData.recommendations);
      if (analysisData.framework_analysis) {
        //console.log("[Velocity] Framework Analysis:", analysisData.framework_analysis);
      }
    }

    if (!analysisData || !analysisData.metrics) {
      return {
        lowestMetric: null,
        lowestRecommendation: null
      };
    }

    // Check if recommendations exist in the data
    const hasRecommendations = analysisData && analysisData.recommendations &&
      (analysisData.recommendations.context_improvement ||
       analysisData.recommendations.action_improvement ||
       analysisData.recommendations.result_improvement ||
       analysisData.recommendations.example_improvement);

    // Update recommendation content if recommendations exist
    if (hasRecommendations) {
      const recommendations = analysisData.recommendations;
      const recText = recommendations.context_improvement ||
                      recommendations.action_improvement ||
                      recommendations.result_improvement ||
                      recommendations.example_improvement ||
                      "";

      recommendationsContent.textContent = recText;
    } else {
      // Default to empty if no recommendations
      recommendationsContent.textContent = "";
    }

    // Update overall strength based on average scores
    if (analysisData && analysisData.metrics) {
      const metrics = analysisData.metrics;
      const values = Object.values(metrics).map(v => parseInt(v) || 0);
      const avg = values.reduce((sum, val) => sum + val, 0) / values.length;

      // Alternatively, use the average_score if available
      const avgScore = analysisData.average_score || avg;

      let strengthText = "Needs Work";
      if (avgScore >= 8) strengthText = "Well-Written";
      else if (avgScore >= 6) strengthText = "Getting There";
      else if (avgScore >= 4) strengthText = "Needs Improvement";

      overallStrength.textContent = strengthText;

      // Set color based on strength text
      if (strengthText === "Needs Improvement") {
        overallStrength.style.color = "#EF4444"; // Red
      } else if (strengthText === "Getting There") {
        overallStrength.style.color = "#F59E0B"; // Amber/yellow
      } else if (strengthText === "Well-Written") {
        overallStrength.style.color = "#22C55E"; // Green
      } else {
        overallStrength.style.color = "#EF4444"; // Default to red for low scores
      }
    }

    if (analysisData && analysisData.metrics) {
      const metrics = analysisData.metrics;

      // Function to get color based on value
      const getValueColor = (val) => {
        if (val <= 4) return '#EF4444'; // Red for low scores
        if (val <= 7) return '#EAB308'; // Yellow for medium scores
        return '#22C55E'; // Green for high scores
      };

      // Default recommendation texts based on metric
      const defaultRecommendations = {
        "Clarity": "Add more background information and context to your prompt.",
        "Depth": "Include more specific details about what you want to accomplish.",
        "Impact": "Clarify the expected outcome or result you're looking for.",
        "Specificity": "Provide examples or specific requirements to make your request clearer."
      };

      // Update metrics mapping to use value-based colors
      const metricData = [
        { value: parseInt(metrics.Context) || 0, label: "Clarity", color: getValueColor(parseInt(metrics.Context) || 0) },
        { value: parseInt(metrics.Action) || 0, label: "Depth", color: getValueColor(parseInt(metrics.Action) || 0) },
        { value: parseInt(metrics.Result) || 0, label: "Impact", color: getValueColor(parseInt(metrics.Result) || 0) },
        { value: parseInt(metrics.Example) || 0, label: "Specificity", color: getValueColor(parseInt(metrics.Example) || 0) }
      ];

      metricData.forEach((metric, index) => {
        // Create circle progress indicator for each metric
        const indicator = document.createElement("div");
        indicator.style.cssText = `
          flex: 1;
          display: flex;
          padding: 4px;
          flex-direction: column;
          align-items: center;
          position: relative;
          z-index: 1;
        `;
        indicator.innerHTML = createCircleProgressIndicator(metric.value, 10, metric.color, metric.label, isDarkMode);

        // Get the metric indicator element for border handling
        const metricElement = indicator.querySelector('.metric-indicator');

        indicator.addEventListener("click", () => {
          if (typeof sendGAEvent === 'function') {
            sendGAEvent('prompt_review', 'metric_click', metric.label, metric.value);
          }

          // Update recommendation title and content based on clicked metric
          recommendationsTitle.textContent = metric.label;

          // Update the icon image to match the metric
          metricIcon.querySelector('#metric-icon-img').setAttribute('src', `chrome-extension://${chrome.runtime.id}/assets/${metric.label.toLowerCase()}.png`);

          // Check if we have recommendations from the API
          let recommendationText = "";

          if (hasRecommendations) {
            const recommendations = analysisData.recommendations;

            if (metric.label === "Clarity") {
              recommendationText = recommendations.context_improvement || defaultRecommendations[metric.label];
            } else if (metric.label === "Depth") {
              recommendationText = recommendations.action_improvement || defaultRecommendations[metric.label];
            } else if (metric.label === "Impact") {
              recommendationText = recommendations.result_improvement || defaultRecommendations[metric.label];
            } else if (metric.label === "Specificity") {
              recommendationText = recommendations.example_improvement || defaultRecommendations[metric.label];
            }
          } else {
            // Use default recommendations if none from API
            recommendationText = defaultRecommendations[metric.label] || "";
          }

          recommendationsContent.textContent = recommendationText;

          // Remove selected border from all metrics
          metricsIndicatorContainer.querySelectorAll('.metric-indicator').forEach(element => {
            element.style.border = 'none';
            element.style.boxShadow = 'none';
          });

          // Add selected border to clicked metric
          if (metricElement) {
            metricElement.style.border = `2px solid ${metric.color}`;
            metricElement.style.boxShadow = `0 0 8px ${metric.color}40`;
          }
        });

        metricsIndicatorContainer.appendChild(indicator);
      });
    }

    setTimeout(() => ensureScrollingEnabled(container), 50);
  };

  container.updateEnhancedPrompt = (enhancedPromptData) => {
    while (inputSections.firstChild) {
      inputSections.removeChild(inputSections.firstChild);
    }
    //console.log("tttttttttttttttttttttttttttttt",enhancedPromptData);

    // Extract and log questions from the enhanced prompt if available
    if (enhancedPromptData && enhancedPromptData.questions) {
      // Try to get the enhanced prompt text from various possible locations in the response
      let enhancedPromptText = "";

      //console.log("[Velocity] Processing enhanced prompt questions:", enhancedPromptData.questions);
      if (Array.isArray(enhancedPromptData.questions)) {
        // Clear existing input sections
        while (inputSections.firstChild) {
          inputSections.removeChild(inputSections.firstChild);
        }

        // Create input fields for each question (limit to 2)
        const questionsToShow = enhancedPromptData.questions.slice(0, 2);
        questionsToShow.forEach(question => {
          const inputField = createInputField(question, "Write your answer here");
          inputSections.appendChild(inputField);
        });


        // Store the questions for later use
        contextObj.qa_pairs = questionsToShow.map(question => ({ question, answer: '' }));
        //console.log("Qa Pairs", contextObj.qa_pairs);
        updateContextObj('enhanced_prompt', enhancedPromptData.enhanced_prompt, 'enhancedPromptData');        contextObj.suggestions = questionsToShow;
        contextObj.conversationId = enhancedPromptData.conversationId;

        // Don't return early - continue with the rest of the function
        // so the button gets added by the existing code
        questionsProcessed = true;

      }
      // if (typeof enhancedPromptData.data === 'string') {
      //   enhancedPromptText = enhancedPromptData.data;
      // }

      if (enhancedPromptText) {
        // Look for question patterns in the text
        const questionPatterns = [
          /\b([A-Z][^.!?]*\?)/g,                 // Sentences ending with question mark
          /\b(what|who|when|where|why|how|which|can|could|would|should|is|are|do|does|did)[^.!?]*\?/gi, // Common question starters
          /\b(please (provide|specify|explain|describe|clarify)[^.!?]*\??)/gi, // Request patterns
          /\b(I need to know[^.!?]*\??)/gi       // Information request patterns
        ];

        const questions = [];

        // Apply each pattern and collect unique questions
        questionPatterns.forEach(pattern => {
          const matches = enhancedPromptText.match(pattern);
          if (matches) {
            matches.forEach(match => {
              // Clean up the question and add if not already included
              const cleanQuestion = match.trim();
              if (cleanQuestion.length > 10 && !questions.includes(cleanQuestion)) {
                questions.push(cleanQuestion);
              }
            });
          }
        });

        // If no questions found with patterns, try to extract key points
        if (questions.length === 0) {
          // Split by newlines and look for bullet points or numbered items
          const lines = enhancedPromptText.split('\n');
          lines.forEach(line => {
            const trimmedLine = line.trim();
            // Check for bullet points, numbers, or other list markers
            if (/^(\*|\-|\d+\.|•)/.test(trimmedLine) && trimmedLine.length > 10) {
              // Remove the bullet/number and add to questions
              const cleanLine = trimmedLine.replace(/^(\*|\-|\d+\.|•)\s*/, '');
              questions.push(cleanLine);
            }
          });
        }

        // Log the extracted questions
        //console.log("[Velocity DEBUG] Questions extracted from EnhancePromptV2 response:", questions);

        // Use the extracted questions if available (limit to 2 questions)
        if (questions.length > 0) {
          // Store the extracted questions as context suggestions
          const enhancedPromptData = questions.slice(0, 2);
          contextObj.suggestions = enhancedPromptData;
          contextObj.conversationId = enhancedPromptData.conversationId;

          // Create input fields for each extracted question
          extractedSuggestions.forEach((question) => {
            const inputField = createInputField(question, "Write your answer here");
            inputSections.appendChild(inputField);
          });

          // Return early since we've already added the input fields
          return;
        }
      }
    }

    // Default questions are defined here but used only when needed
    // These can be used as fallback if no questions are available from the API

    // if (enhancedPromptData && enhancedPromptData.contextSuggestions && enhancedPromptData.contextSuggestions.length) {
    //   const suggestionsToUse = enhancedPromptData.slice(0, 2);
    //   contextObj.suggestions = suggestionsToUse;
    //   contextObj.conversationId = enhancedPromptData.conversationId;

    //   suggestionsToUse.forEach((suggestion) => {
    //     const inputField = createInputField(suggestion, "Write your answer here");
    //     inputSections.appendChild(inputField);
    //   });
    // } else {
    //   defaultPrompts.forEach((prompt) => {
    //     const inputField = createInputField(prompt.label, prompt.placeholder);
    //     inputSections.appendChild(inputField);
    //   });
    // }


    // Add the button after all inputs
    const buttonContainer = document.createElement("div");
    buttonContainer.style.cssText = `
      display: flex;
      justify-content: center;
      margin-top: 16px;
      position: relative;
    `;

    const button = document.createElement("button");
    button.innerHTML = `
      <img
        src="chrome-extension://${chrome.runtime.id}/assets/refineprompt.png"
        alt="Refine icon"
        width="18"
        height="18"
        style="margin-right: 8px;"
      />
      Refine Prompt
    `;
    button.style.cssText = `
      background: #00c2ff;
      color: white;
      padding: 10px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    button.addEventListener("mouseover", () => {
      button.style.backgroundColor = '#00b0e6';
    });

    button.addEventListener("mouseout", () => {
      button.style.backgroundColor = "#00c2ff";
    });

    button.addEventListener("click", async (e) => {
      e.preventDefault();

      // Initialize qa_pairs if it doesn't exist
      if (!contextObj.qa_pairs) {
        contextObj.qa_pairs = [];
      }

      // Update qa_pairs with the current answers
      let questionInputs = container.querySelectorAll('input[type="text"], textarea');
      questionInputs.forEach((input, index) => {
        if (contextObj.qa_pairs[index]) {
          contextObj.qa_pairs[index].answer = input.value.trim();
        }
      });

      // Track analytics for refine prompt button click with GA4
      if (typeof sendGAEvent === 'function') {
        sendGAEvent('prompt_review', 'refine_click', 'refine_prompt', 1);
      }

      //console.log("[Velocity] Refine Prompt button clicked - starting refine process");

      // Count answered questions for Mixpanel tracking
      let answeredQuestions = [];
      let inputs = inputSections.querySelectorAll('input');
      inputs.forEach(input => {
        if (input.value && input.value.trim() !== '') {
          answeredQuestions.push(input.value.trim());
        }
      });

      //console.log("Answered Questions", answeredQuestions);
      if (answeredQuestions.length > 0) {
        // Take up to 2 answered questions
        const answersToUse = answeredQuestions.slice(0, 2);

        // Update qa_pairs with these answers
        answersToUse.forEach((answer, index) => {
          if (contextObj.qa_pairs[index]) {
            contextObj.qa_pairs[index].answer = answer;
          }
        });

        // Keep only the qa_pairs that have answers
        contextObj.qa_pairs = contextObj.qa_pairs.slice(0, answersToUse.length);
      } else {
        // If no answers, clear qa_pairs but still proceed with enhancement
        //console.log("[Velocity] No context answers provided, will still enhance prompt");
        contextObj.qa_pairs = [];
      }

      // Track Refine Prompt Clicked event with Mixpanel
      // Platform information is available but not currently used

      // chrome.runtime.sendMessage({
      //   action: "trackMixpanelEvent",
      //   eventName: "Refine Prompt Clicked",
      //   properties: {
      //     questions_answered: answeredQuestions.length,
      //     platform: currentPlatform
      //   }
      // });

      // Increment interaction count
      if (window.promptReviewState) {
        window.promptReviewState.interactionCount = (window.promptReviewState.interactionCount || 0) + 1;
      }

      container.style.opacity = "0";
      container.style.pointerEvents = "none";

      const originalData = container.currentData;
      if (!originalData?.originalPrompt) {
        alert("No original prompt data available.");
        container.style.opacity = "1";
        container.style.pointerEvents = "auto";
        return;
      }

      // Get the button and start loading animation
      const velocityButton = document.querySelector(".custom-injected-button button");
      if (velocityButton) {
        // First update prompt review status to ensure messages are hidden
        if (window.updatePromptReviewStatus) {
          window.updatePromptReviewStatus(false);
        }

        // Start loading animation
        if (window.velocityWrapperState && window.velocityWrapperState.buttonSystem) {
          // For new system
          window.velocityWrapperState.buttonSystem.stateMachine.transition('loading', { button: velocityButton });
        } else if (velocityButton.classList) {
          // For older systems, manually add animation class
          velocityButton.classList.add('velocity-loading-animation');
        }
      }

      // const contextValues = [];
      // // const inputs = inputSections.querySelectorAll('input');

      // inputs.forEach((input) => {
      //   if (input.value.trim()) {
      //     contextValues.push(input.value.trim());
      //   }
      // });

      // const context = contextValues.join("\n\n");
      // //console.log("[Velocity] Collected context values:", contextValues.length);

      button.textContent = "Enhancing...";
      button.disabled = true;

      // Keep track of whether we've already transitioned to success state
      let successStateActivated = false;

      // Function to ensure loading animation is stopped and success state is activated
      const ensureSuccessState = () => {
        if (successStateActivated) return; // Only do this once

        const velocityButton = document.querySelector(".custom-injected-button button");
        if (!velocityButton) return;

        // Make sure the loading animation is stopped immediately
        velocityButton.classList.remove('velocity-loading-animation');

        if (window.velocityWrapperState && window.velocityWrapperState.buttonSystem) {
          // For new system
          window.velocityWrapperState.buttonSystem.cleanupLoadingAnimation(velocityButton);
          window.velocityWrapperState.buttonSystem.handleSuccessState(velocityButton, false);
          //console.log('[Velocity] Loading animation stopped and success state activated');
        } else if (window.markPromptSuccess) {
          // For older systems
          window.markPromptSuccess(false);
        }

        successStateActivated = true;
        //console.log('[Velocity] Success state activated and button animation stopped');
      };

      try {
        // Platform and style information is available for future use
        const hasConversationId = contextObj.conversationId !== null;
        //console.log("[Velocity] QA pairs for context:", contextObj.qa_pairs);
        let enhanceResponse;


        // if (hasConversationId) {
          //console.log("[DEBUG] Before addContext call:", {
          //   hasConversationId,
          //   conversationId: contextObj.conversationId,
          //   enhancedPromptLength: contextObj.enhanced_prompt?.length || 0,
          //   enhancedPromptSample: contextObj.enhanced_prompt?.substring(0, 50) + "...",
          //   qa_pairs: contextObj.qa_pairs
          // });

          // Check if we have any qa_pairs with answers
          const hasAnswers = contextObj.qa_pairs && contextObj.qa_pairs.length > 0 &&
                            contextObj.qa_pairs.some(pair => pair.answer && pair.answer.trim() !== '');

          if (hasAnswers) {
            // If we have answers, use addContext API
            enhanceResponse = await new Promise(resolve => chrome.runtime.sendMessage({
              action: "addContext",
              prompt: contextObj.enhanced_prompt,
              qa_pairs: contextObj.qa_pairs,
            }, resolve));
            //console.log("[Velocity] addContext API response received:", enhanceResponse.success);
            //console.log("[Velocity] addContext API response data:", enhanceResponse.data);
          } else {
            // If no answers, use the enhanced prompt directly
            //console.log("[Velocity] No context answers provided, using enhanced prompt directly");
            enhanceResponse = {
              success: true,
              data: {
                refined_prompt: contextObj.enhanced_prompt || originalData.originalPrompt
              }
            };
          }
        // } else {
        //   const refinedPrompt = `${originalData.originalPrompt}\n\n${context}`;
        //   //console.log("[Velocity] Using enhancePrompt API for refine (no conversationId)");
        //   enhanceResponse = await new Promise(resolve => chrome.runtime.sendMessage({
        //     action: "enhancePrompt",
        //     prompt: refinedPrompt,
        //     style: selectedStyle,
        //     platform: platform
        //   }, resolve));
        //   //console.log("[Velocity] enhancePrompt API response received:", enhanceResponse.success);
        //   //console.log("[Velocity] enhancePrompt API response data:", enhanceResponse.data);
        // }

        if (!enhanceResponse || !enhanceResponse.success) {
          throw new Error(enhanceResponse?.error || "Failed to enhance prompt");
        }


        const enhancedPromptContent = enhanceResponse.data.refined_prompt
          ? enhanceResponse.data.refined_prompt
          : `${enhanceResponse.data.originalPrompt}\n\n${context}`;

        //console.log("[Velocity] Enhanced prompt generated, lengthhhhhh:", enhancedPromptContent);

        // //console.log("[Velocity] Enhanced prompt generated, length:", refinedPrompt.length);


        const analysisResponse = await new Promise(resolve => chrome.runtime.sendMessage({
          action: "promptAnalysis",
          prompt: enhancedPromptContent,
          // style: selectedStyle,
          // platform: platform
        }, resolve));
        //console.log("22222222222222222222222222222222222222222222222222222222222222222222222222222")


        //console.log("[Velocity] Analysis response received:", analysisResponse.success);
        //console.log("[Velocity] Analysis response data:", analysisResponse.data);

        if (!analysisResponse || !analysisResponse.success) {
          throw new Error(analysisResponse?.error || "Failed to analyze enhanced prompt");
        }

        // Make sure we're accessing the correct structure
        const analysisData = analysisResponse.data;
        //console.log("[Velocity] Analysis metrics:", analysisData.analysis?.metrics);

        // Update the UI with the analysis data
        if (analysisData) {
          //console.log("[Velocity DEBUG] Analysis data available:", {
          //   hasMetrics: !!analysisData.analysis?.metrics,
          //   metrics: analysisData.analysis?.metrics ? JSON.stringify(analysisData.analysis.metrics) : "No metrics",
          //   avgScore: analysisData.analysis?.average_score || "No average score"
          // });

          // Check if updateAnalysisUI function exists on window
          try {
            if (window['updateAnalysisUI'] && typeof window['updateAnalysisUI'] === 'function') {
              window['updateAnalysisUI'](analysisData);
            } else {
              //console.log("[Velocity] updateAnalysisUI function not available, skipping UI update");
            }
          } catch (err) {
            // console.error("[Velocity] Error updating analysis UI:", err);
          }
        }

        const currentURL = window.location.href;
        const platformKey = Object.keys(window.platforms || {}).find(key =>
          window.platforms[key].urlPattern.test(currentURL)
        );

        if (platformKey && window.platforms[platformKey].textAreaSelector) {
          const chatInputBox = document.querySelector(window.platforms[platformKey].textAreaSelector);

          //console.log("[Velocity] Will inject enhanced prompt into UI, length:", enhancedPromptContent);

          if (chatInputBox) {
            // First stop loading animation before setting the content
            if (velocityButton) {
              // Use our helper function to ensure success state
              ensureSuccessState();
            }

            //console.log("[Velocity] Injecting enhanced prompt into chat input, platform:", platformKey);

            // Make sure the highlight styles are injected
            injectEnhancedHighlightStyles();

            // Try to get the input box from velocityWrapperState first (most reliable)
            const inputBox = window.velocityWrapperState?.inputBox || chatInputBox;

            // Now set the enhanced prompt in the input
            if (inputBox.tagName === "TEXTAREA") {
              inputBox.value = enhancedPromptContent;
            } else if (inputBox.hasAttribute("contenteditable")) {
              inputBox.innerText = enhancedPromptContent;
            } else {
              inputBox.textContent = enhancedPromptContent;
            }

            // Dispatch input event to trigger UI updates
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
            //console.log("[Velocity] Enhanced prompt successfully injected into UI element");

            // Immediately ensure success state after updating input
            ensureSuccessState();

            // Apply the highlight and scale effects
            inputBox.classList.add("velocity-enhanced-highlight", "velocity-enhanced-scale");

            // After animation completes, remove highlight classes
            setTimeout(() => {
              inputBox.classList.remove("velocity-enhanced-highlight", "velocity-enhanced-scale");

            }, 1000);
          } else {
            // Try to use injectPromptIntoInputField function if available
            if (typeof injectPromptIntoInputField === 'function') {
              injectPromptIntoInputField(enhancedPromptContent);
            }
          }
        }

        // Track Prompt Refinement Success event with Mixpanel
        // Platform information is available for analytics but not currently used

        // //console.log("[Velocity] Sending Mixpanel event with enhanced prompt length:", enhancedPromptContent.length);
        // chrome.runtime.sendMessage({
        //   action: "trackMixpanelEvent",
        //   eventName: "Prompt Refinement Success",
        //   properties: {
        //     original_length: originalData.originalPrompt.length,
        //     refined_length: enhancedPromptContent.length,
        //     platform: currentPlatform
        //   }
        // });

        // Handle UI state after successful enhancement
        container.style.opacity = "0";
        container.style.pointerEvents = "none";

        setTimeout(() => {
          container.remove();
          //console.log("[Velocity] Prompt enhancement complete");
        }, 300);

        // Track successful enhancement
        // if (typeof sendGAEvent === 'function') {
        //   sendGAEvent('prompt_review', 'enhance_success', platform, 1);
        //   // Track context values count
        //   sendGAEvent('prompt_review', 'context_values', 'count', contextValues.length);
        // }
      } catch (error) {
        const errorMessage = error.message || "Unknown error occurred";
        // console.error("[Velocity] Error during prompt refinement:", error);

        const currentPlatform = originalData && originalData.platform ? originalData.platform : "unknown";
        let refinementStage = "unknown";

        // Try to determine which stage the error occurred in
        if (errorMessage.includes("addContext")) {
          refinementStage = "context_addition";
        } else if (errorMessage.includes("enhance")) {
          refinementStage = "enhancement";
        } else if (errorMessage.includes("analysis")) {
          refinementStage = "analysis";
        }

        chrome.runtime.sendMessage({
          action: "trackMixpanelEvent",
          eventName: "Prompt Refinement Error",
          properties: {
            error_message: errorMessage,
            stage: refinementStage,
            platform: currentPlatform
          }
        });

        // Even if there's an error, we should still try to enhance the prompt with original content
        try {
          // Get the input box and inject the original prompt
          const currentURL = window.location.href;
          const platformKey = Object.keys(window.platforms || {}).find(key =>
            window.platforms[key].urlPattern.test(currentURL)
          );

          if (platformKey && window.platforms[platformKey].textAreaSelector) {
            const chatInputBox = document.querySelector(window.platforms[platformKey].textAreaSelector);
            if (chatInputBox) {
              // Use original prompt if we have it
              const promptToUse = originalData.originalPrompt || "";

              // Try to get the input box from velocityWrapperState first (most reliable)
              const inputBox = window.velocityWrapperState?.inputBox || chatInputBox;

              if (inputBox.tagName === "TEXTAREA") {
                inputBox.value = promptToUse;
              } else if (inputBox.hasAttribute("contenteditable")) {
                inputBox.innerText = promptToUse;
              } else {
                inputBox.textContent = promptToUse;
              }

              // Dispatch input event to trigger UI updates
              inputBox.dispatchEvent(new Event('input', { bubbles: true }));
              //console.log("[Velocity] Original prompt injected as fallback after error");
            }
          }
        } catch (fallbackError) {
          // console.error("[Velocity] Error during fallback prompt injection:", fallbackError);
        }

        // Always ensure the success state is activated to stop button animation
        ensureSuccessState();

        alert(`Failed to refine and analyze the prompt: ${errorMessage}. Please try again.`);
        container.style.opacity = "1";
        container.style.pointerEvents = "auto";
      } finally {
        // Always ensure the button animation is stopped
        ensureSuccessState();

        // Reset the refine button
        button.innerHTML = `
          <img
            src="chrome-extension://${chrome.runtime.id}/assets/refineprompt.png"
            alt="Refine icon"
            width="18"
            height="18"
            style="margin-right: 8px;"
          />
          Refine Prompt
        `;
        button.disabled = false;

        //console.log("[Velocity] Prompt enhancement process completed");
      }
    });

    buttonContainer.appendChild(button);
    inputSections.appendChild(buttonContainer);

    // Update dark mode styling for inputs
    function updateInputStyles() {
      const isDarkMode = document.body.classList.contains('dark-mode') || container.isDarkMode;
      inputSections.querySelectorAll('div').forEach(section => {
        if (section.labelElement) {
          section.labelElement.style.color = isDarkMode ? '#ffffff' : '#333333';
        }
        if (section.inputElement) {
          section.inputElement.style.background = isDarkMode ? '#2a363c' : '#ffffff';
          section.inputElement.style.color = isDarkMode ? '#ffffff' : '#333333';
          section.inputElement.style.borderColor = isDarkMode ? '#3a464c' : '#e0e0e0';
        }
      });
    }

    // Call it once initially
    updateInputStyles();

    setTimeout(() => ensureScrollingEnabled(container), 50);
  };

  function createInputField(label, placeholder) {
    const container = document.createElement("div");
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      margin: 8px 4px;
    `;

    const labelElement = document.createElement("div");
    labelElement.textContent = label;
    labelElement.style.cssText = `
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #111827;
    `;

    const input = document.createElement("input");
    input.placeholder = placeholder;
    input.style.cssText = `
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 10px 12px;
      font-size: 14px;
      background: #ffffff;
      color: #333333;
      width: 100%;
      box-sizing: border-box;
    `;

    container.appendChild(labelElement);
    container.appendChild(input);

    // Store references for dark mode updates
    container.labelElement = labelElement;
    container.inputElement = input;

    return container;
  }

  updateDarkModeStyles();

  // For promptReview.js - Position indicator in top right
  const positionIndicator = document.createElement("div");
  positionIndicator.className = "position-indicator";
  positionIndicator.style.cssText = `
    position: absolute;
    top: -30px;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1000001;
  `;
  container.appendChild(positionIndicator);

  return container;
}

/**
 * Get the correct position for UI elements based on the button's anchor position
 * @param {Element} button - The button element
 * @returns {Object} - Object with position details: {side: 'left'|'right', verticalAlign: 'top'|'bottom'}
 */
function getPositionFromButtonAnchor(button) {
  // Find button container for position reference
  const buttonContainer = button.closest('.velocity-button-container');
  if (!buttonContainer) {
    // Default to right side if no container found
    return { side: 'right', verticalAlign: 'top' };
  }

  // Get anchor position from button container
  const anchorPosition = buttonContainer.dataset.anchorPosition || 'top-right';

  // Determine side based on anchor position
  const side = anchorPosition.includes('left') ? 'left' : 'right';
  const verticalAlign = anchorPosition.includes('top') ? 'top' : 'bottom';

  return { side, verticalAlign };
}

function positionUIRelativeToInputBox(uiElement) {
  // Call the new positioning function
  positionPromptReviewBox(uiElement);
}

// Add the new positioning functions
function positionPromptReviewBox(promptReviewBox) {
  // Check if wrapper state and button exist
  if (!window.velocityWrapperState || !window.velocityWrapperState.wrapper || !window.velocityWrapperState.button) {
    // console.error("[Velocity] Cannot position prompt review box: wrapper or button not found");
    // Default positioning in the middle of the screen
    promptReviewBox.style.position = "fixed";
    promptReviewBox.style.top = "50%";
    promptReviewBox.style.left = "50%";
    promptReviewBox.style.transform = "translate(-50%, -50%)";
    return;
  }

  const wrapper = window.velocityWrapperState.wrapper;
  const button = window.velocityWrapperState.button;

  // Get wrapper position
  const wrapperRect = wrapper.getBoundingClientRect();

  // Get button anchor position (left/right, top/bottom)
  const buttonAnchorPosition = button.dataset.anchorPosition || 'top-right';
  const isButtonOnLeft = buttonAnchorPosition.includes('left');
  const isButtonOnTop = buttonAnchorPosition.includes('top');

  // Calculate prompt review box dimensions
  const promptReviewWidth = 350; // Standard width from existing code
  const promptReviewHeight = Math.min(600, window.innerHeight - 40); // Standard height calculation

  // Calculate horizontal position based on button position
  let left;
  if (isButtonOnLeft) {
    // If button is on the left, place prompt review box to the left of the wrapper
    left = wrapperRect.left - promptReviewWidth - 20;
    // If not enough space on the left, place it to the right
    if (left < 10) {
      left = wrapperRect.right + 20;
    }
  } else {
    // If button is on the right, place prompt review box to the right of the wrapper
    left = wrapperRect.right + 20;
    // If not enough space on the right, place it to the left
    if (left + promptReviewWidth > window.innerWidth - 10) {
      left = wrapperRect.left - promptReviewWidth - 20;
    }
  }

  // If still not enough space horizontally, center it
  if (left < 10 || left + promptReviewWidth > window.innerWidth - 10) {
    left = Math.max(10, Math.min(window.innerWidth - promptReviewWidth - 10,
                             (window.innerWidth - promptReviewWidth) / 2));
  }

  // Calculate vertical position based on button position
  let top;
  if (isButtonOnTop) {
    // If button is on top, align with the top of the wrapper
    top = wrapperRect.top;
  } else {
    // If button is on bottom, align with the bottom of the wrapper minus prompt height
    top = wrapperRect.bottom - promptReviewHeight;
  }

  // Ensure it stays within the viewport vertically
  top = Math.max(10, Math.min(window.innerHeight - promptReviewHeight - 10, top));

  // Apply the positioning
  promptReviewBox.style.position = "fixed";
  promptReviewBox.style.top = `${top}px`;
  promptReviewBox.style.left = `${left}px`;
  promptReviewBox.style.width = `${promptReviewWidth}px`;
  promptReviewBox.style.maxHeight = `${promptReviewHeight}px`;
  promptReviewBox.style.transform = "none"; // Reset any transform

  // Log positioning for debugging
  //console.log(`[Velocity] Positioned prompt review box at (${left}, ${top}), button on ${isButtonOnLeft ? 'left' : 'right'}`);

  // Ensure the prompt review box is visible even after positioning
  promptReviewBox.style.opacity = "1";
  promptReviewBox.style.pointerEvents = "auto";
  promptReviewBox.style.visibility = "visible";
}

// Add helper function to check viewport visibility
function isInViewport(element) {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

function ensureScrollingEnabled(uiElement) {
  const metricsSection = uiElement.querySelector('div[style*="background: #171717"]') ||
                          uiElement.querySelector('div[style*="background: white"]');

  if (metricsSection) {
    metricsSection.style.overflow = "visible";
    metricsSection.style.height = "auto";
  }

  const recommendationsContent = uiElement.querySelector('div[style*="line-height: 1.5"]');
  if (recommendationsContent) {
    recommendationsContent.style.overflow = "visible";
    recommendationsContent.style.height = "auto";
  }

  const formSection = uiElement.querySelector('div[style*="gap: 12px"]');
  if (formSection) {
    formSection.style.overflow = "visible";
  }

  // Update the content container height based on content
  const contentContainer = uiElement.querySelector('div[style*="position: relative"]');
  if (contentContainer) {
    const activeSection = uiElement.querySelector('div[style*="display: block"]') ||
                           uiElement.querySelector('div[style*="display: flex"]');

    if (activeSection) {
      contentContainer.style.height = "auto";
    }
  }
}

function makeDraggable(container) {
  // Note: dragElement parameter was unused and has been removed
  let isDragging = false;
  let initialX;
  let initialY;
  let currentX = 0;
  let currentY = 0;

  const headerBar = container.querySelector("div[style*='cursor: move']");
  if (!headerBar) return;

  headerBar.addEventListener("mousedown", dragStart, false);
  document.addEventListener("mousemove", drag, false);
  document.addEventListener("mouseup", dragEnd, false);

  function dragStart(e) {
    if (e.target.tagName === "BUTTON" || e.target.tagName === "INPUT") return;

    initialX = e.clientX - container.offsetLeft;
    initialY = e.clientY - container.offsetTop;

    isDragging = true;
    headerBar.style.cursor = "grabbing";
    e.preventDefault();
  }

  function drag(e) {
    if (!isDragging) return;
    e.preventDefault();

    currentX = e.clientX - initialX;
    currentY = e.clientY - initialY;

    const maxX = window.innerWidth - container.offsetWidth;
    const maxY = window.innerHeight - container.offsetHeight;

    currentX = Math.max(0, Math.min(currentX, maxX));
    currentY = Math.max(0, Math.min(currentY, maxY));

    container.style.left = `${currentX}px`;
    container.style.top = `${currentY}px`;
    container.style.right = "auto";
    container.style.bottom = "auto";
    container.style.width = "350px";
    container.style.transform = "none";
  }

  function dragEnd() {
    isDragging = false;
    headerBar.style.cursor = "move";
  }
}

function updateMessageBoxPosition(msgBox, button) {
  if (!msgBox || !button) return;

  const rect = button.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) return;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const messageWidth = 220;

  const styleProps = {
    backgroundColor: msgBox.style.backgroundColor,
    color: msgBox.style.color,
    border: msgBox.style.border
  };

  // Check if this is a prompt review message
  const isPromptReview = msgBox.dataset && msgBox.dataset.state === 'promptReview';

  // Common CSS properties for all positions
  const commonCss = `
    background-color: ${styleProps.backgroundColor || '#e0f2fe'};
    color: ${styleProps.color || '#0369a1'};
    border: ${styleProps.border || '1px solid #bae6fd'};
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    max-width: ${messageWidth}px;
    position: fixed;
    z-index: 999999;
    pointer-events: none;
    transform: translateZ(0);
  `;

  // Get button position from its container
  let buttonPosition = 'right';
  const buttonContainer = button.closest('.velocity-button-container');
  if (buttonContainer && buttonContainer.dataset.anchorPosition) {
    const anchorPosition = buttonContainer.dataset.anchorPosition;
    buttonPosition = anchorPosition.includes('left') ? 'left' : 'right';
  }

  // For prompt review messages, use the button position for consistency
  if (isPromptReview) {
    if (buttonPosition === 'left' && rect.left > messageWidth + 20) {
      // Left side positioning for left-anchored buttons
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.left - messageWidth - 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;
    } else {
      // Right side positioning for right-anchored buttons
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.right + 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;

      // Make sure it doesn't go off-screen to the right
      if (rect.right + messageWidth + 20 >= viewportWidth) {
        msgBox.style.cssText = `
          ${commonCss}
          left: ${rect.left - messageWidth - 15}px;
          top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
        `;
      }
    }
    return; // Exit early for prompt review messages
  }

  // Position based on button anchor position and available space
  if (buttonPosition === 'left') {
    // Left-anchored button - prefer showing message on the left
    if (rect.left > messageWidth + 20) {
      // Left side positioning
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.left - messageWidth - 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;
    } else if (rect.right + messageWidth + 20 < viewportWidth) {
      // Right side positioning (fallback if not enough space on left)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.right + 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;
    } else if (rect.top > 80) {
      // Top positioning (second fallback)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
        top: ${rect.top - 60}px;
      `;
    } else {
      // Bottom positioning (last fallback)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
        top: ${rect.bottom + 10}px;
      `;
    }
  } else {
    // Right-anchored button - prefer showing message on the right
    if (rect.right + messageWidth + 20 < viewportWidth) {
      // Right side positioning
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.right + 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;
    } else if (rect.left > messageWidth + 20) {
      // Left side positioning (fallback if not enough space on right)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${rect.left - messageWidth - 15}px;
        top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
      `;
    } else if (rect.top > 80) {
      // Top positioning (second fallback)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
        top: ${rect.top - 60}px;
      `;
    } else {
      // Bottom positioning (last fallback)
      msgBox.style.cssText = `
        ${commonCss}
        left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
        top: ${rect.bottom + 10}px;
      `;
    }
  }
}

function initAnalysisUI(data) {
  const existingUI = document.querySelector(".analysis-ui-container");
  if (existingUI && existingUI.parentNode) {
    existingUI.parentNode.removeChild(existingUI);
  }

  const ui = createAnalysisUI();
  document.body.appendChild(ui);

  // Use the new positioning function
  positionPromptReviewBox(ui);

  const updatePosition = () => {
    if (ui.style.opacity === "1") {
      positionPromptReviewBox(ui);
    }
  };

  // Store event listeners for cleanup
  ui._positionEventListeners = ui._positionEventListeners || [];

  // Clean up existing listeners before adding new ones
  if (ui._positionEventListeners.length > 0) {
    ui._positionEventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    ui._positionEventListeners = [];
  }

  // Add new listeners
  const resizeHandler = () => updatePosition();
  const scrollHandler = () => updatePosition();

  window.addEventListener('resize', resizeHandler, { passive: true });
  window.addEventListener('scroll', scrollHandler, { passive: true });

  ui._positionEventListeners.push(
    { element: window, event: 'resize', handler: resizeHandler },
    { element: window, event: 'scroll', handler: scrollHandler }
  );

  // Variable to track timeout for cleanup
  let retryTimeoutId = null;

  ui.show = (analysisData, enhancedPromptData) => {
    try {
      //console.log("[Velocity DEBUG] show method called with:", {
        // hasAnalysisData: !!analysisData,
        // hasAnalysisObject: !!analysisData?.analysis,
        // hasMetrics: !!analysisData?.analysis?.metrics,
      //   hasEnhancedPromptData: !!enhancedPromptData
      // });

      // Check if context metric is high enough to skip showing the review box
      if (analysisData && shouldSkipPromptReview(analysisData)) {
        //console.log("[Velocity] Skipping prompt review box in show method - context score is above threshold");
        // Send analytics event that review was skipped due to high context score
        if (typeof sendGAEvent === 'function') {
          sendGAEvent('prompt_review', 'skip', 'high_context_score', 1);
        }
        return;
      }

      // Track UI display analytics
      if (typeof sendGAEvent === 'function') {
        sendGAEvent('prompt_review', 'show', 'analysis_ui', 1);

        // Track metrics if available
        if (analysisData && analysisData.analysis && analysisData.analysis.metrics) {
          const metrics = analysisData.analysis.metrics;
          sendGAEvent('prompt_review', 'context_score', 'metric', parseInt(metrics.Context) || 0);
          sendGAEvent('prompt_review', 'action_score', 'metric', parseInt(metrics.Action) || 0);
          sendGAEvent('prompt_review', 'result_score', 'metric', parseInt(metrics.Result) || 0);
          sendGAEvent('prompt_review', 'example_score', 'metric', parseInt(metrics.Example) || 0);
        }
      }

      // Function to handle missing analysis data
      const handleMissingAnalysisData = () => {
        //console.log("[Velocity] Analysis data is missing or incomplete");

        // Instead of retrying, we'll just continue with what we have
        // This ensures the UI still works even without complete analysis data
        return true;
      };

      // If analysis data is missing, handle it and continue
      if (!analysisData || !analysisData.analysis) {
        handleMissingAnalysisData();
        // We'll still proceed with the UI update even without analysis data
      }

      ui.updateAnalysis(analysisData || data);
      if (enhancedPromptData) {
        ui.updateEnhancedPrompt(enhancedPromptData);
      }
      ui.style.opacity = "1";
      ui.style.pointerEvents = "auto";
      ui.style.display = "flex";
      ui.style.visibility = "visible";
      positionPromptReviewBox(ui);

      // Update prompt review status
      if (window.updatePromptReviewStatus) {
        window.updatePromptReviewStatus(true);
      }

      // Find the button element but don't create message box - all messages are hidden
      const currentURL = window.location.href;
      const platformKey = Object.keys(window.platforms || {}).find((key) =>
        window.platforms[key].urlPattern.test(currentURL)
      );

      if (platformKey) {
        const button = document.querySelector(".custom-injected-button button");
        if (button) {
          // Remove all message boxes
          if (window.velocityAnimations && window.velocityAnimations.removeAllMessageBoxes) {
            window.velocityAnimations.removeAllMessageBoxes();
          } else if (window.velocityAnimations && window.velocityAnimations.removeMessageBox) {
            window.velocityAnimations.removeMessageBox();
          }
        }
      }

      // Update SVG review status safely with a try-catch
      try {
        if (window.updateSvgPromptReviewStatus) {
          window.updateSvgPromptReviewStatus(true);
        }
      } catch (err) {
        // console.error("Error updating SVG prompt review status:", err);
      }

      // Always use togglePromptReviewBox instead of direct style changes
      window.togglePromptReviewBox(true, analysisData);
    } catch (error) {
      // console.error("Error showing analysis UI:", error);
    }
  };

  if (data) {
    ui.updateAnalysis(data);
  }

  // Add a custom close event for when UI is hidden
  ui.addEventListener = (eventName, handler) => {
    ui._eventHandlers = ui._eventHandlers || {};
    ui._eventHandlers[eventName] = ui._eventHandlers[eventName] || [];
    ui._eventHandlers[eventName].push(handler);
  };

  ui.dispatchEvent = (eventName, data) => {
    if (!ui._eventHandlers || !ui._eventHandlers[eventName]) return;
    ui._eventHandlers[eventName].forEach(handler => handler(data));
  };

  // Modify the cleanup method to include new event listeners and input monitor
  ui.cleanup = () => {
    if (retryTimeoutId) {
      clearTimeout(retryTimeoutId);
      retryTimeoutId = null;
    }

    if (ui._messagePositionTracker) {
      clearInterval(ui._messagePositionTracker);
      ui._messagePositionTracker = null;
    }

    if (ui._positionEventListeners) {
      ui._positionEventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      ui._positionEventListeners = [];
    }

    // Clean up input monitor
    if (window._promptReviewInputMonitor) {
      clearInterval(window._promptReviewInputMonitor);
      window._promptReviewInputMonitor = null;
    }
  };

  return ui;
}

window.positionPromptReviewBox = positionPromptReviewBox;
window.initAnalysisUI = initAnalysisUI;

// Add this function to monitor input content and hide prompt review box if empty
function ********************************() {
  const inputBox = window.velocityWrapperState?.inputBox;
  if (!inputBox) return;

  // Clear any existing monitors
  if (window._promptReviewInputMonitor) {
    clearInterval(window._promptReviewInputMonitor);
  }

  // Set up a new monitor
  window._promptReviewInputMonitor = setInterval(() => {
    // Only check if prompt review box is visible
    const promptReviewBox = document.querySelector(".analysis-ui-container");
    if (!promptReviewBox || promptReviewBox.style.opacity !== "1") return;

    let inputContent = "";
    if (inputBox.tagName === "TEXTAREA") {
      inputContent = inputBox.value.trim();
    } else if (inputBox.hasAttribute("contenteditable")) {
      inputContent = inputBox.innerText.trim();
    } else {
      inputContent = inputBox.textContent.trim();
    }

    // If input is empty and prompt review box is showing, hide it
    if (!inputContent && window.togglePromptReviewBox) {
      //console.log("[Velocity] Input box empty, hiding prompt review box");
      window.togglePromptReviewBox(false);
    }
  }, 1000); // Check every second
}

// Function to show the analysis UI with data
window.showAnalysisUI = function(data) {
  try {
    // Check if context metric is high enough to skip showing the review box
    if (data && shouldSkipPromptReview(data)) {
      //console.log("[Velocity] Skipping prompt review box in showAnalysisUI - context score is above threshold");
      // Send analytics event that review was skipped due to high context score
      if (typeof sendGAEvent === 'function') {
        sendGAEvent('prompt_review', 'skip', 'high_context_score', 1);
      }
      return;
    }

    const ui = initAnalysisUI();
    if (!ui) {
      throw new Error("Failed to initialize analysis UI");
    }

    // Use the toggle function
    window.togglePromptReviewBox(true, data);

    // Set up input monitoring after showing UI
    ********************************();

    // Update prompt review status
    if (window.updatePromptReviewStatus) {
      window.updatePromptReviewStatus(true);
    }

    // Find the button element but don't create message box - all messages are hidden
    const currentURL = window.location.href;
    const platformKey = Object.keys(window.platforms || {}).find((key) =>
      window.platforms[key].urlPattern.test(currentURL)
    );

    if (platformKey) {
      const button = document.querySelector(".custom-injected-button button");
      if (button) {
        // Remove all message boxes
        if (window.velocityAnimations && window.velocityAnimations.removeAllMessageBoxes) {
          window.velocityAnimations.removeAllMessageBoxes();
        } else if (window.velocityAnimations && window.velocityAnimations.removeMessageBox) {
          window.velocityAnimations.removeMessageBox();
        }
      }
    }

    // Update SVG review status safely with a try-catch
    try {
      if (window.updateSvgPromptReviewStatus) {
        window.updateSvgPromptReviewStatus(true);
      }
    } catch (err) {
      // console.error("Error updating SVG prompt review status:", err);
    }
  } catch (error) {
    // console.error("Error showing analysis UI:", error);
  }
};

// Add function to inject the enhanced highlight animation styles
function injectEnhancedHighlightStyles() {
  if (document.getElementById('velocity-highlight-styles')) return;

  const styleEl = document.createElement('style');
  styleEl.id = 'velocity-highlight-styles';
  styleEl.innerHTML = `
    @keyframes velocity-enhanced-highlight {
      0% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
      30% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      70% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      100% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
    }

    @keyframes velocity-enhanced-scale {
      0% { transform: scale(1); }
      30% { transform: scale(1.03); }
      70% { transform: scale(1.03); }
      100% { transform: scale(1); }
    }

    .velocity-enhanced-highlight {
      animation: velocity-enhanced-highlight 1s ease-in-out forwards;
      border-color: #0088cb !important;
      transition: all 0.3s ease;
    }

    .velocity-enhanced-scale {
      animation: velocity-enhanced-scale 1s ease-in-out forwards;
    }

    .text-pop-effect {
      animation: text-pop 0.3s ease-in-out;
    }

    @keyframes text-pop {
      0% { transform: scale(1); }
      50% { transform: scale(1.02); }
      100% { transform: scale(1); }
    }
  `;

  document.head.appendChild(styleEl);
}

// Add function to handle toggling visibility of the prompt review box
window.togglePromptReviewBox = function(shouldShow, data = null) {
  //console.log("[Velocity DEBUG] togglePromptReviewBox called with:", {
  //   shouldShow,
  //   hasData: !!data,
  //   dataStructure: data ? Object.keys(data).join(", ") : "none"
  // });

  // Check if context metric is high enough to skip showing the review box
  if (shouldShow && data && shouldSkipPromptReview(data)) {
    //console.log("[Velocity DEBUG] Skipping prompt review box - context score is above threshold");
    // Notify the button system that we're not showing the review
    if (window.updatePromptReviewStatus) {
      window.updatePromptReviewStatus(false);
    }
    return;
  }

  // Try to find existing UI
  const existingUI = document.querySelector(".analysis-ui-container");
  //console.log("[Velocity DEBUG] Existing UI found:", !!existingUI);

  if (!existingUI) {
    if (shouldShow && data) {
      // If there's no UI but we want to show it and have data, create and show it
      //console.log("[Velocity DEBUG] Creating new analysis UI");
      const ui = window.initAnalysisUI(data);

      // Force visibility
      ui.style.opacity = "1";
      ui.style.pointerEvents = "auto";
      ui.style.display = "flex";
      ui.style.visibility = "visible";

      //console.log("[Velocity DEBUG] New UI created with styles:", {
      //   opacity: ui.style.opacity,
      //   pointerEvents: ui.style.pointerEvents,
      //   display: ui.style.display,
      //   visibility: ui.style.visibility
      // });

      ui.show(data);

      // Set up input monitoring after showing UI
      ********************************();

      // Hide any animation message boxes when prompt review is shown
      const messageBoxes = document.querySelectorAll('.velocity-message-box');
      messageBoxes.forEach(box => {
        box.style.display = 'none';
      });

      // Notify the button system about prompt review visibility
      if (window.updatePromptReviewStatus) {
        window.updatePromptReviewStatus(true);
      }
    }
    return;
  }

  // If we have an existing UI element
  if (shouldShow) {
    //console.log("[Velocity DEBUG] Showing existing UI");
    // Show the UI with new data if provided
    if (data) {
      existingUI.updateAnalysis(data);
    }

    // Force visibility
    existingUI.style.opacity = "1";
    existingUI.style.pointerEvents = "auto";
    existingUI.style.display = "flex";
    existingUI.style.visibility = "visible";

    //console.log("[Velocity DEBUG] Updated existing UI styles:", {
    //   opacity: existingUI.style.opacity,
    //   pointerEvents: existingUI.style.pointerEvents,
    //   display: existingUI.style.display,
    //   visibility: existingUI.style.visibility
    // });

    // Position it correctly
    if (window.positionPromptReviewBox) {
      window.positionPromptReviewBox(existingUI);
    }

    // Hide any animation message boxes
    const messageBoxes = document.querySelectorAll('.velocity-message-box');
    messageBoxes.forEach(box => {
      box.style.display = 'none';
    });

    // Notify the button system about prompt review visibility
    if (window.updatePromptReviewStatus) {
      window.updatePromptReviewStatus(true);
    }
  } else {
    //console.log("[Velocity DEBUG] Hiding existing UI");
    // Hide the UI
    existingUI.style.opacity = "0";
    existingUI.style.pointerEvents = "none";

    // Wait for animation to complete before fully hiding
    setTimeout(() => {
      existingUI.style.display = "none";
      existingUI.style.visibility = "hidden";

      // Notify the button system about prompt review visibility
      if (window.updatePromptReviewStatus) {
        window.updatePromptReviewStatus(false);
      }
    }, 300);

    // Clear the input monitor as it's no longer needed
    if (window._promptReviewInputMonitor) {
      clearInterval(window._promptReviewInputMonitor);
      window._promptReviewInputMonitor = null;
    }
  }
};

// Helper function to check if prompt review should be skipped based on context score
function shouldSkipPromptReview(data) {
  if (!data || !data.analysis || !data.analysis.metrics) return false;

  const contextScore = parseInt(data.analysis.metrics.Context) || 0;
  return contextScore > 7; // Skip if context score is greater than 7
}

// Export a function to check if prompt review box is visible
window.isPromptReviewBoxVisible = function() {
  const existingUI = document.querySelector(".analysis-ui-container");
  return existingUI && existingUI.style.opacity !== "0";
}































