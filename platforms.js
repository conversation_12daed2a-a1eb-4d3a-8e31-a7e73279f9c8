window.platforms = {
  chatgpt: {
    urlPattern: /^https:\/\/chatgpt\.com/,
    textAreaSelector: 'ProseMirror',
    suggestionSelector: "div[class='relative flex w-full flex-auto flex-col']",
    // Define anchor positions for ChatGPT
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "14%", right: "17%" },
      defaultAnchor: "topRight" // The default anchor to use for this platform
    }
  },
  claude: {
    urlPattern: /^https:\/\/claude\.ai/,
    textAreaSelector: '.ProseMirror, div[contenteditable="true"]',
    suggestionSelector: 'div[class="max-h-96 w-full overflow-y-auto break-words min-h-[3rem]"]',
    // Define anchor positions for <PERSON>
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "28%", right: "14%" },
      defaultAnchor: "bottomRight" // Claude works better with bottom-right positioning
    }
  },
  gemini: {
    urlPattern: /^https:\/\/gemini\.google\.com/,
    textAreaSelector: 'div[class="text-input-field_textarea-wrapper ng-tns-c2516172363-3"]',
    suggestionSelector: "div[class='text-input-field_textarea-wrapper ng-tns-c1711834494-3']",
    // Define anchor positions for Gemini
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "2%", right: "11%" },
      defaultAnchor: "bottomRight" // Gemini works better with bottom-right positioning
    }
  },
  grok: {
    urlPattern: /^https:\/\/grok\.com/,
    textAreaSelector: 'div[class="w-full px-2 @[480px]/input:px-3 bg-transparent focus:outline-none text-fg-primary align-bottom min-h-14 pt-5 my-0 mb-5"]',
    suggestionSelector: "div[class='relative z-10']",
    // Define anchor positions for Grok
    anchorPositions: {
      topLeft: { top: "2%", left: "2%" },
      topRight: { top: "2%", right: "2%" },
      bottomLeft: { bottom: "10%", left: "2%" },
      bottomRight: { bottom: "30%", right: "21%" },
      defaultAnchor: "topRight" // Default anchor for Grok
    }
  },
  perplexity: {
    urlPattern: /^https:\/\/(?:www\.)?perplexity\.ai/,
    textAreaSelector: "div[class='bg-background w-full outline-none focus:outline-none focus:ring-borderMain font-sans flex items-center text-textMain placeholder-textOff border focus:ring-1 dark:bg-offsetDark dark:text-textMainDark dark:placeholder-textOffDark selection:bg-superDuper selection:text-textMain duration-75 transition-all border-borderMain dark:border-textMain/10 shadow-sm dark:shadow-md shadow-textMain/5 dark:shadow-black/10 rounded-3xl px-md pt-3 pb-3 grid items-center']",
    suggestionSelector: "div[class*='col-start-1 col-end-4 pb-sm overflow-hidden relative flex h-full w-full']",
    // Define anchor positions for Perplexity
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "13%", right: "34%" },
      defaultAnchor: "bottomRight" // Perplexity works better with bottom-right positioning
    }
  },
  // Updated platform: Bolt
  bolt: {
    urlPattern: /^https:\/\/(www\.)?bolt\.new/,
    textAreaSelector: "div[contenteditable='true'], div[role='textbox'], div.ProseMirror",
    suggestionSelector: "div[class='relative select-none']",
    // Define anchor positions for Bolt
    anchorPositions: {
      topLeft: { top: "10%", left: "6%" },
      topRight: { top: "10%", right: "5%" },
      bottomLeft: { bottom: "15%", left: "5%" },
      bottomRight: { bottom: "28%", right: "14%" },
      defaultAnchor: "topRight" // Default anchor for Bolt
    }
  },
  vercelv0: {
    urlPattern: /^https:\/\/(www\.)?v0\.dev/,
    textAreaSelector: "div[class='flex flex-col flex-1 overflow-y-auto']",
    suggestionSelector: "div[class='@container/textarea bg-background-subtle relative z-10 grid min-h-[100px] rounded-xl']",
    // Define anchor positions for Vercel V0
    anchorPositions: {
      topLeft: { top: "0", left: "2%" },
      topRight: { top: "0", right: "2%" },
      bottomLeft: { bottom: "10%", left: "2%" },
      bottomRight: { bottom: "32%", right: "19%" },
      defaultAnchor: "bottomRight" // Vercel V0 works better with bottom-right positioning
    }
  },
  gamma: {
    urlPattern: /^https:\/\/(www\.)?gamma\.app\/create\/generate/,
    textAreaSelector: "div[class='flex flex-col flex-1 overflow-y-auto']",
    suggestionSelector: "div[class='chakra-input__group css-13qjysg']",
    // Define anchor positions for Gamma
    anchorPositions: {
      topLeft: { top: "0", left: "2%" },
      topRight: { top: "0", right: "2%" },
      bottomLeft: { bottom: "12%", left: "2%" },
      bottomRight: { bottom: "12%", right: "2%" },
      defaultAnchor: "bottomRight" // Gamma works better with bottom-right positioning
    }
  },
  mistral: {
    urlPattern: /^https:\/\/(www\.)?chat\.mistral\.ai\/chat/,
    textAreaSelector: "div[class='flex flex-col flex-1 overflow-y-auto']",
    suggestionSelector: "div[class='relative overflow-hidden mb-2 min-h-10 w-full']",
    // Define anchor positions for Mistral
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "10%", right: "13%" },
      defaultAnchor: "topRight" // Default anchor for Mistral
    }
  },
  lovable: {
    urlPattern: /^https:\/\/(www\.)?lovable\.dev/,
    textAreaSelector: "div[class='flex w-full rounded-md px-2 py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none text-[16px] leading-snug placeholder-shown:text-ellipsis placeholder-shown:whitespace-nowrap md:text-base focus-visible:ring-0 focus-visible:ring-offset-0 max-h-[200px] scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted-foreground bg-transparent focus:bg-transparent flex-1']",
    suggestionSelector: "div[class='relative overflow-hidden mb-2 min-h-10 w-full']",
    // Define anchor positions for Lovable
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "10%", right: "21%" },
      defaultAnchor: "topRight" // Default anchor for Mistral
    }
  },
  replit: {
    urlPattern: /^https:\/\/(www\.)?replit\.com/,
    textAreaSelector: "div[class='flex flex-col flex-1 overflow-y-auto']",
    suggestionSelector: "div[class='relative overflow-hidden mb-2 min-h-10 w-full']",
    // Define anchor positions for Replit
    anchorPositions: {
      topLeft: { top: "0", left: "0%" },
      topRight: { top: "0", right: "0%" },
      bottomLeft: { bottom: "0%", left: "0%" },
      bottomRight: { bottom: "11%", right: "27%" },
      defaultAnchor: "topRight" // Default anchor for Mistral
    }
  }
};