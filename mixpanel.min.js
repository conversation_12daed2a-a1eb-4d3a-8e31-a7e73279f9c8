(function() {
    var l=void 0,m=!0,r=null,D=!1;
    (function(){function Ba(){function a(){if(!a.Hc)la=a.Hc=m,ma=D,c.a(F,function(a){a.uc()})}function b(){try{u.documentElement.doScroll("left")}catch(d){setTimeout(b,1);return}a()}if(u.addEventListener)"complete"===u.readyState?a():u.addEventListener("DOMContentLoaded",a,D);else if(u.attachEvent){u.attachEvent("onreadystatechange",a);var d=D;try{d=n.frameElement===r}catch(h){}u.documentElement.doScroll&&d&&b()}c.Xb(n,"load",a,m)}function Ca(){x.init=function(a,b,d){if(d)return x[d]||(x[d]=F[d]=S(a,
    b,d),x[d].la()),x[d];d=x;if(F.mixpanel)d=F.mixpanel;else if(a)d=S(a,b,"mixpanel"),d.la(),F.mixpanel=d;x=d;1===ca&&(n.mixpanel=x);Da()}}function Da(){c.a(F,function(a,b){"mixpanel"!==b&&(x[b]=a)});x._=c}function da(a){a=c.g(a)?a:c.e(a)?{}:{days:a};return c.extend({},Ea,a)}function S(a,b,d){var h,f="mixpanel"===d?x:x[d];if(f&&0===ca)h=f;else{if(f&&!c.isArray(f)){o.error("You have already initialized "+d);return}h=new e}h.mb={};h.Y(a,b,d);h.people=new k;h.people.Y(h);if(!h.c("skip_first_touch_marketing")){var a=
    c.info.Z(r),g={},v=D;c.a(a,function(a,b){(g["initial_"+b]=a)&&(v=m)});v&&h.people.O(g)}J=J||h.c("debug");!c.e(f)&&c.isArray(f)&&(h.Ba.call(h.people,f.people),h.Ba(f));return h}function e(){}function P(){}function Fa(a){return a}function na(a){throw Error(a+" not available in this build.");}function q(a){this.props={};this.Fd=D;this.name=a.persistence_name?"mp_"+a.persistence_name:"mp_"+a.token+"_mixpanel";var b=a.persistence;if("cookie"!==b&&"localStorage"!==b)o.B("Unknown persistence type "+b+"; falling back to cookie"),
    b=a.persistence="cookie";this.j="localStorage"===b&&c.localStorage.ua()?c.localStorage:c.cookie;this.load();this.pc(a);this.Bd();this.save()}function k(){}function t(){}function C(a,b){this.L=b.L;this.ca=new G(a,{L:c.bind(this.h,this),j:b.j,v:b.v});this.C=b.C;this.dd=b.ed;this.ma=b.ma;this.od=b.pd;this.G=this.C.batch_size;this.qa=this.C.batch_flush_interval_ms;this.fa=!this.C.batch_autostart;this.Ka=0;this.J={};this.Fb=b.Fb||D}function oa(a,b){var d=[];c.a(a,function(a){var c=a.id;if(c in b){if(c=
    b[c],c!==r)a.payload=c,d.push(a)}else d.push(a)});return d}function pa(a,b){var d=[];c.a(a,function(a){a.id&&!b[a.id]&&d.push(a)});return d}function G(a,b){b=b||{};this.P=a;if(this.v=b.v)this.j=b.j||window.localStorage,this.Ya=new qa(a,{j:this.j});this.h=b.L||c.bind(ra.error,ra);this.wa=b.wa||r;this.D=[]}function qa(a,b){b=b||{};this.P=a;this.j=b.j||window.localStorage;this.Vb=b.Vb||100;this.jc=b.jc||2E3}function T(){this.Sb="submit"}function M(){this.Sb="click"}function E(){}function Ga(){var a=
    n.navigator.onLine;return c.e(a)||a}function sa(a){var b=Ha,d=a.split("."),d=d[d.length-1];if(4<d.length||"com"===d||"org"===d)b=Ia;return(a=a.match(b))?a[0]:""}function ea(a){var b=Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10);return a?b.substring(0,a):b}function U(a,b){if(fa!==r&&!b)return fa;var d=m;try{var a=a||window.localStorage,c="__mplss_"+ea(8);a.setItem(c,"xyz");"xyz"!==a.getItem(c)&&(d=D);a.removeItem(c)}catch(f){d=D}return fa=d}function ga(a){return{log:ha(o.log,
    a),error:ha(o.error,a),B:ha(o.B,a)}}function ha(a,b){return function(){arguments[0]="["+b+"] "+arguments[0];return a.apply(o,arguments)}}function Ja(a,b){ta(m,a,b)}function Ka(a,b){ta(D,a,b)}function La(a,b){return"1"===V(b).get(W(a,b))}function ua(a,b){if(Ma(b))return o.warn('This browser has "Do Not Track" enabled. This will prevent the Mixpanel SDK from sending any data. To ignore the "Do Not Track" browser setting, initialize the Mixpanel instance with the config "ignore_dnt: true"'),m;var d=
    "0"===V(b).get(W(a,b));d&&o.warn("You are opted out of Mixpanel tracking. This will prevent the Mixpanel SDK from sending any data.");return d}function K(a){return ia(a,function(a){return this.c(a)})}function H(a){return ia(a,function(a){return this.p(a)})}function N(a){return ia(a,function(a){return this.p(a)})}function Na(a,b){b=b||{};V(b).remove(W(a,b),!!b.Ab,b.yb)}function V(a){a=a||{};return"localStorage"===a.Ub?c.localStorage:c.cookie}function W(a,b){b=b||{};return(b.Tb||Oa)+a}function Ma(a){if(a&&
    a.Jb)return D;var a=a&&a.window||n,b=a.navigator||{},d=D;c.a([b.doNotTrack,b.msDoNotTrack,a.doNotTrack],function(a){c.i([m,1,"1","yes"],a)&&(d=m)});return d}function ta(a,b,d){!c.Xa(b)||!b.length?o.error("gdpr."+(a?"optIn":"optOut")+" called with an invalid token"):(d=d||{},V(d).set(W(b,d),a?1:0,c.Nb(d.zb)?d.zb:r,!!d.Ab,!!d.cd,!!d.Ec,d.yb),d.o&&a&&d.o(d.ud||"$opt_in",d.vd,{send_immediately:m}))}function ia(a,b){return function(){var d=D;try{var c=b.call(this,"token"),f=b.call(this,"ignore_dnt"),g=
    b.call(this,"opt_out_tracking_persistence_type"),v=b.call(this,"opt_out_tracking_cookie_prefix"),e=b.call(this,"window");c&&(d=ua(c,{Jb:f,Ub:g,Tb:v,window:e}))}catch(j){o.error("Unexpected error when checking tracking opt-out status: "+j)}if(!d)return a.apply(this,arguments);d=arguments[arguments.length-1];"function"===typeof d&&d(0)}}var J=D,n;if("undefined"===typeof window){var A={hostname:""};n={navigator:{userAgent:"",onLine:m},document:{location:A,referrer:""},screen:{width:0,height:0},location:A}}else n=
    window;var A=Array.prototype,va=Object.prototype,L=A.slice,Q=va.toString,X=va.hasOwnProperty,y=n.console,I=n.navigator,u=n.document,Y=n.opera,Z=n.screen,z=I.userAgent,ja=Function.prototype.bind,wa=A.forEach,xa=A.indexOf,ya=A.map,A=Array.isArray,ka={},c={trim:function(a){return a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}},o={log:function(){if(J&&!c.e(y)&&y)try{y.log.apply(y,arguments)}catch(a){c.a(arguments,function(a){y.log(a)})}},warn:function(){if(J&&!c.e(y)&&y){var a=["Mixpanel warning:"].concat(c.Q(arguments));
    try{y.warn.apply(y,a)}catch(b){c.a(a,function(a){y.warn(a)})}}},error:function(){if(J&&!c.e(y)&&y){var a=["Mixpanel error:"].concat(c.Q(arguments));try{y.error.apply(y,a)}catch(b){c.a(a,function(a){y.error(a)})}}},B:function(){if(!c.e(y)&&y){var a=["Mixpanel error:"].concat(c.Q(arguments));try{y.error.apply(y,a)}catch(b){c.a(a,function(a){y.error(a)})}}}};c.bind=function(a,b){var d,h;if(ja&&a.bind===ja)return ja.apply(a,L.call(arguments,1));if(!c.Wa(a))throw new TypeError;d=L.call(arguments,2);return h=
    function(){if(!(this instanceof h))return a.apply(b,d.concat(L.call(arguments)));var c={};c.prototype=a.prototype;var g=new c;c.prototype=r;c=a.apply(g,d.concat(L.call(arguments)));return Object(c)===c?c:g}};c.a=function(a,b,d){if(!(a===r||a===l))if(wa&&a.forEach===wa)a.forEach(b,d);else if(a.length===+a.length)for(var c=0,f=a.length;c<f&&!(c in a&&b.call(d,a[c],c,a)===ka);c++);else for(c in a)if(X.call(a,c)&&b.call(d,a[c],c,a)===ka)break};c.extend=function(a){c.a(L.call(arguments,1),function(b){for(var d in b)b[d]!==
    l&&(a[d]=b[d])});return a};c.isArray=A||function(a){return"[object Array]"===Q.call(a)};c.Wa=function(a){try{return/^\s*\bfunction\b/.test(a)}catch(b){return D}};c.Qc=function(a){return!(!a||!X.call(a,"callee"))};c.Q=function(a){return!a?[]:a.Q?a.Q():c.isArray(a)||c.Qc(a)?L.call(a):c.Dd(a)};c.map=function(a,b,d){if(ya&&a.map===ya)return a.map(b,d);var h=[];c.a(a,function(a){h.push(b.call(d,a))});return h};c.keys=function(a){var b=[];if(a===r)return b;c.a(a,function(a,c){b[b.length]=c});return b};
    c.Dd=function(a){var b=[];if(a===r)return b;c.a(a,function(a){b[b.length]=a});return b};c.Ua=function(a,b){var d=D;if(a===r)return d;if(xa&&a.indexOf===xa)return-1!=a.indexOf(b);c.a(a,function(a){if(d||(d=a===b))return ka});return d};c.i=function(a,b){return-1!==a.indexOf(b)};c.Lb=function(a,b){a.prototype=new b;a.rd=b.prototype};c.g=function(a){return a===Object(a)&&!c.isArray(a)};c.ta=function(a){if(c.g(a)){for(var b in a)if(X.call(a,b))return D;return m}return D};c.e=function(a){return a===l};
    c.Xa=function(a){return"[object String]"==Q.call(a)};c.Rc=function(a){return"[object Date]"==Q.call(a)};c.Nb=function(a){return"[object Number]"==Q.call(a)};c.Sc=function(a){return!!(a&&1===a.nodeType)};c.Na=function(a){c.a(a,function(b,d){c.Rc(b)?a[d]=c.Jc(b):c.g(b)&&(a[d]=c.Na(b))});return a};c.timestamp=function(){Date.now=Date.now||function(){return+new Date};return Date.now()};c.Jc=function(a){function b(a){return 10>a?"0"+a:a}return a.getUTCFullYear()+"-"+b(a.getUTCMonth()+1)+"-"+b(a.getUTCDate())+
    "T"+b(a.getUTCHours())+":"+b(a.getUTCMinutes())+":"+b(a.getUTCSeconds())};c.ga=function(a){var b={};c.a(a,function(a,h){c.Xa(a)&&0<a.length&&(b[h]=a)});return b};c.truncate=function(a,b){var d;"string"===typeof a?d=a.slice(0,b):c.isArray(a)?(d=[],c.a(a,function(a){d.push(c.truncate(a,b))})):c.g(a)?(d={},c.a(a,function(a,f){d[f]=c.truncate(a,b)})):d=a;return d};c.ia=function(){return function(a){function b(a,c){var g="",e=0,i=e="",i=0,j=g,p=[],s=c[a];s&&"object"===typeof s&&"function"===typeof s.toJSON&&
    (s=s.toJSON(a));switch(typeof s){case "string":return d(s);case "number":return isFinite(s)?""+s:"null";case "boolean":case "null":return""+s;case "object":if(!s)return"null";g+="    ";p=[];if("[object Array]"===Q.apply(s)){i=s.length;for(e=0;e<i;e+=1)p[e]=b(e,s)||"null";return i=0===p.length?"[]":g?"[\n"+g+p.join(",\n"+g)+"\n"+j+"]":"["+p.join(",")+"]"}for(e in s)X.call(s,e)&&(i=b(e,s))&&p.push(d(e)+(g?": ":":")+i);return i=0===p.length?"{}":g?"{"+p.join(",")+""+j+"}":"{"+p.join(",")+"}"}}function d(a){var b=
    /[\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,d={"\u0008":"\\b","\t":"\\t","\n":"\\n","\u000c":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};b.lastIndex=0;return b.test(a)?'"'+a.replace(b,function(a){var b=d[a];return"string"===typeof b?b:"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+a+'"'}return b("",{"":a})}}();c.T=function(){function a(){switch(i){case "t":return f("t"),f("r"),f("u"),f("e"),m;case "f":return f("f"),
    f("a"),f("l"),f("s"),f("e"),D;case "n":return f("n"),f("u"),f("l"),f("l"),r}g('Unexpected "'+i+'"')}function b(){for(;i&&" ">=i;)f()}function d(){var a,b,d="",c;if('"'===i)for(;f();){if('"'===i)return f(),d;if("\\"===i)if(f(),"u"===i){for(b=c=0;4>b;b+=1){a=parseInt(f(),16);if(!isFinite(a))break;c=16*c+a}d+=String.fromCharCode(c)}else if("string"===typeof j[i])d+=j[i];else break;else d+=i}g("Bad string")}function c(){var a;a="";"-"===i&&(a="-",f("-"));for(;"0"<=i&&"9">=i;)a+=i,f();if("."===i)for(a+=
    ".";f()&&"0"<=i&&"9">=i;)a+=i;if("e"===i||"E"===i){a+=i;f();if("-"===i||"+"===i)a+=i,f();for(;"0"<=i&&"9">=i;)a+=i,f()}a=+a;if(isFinite(a))return a;g("Bad number")}function f(a){a&&a!==i&&g("Expected '"+a+"' instead of '"+i+"'");i=p.charAt(e);e+=1;return i}function g(a){a=new SyntaxError(a);a.Ed=e;a.text=p;throw a;}var e,i,j={'"':'"',"\\":"\\","/":"/",b:"\u0008",f:"\u000c",n:"\n",r:"\r",t:"\t"},p,s;s=function(){b();switch(i){case "{":var e;a:{var v,j={};if("{"===i){f("{");b();if("}"===i){f("}");e=
    j;break a}for(;i;){v=d();b();f(":");Object.hasOwnProperty.call(j,v)&&g('Duplicate key "'+v+'"');j[v]=s();b();if("}"===i){f("}");e=j;break a}f(",");b()}}g("Bad object")}return e;case "[":a:{e=[];if("["===i){f("[");b();if("]"===i){f("]");v=e;break a}for(;i;){e.push(s());b();if("]"===i){f("]");v=e;break a}f(",");b()}}g("Bad array")}return v;case '"':return d();case "-":return c();default:return"0"<=i&&"9">=i?c():a()}};return function(a){p=a;e=0;i=" ";a=s();b();i&&g("Syntax error");return a}}();c.Cc=
    function(a){var b,d,h,f,g=0,e=0,i="",i=[];if(!a)return a;a=c.Cd(a);do b=a.charCodeAt(g++),d=a.charCodeAt(g++),h=a.charCodeAt(g++),f=b<<16|d<<8|h,b=f>>18&63,d=f>>12&63,h=f>>6&63,f&=63,i[e++]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(b)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(d)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(h)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(f);
    while(g<a.length);i=i.join("");switch(a.length%3){case 1:i=i.slice(0,-2)+"==";break;case 2:i=i.slice(0,-1)+"="}return i};c.Cd=function(a){var a=(a+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),b="",d,c,f=0,g;d=c=0;f=a.length;for(g=0;g<f;g++){var e=a.charCodeAt(g),i=r;128>e?c++:i=127<e&&2048>e?String.fromCharCode(e>>6|192,e&63|128):String.fromCharCode(e>>12|224,e>>6&63|128,e&63|128);i!==r&&(c>d&&(b+=a.substring(d,c)),b+=i,d=c=g+1)}c>d&&(b+=a.substring(d,a.length));return b};c.kb=function(){function a(){function a(b,
    c){var d,h=0;for(d=0;d<c.length;d++)h|=g[d]<<8*d;return b^h}var b,c,g=[],e=0;for(b=0;b<z.length;b++)c=z.charCodeAt(b),g.unshift(c&255),4<=g.length&&(e=a(e,g),g=[]);0<g.length&&(e=a(e,g));return e.toString(16)}function b(){var a=1*new Date,b;if(n.performance&&n.performance.now)b=n.performance.now();else for(b=0;a==1*new Date;)b++;return a.toString(16)+Math.floor(b).toString(16)}return function(){var c=(Z.height*Z.width).toString(16);return b()+"-"+Math.random().toString(16).replace(".","")+"-"+a()+
    "-"+c+"-"+b()}}();var za="ahrefsbot,ahrefssiteaudit,baiduspider,bingbot,bingpreview,chrome-lighthouse,facebookexternal,petalbot,pinterest,screaming frog,yahoo! slurp,yandexbot,adsbot-google,apis-google,duplexweb-google,feedfetcher-google,google favicon,google web preview,google-read-aloud,googlebot,googleweblight,mediapartners-google,storebot-google".split(",");c.Mb=function(a){var b,a=a.toLowerCase();for(b=0;b<za.length;b++)if(-1!==a.indexOf(za[b]))return m;return D};c.jb=function(a){var b,d,h,f=
    [];c.e(b)&&(b="&");c.a(a,function(a,b){d=encodeURIComponent(a.toString());h=encodeURIComponent(b);f[f.length]=h+"="+d});return f.join(b)};c.Ra=function(a,b){var b=b.replace(/[[]/g,"\\[").replace(/[\]]/g,"\\]"),c=RegExp("[\\?&]"+b+"=([^&#]*)").exec(a);if(c===r||c&&"string"!==typeof c[1]&&c[1].length)return"";c=c[1];try{c=decodeURIComponent(c)}catch(h){o.error("Skipping decoding for malformed query param: "+c)}return c.replace(/\+/g," ")};c.cookie={get:function(a){for(var a=a+"=",b=u.cookie.split(";"),
    c=0;c<b.length;c++){for(var h=b[c];" "==h.charAt(0);)h=h.substring(1,h.length);if(0===h.indexOf(a))return decodeURIComponent(h.substring(a.length,h.length))}return r},parse:function(a){var b;try{b=c.T(c.cookie.get(a))||{}}catch(d){}return b},Id:function(a,b,c,h,f,g,e){var i="",j="",p="";e?i="; domain="+e:h&&(i=(i=sa(u.location.hostname))?"; domain=."+i:"");c&&(j=new Date,j.setTime(j.getTime()+1E3*c),j="; expires="+j.toGMTString());g&&(f=m,p="; SameSite=None");f&&(p+="; secure");u.cookie=a+"="+encodeURIComponent(b)+
    j+"; path=/"+i+p},set:function(a,b,c,h,f,g,e){var i="",j="",p="";e?i="; domain="+e:h&&(i=(i=sa(u.location.hostname))?"; domain=."+i:"");c&&(j=new Date,j.setTime(j.getTime()+864E5*c),j="; expires="+j.toGMTString());g&&(f=m,p="; SameSite=None");f&&(p+="; secure");a=a+"="+encodeURIComponent(b)+j+"; path=/"+i+p;return u.cookie=a},remove:function(a,b,d){c.cookie.set(a,"",-1,b,D,D,d)}};var fa=r;c.localStorage={ua:function(a){(a=U(r,a))||o.error("localStorage unsupported; falling back to cookie store");
    return a},error:function(a){o.error("localStorage error: "+a)},get:function(a){try{return window.localStorage.getItem(a)}catch(b){c.localStorage.error(b)}return r},parse:function(a){try{return c.T(c.localStorage.get(a))||{}}catch(b){}return r},set:function(a,b){try{window.localStorage.setItem(a,b)}catch(d){c.localStorage.error(d)}},remove:function(a){try{window.localStorage.removeItem(a)}catch(b){c.localStorage.error(b)}}};c.Xb=function(){function a(a,h,f){return function(g){if(g=g||b(window.event)){var e=
    m,i;c.Wa(f)&&(i=f(g));g=h.call(a,g);if(D===i||D===g)e=D;return e}}}function b(a){if(a)a.preventDefault=b.preventDefault,a.stopPropagation=b.stopPropagation;return a}b.preventDefault=function(){this.returnValue=D};b.stopPropagation=function(){this.cancelBubble=m};return function(b,c,f,g,e){b?b.addEventListener&&!g?b.addEventListener(c,f,!!e):(c="on"+c,b[c]=a(b,f,b[c])):o.error("No valid element provided to register_event")}}();var Pa=/^(\w*)\[(\w+)([=~\|\^\$\*]?)=?"?([^\]"]*)"?\]$/;c.Gc=function(){function a(a,
    b){return 0<=(" "+a.className+" ").replace(d," ").indexOf(" "+b+" ")}function b(b){if(!u.getElementsByTagName)return[];var b=b.split(" "),d,g,e,i,j,p,s,w=[u];for(i=0;i<b.length;i++)if(d=b[i].replace(/^\s+/,"").replace(/\s+$/,""),-1<d.indexOf("#")){g=d.split("#");d=g[0];w=u.getElementById(g[1]);if(!w||d&&w.nodeName.toLowerCase()!=d)return[];w=[w]}else if(-1<d.indexOf(".")){g=d.split(".");d=g[0];var B=g[1];d||(d="*");g=[];for(j=e=0;j<w.length;j++){s="*"==d?w[j].all?w[j].all:w[j].getElementsByTagName("*"):
    w[j].getElementsByTagName(d);for(p=0;p<s.length;p++)g[e++]=s[p]}w=[];for(j=d=0;j<g.length;j++)g[j].className&&c.Xa(g[j].className)&&a(g[j],B)&&(w[d++]=g[j])}else if(g=d.match(Pa)){d=g[1];var k=g[2],B=g[3],n=g[4];d||(d="*");g=[];for(j=e=0;j<w.length;j++){s="*"==d?w[j].all?w[j].all:w[j].getElementsByTagName("*"):w[j].getElementsByTagName(d);for(p=0;p<s.length;p++)g[e++]=s[p]}w=[];d=0;switch(B){case "=":B=function(a){return a.getAttribute(k)==n};break;case "~":B=function(a){return a.getAttribute(k).match(RegExp("\\b"+
    n+"\\b"))};break;case "|":B=function(a){return a.getAttribute(k).match(RegExp("^"+n+"-?"))};break;case "^":B=function(a){return 0===a.getAttribute(k).indexOf(n)};break;case "$":B=function(a){return a.getAttribute(k).lastIndexOf(n)==a.getAttribute(k).length-n.length};break;case "*":B=function(a){return-1<a.getAttribute(k).indexOf(n)};break;default:B=function(a){return a.getAttribute(k)}}w=[];for(j=d=0;j<g.length;j++)B(g[j])&&(w[d++]=g[j])}else{g=[];for(j=e=0;j<w.length;j++){s=w[j].getElementsByTagName(d);
    for(p=0;p<s.length;p++)g[e++]=s[p]}w=g}return w}var d=/[\t\r\n]/g;return function(a){return c.Sc(a)?[a]:c.g(a)&&!c.e(a.length)?a:b.call(this,a)}}();var Qa="utm_source,utm_medium,utm_campaign,utm_content,utm_term,utm_id,utm_source_platform,utm_campaign_id,utm_creative_format,utm_marketing_tactic".split(","),Ra="dclid,fbclid,gclid,ko_click_id,li_fat_id,msclkid,sccid,ttclid,twclid,wbraid".split(",");c.info={Z:function(a){var b="",d={};c.a(Qa,function(h){b=c.Ra(u.URL,h);b.length?d[h]=b:a!==l&&(d[h]=a)});
    return d},xb:function(){var a="",b={};c.a(Ra,function(d){a=c.Ra(u.URL,d);a.length&&(b[d]=a)});return b},Tc:function(){return c.extend(c.info.Z(),c.info.xb())},ad:function(a){return 0===a.search("https?://(.*)google.([^/?]*)")?"google":0===a.search("https?://(.*)bing.com")?"bing":0===a.search("https?://(.*)yahoo.com")?"yahoo":0===a.search("https?://(.*)duckduckgo.com")?"duckduckgo":r},bd:function(a){var b=c.info.ad(a),d={};if(b!==r)d.$search_engine=b,a=c.Ra(a,"yahoo"!=b?"q":"p"),a.length&&(d.mp_keyword=
    a);return d},na:function(a,b,d){return d||c.i(a," OPR/")?c.i(a,"Mini")?"Opera Mini":"Opera":/(BlackBerry|PlayBook|BB10)/i.test(a)?"BlackBerry":c.i(a,"IEMobile")||c.i(a,"WPDesktop")?"Internet Explorer Mobile":c.i(a,"SamsungBrowser/")?"Samsung Internet":c.i(a,"Edge")||c.i(a,"Edg/")?"Microsoft Edge":c.i(a,"FBIOS")?"Facebook Mobile":c.i(a,"Chrome")?"Chrome":c.i(a,"CriOS")?"Chrome iOS":c.i(a,"UCWEB")||c.i(a,"UCBrowser")?"UC Browser":c.i(a,"FxiOS")?"Firefox iOS":c.i(b||"","Apple")?c.i(a,"Mobile")?"Mobile Safari":
    "Safari":c.i(a,"Android")?"Android Mobile":c.i(a,"Konqueror")?"Konqueror":c.i(a,"Firefox")?"Firefox":c.i(a,"MSIE")||c.i(a,"Trident/")?"Internet Explorer":c.i(a,"Gecko")?"Mozilla":""},Ja:function(a,b,d){b={"Internet Explorer Mobile":/rv:(\d+(\.\d+)?)/,"Microsoft Edge":/Edge?\/(\d+(\.\d+)?)/,Chrome:/Chrome\/(\d+(\.\d+)?)/,"Chrome iOS":/CriOS\/(\d+(\.\d+)?)/,"UC Browser":/(UCBrowser|UCWEB)\/(\d+(\.\d+)?)/,Safari:/Version\/(\d+(\.\d+)?)/,"Mobile Safari":/Version\/(\d+(\.\d+)?)/,Opera:/(Opera|OPR)\/(\d+(\.\d+)?)/,
    Firefox:/Firefox\/(\d+(\.\d+)?)/,"Firefox iOS":/FxiOS\/(\d+(\.\d+)?)/,Konqueror:/Konqueror:(\d+(\.\d+)?)/,BlackBerry:/BlackBerry (\d+(\.\d+)?)/,"Android Mobile":/android\s(\d+(\.\d+)?)/,"Samsung Internet":/SamsungBrowser\/(\d+(\.\d+)?)/,"Internet Explorer":/(rv:|MSIE )(\d+(\.\d+)?)/,Mozilla:/rv:(\d+(\.\d+)?)/}[c.info.na(a,b,d)];if(b===l)return r;a=a.match(b);return!a?r:parseFloat(a[a.length-2])},Rb:function(){return/Windows/i.test(z)?/Phone/.test(z)||/WPDesktop/.test(z)?"Windows Phone":"Windows":
    /(iPhone|iPad|iPod)/.test(z)?"iOS":/Android/.test(z)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(z)?"BlackBerry":/Mac/i.test(z)?"Mac OS X":/Linux/.test(z)?"Linux":/CrOS/.test(z)?"Chrome OS":""},Eb:function(a){return/Windows Phone/i.test(a)||/WPDesktop/.test(a)?"Windows Phone":/iPad/.test(a)?"iPad":/iPod/.test(a)?"iPod Touch":/iPhone/.test(a)?"iPhone":/(BlackBerry|PlayBook|BB10)/i.test(a)?"BlackBerry":/Android/.test(a)?"Android":""},Wb:function(a){a=a.split("/");return 3<=a.length?a[2]:""},Ma:function(){return n.location.href},
    ba:function(a){"object"!==typeof a&&(a={});return c.extend(c.ga({$os:c.info.Rb(),$browser:c.info.na(z,I.vendor,Y),$referrer:u.referrer,$referring_domain:c.info.Wb(u.referrer),$device:c.info.Eb(z)}),{$current_url:c.info.Ma(),$browser_version:c.info.Ja(z,I.vendor,Y),$screen_height:Z.height,$screen_width:Z.width,mp_lib:"web",$lib_version:"2.56.0",$insert_id:ea(),time:c.timestamp()/1E3},c.ga(a))},Xc:function(){return c.extend(c.ga({$os:c.info.Rb(),$browser:c.info.na(z,I.vendor,Y)}),{$browser_version:c.info.Ja(z,
    I.vendor,Y)})},Uc:function(){return c.ga({current_page_title:u.title,current_domain:n.location.hostname,current_url_path:n.location.pathname,current_url_protocol:n.location.protocol,current_url_search:n.location.search})}};var Ia=/[a-z0-9][a-z0-9-]*\.[a-z]+$/i,Ha=/[a-z0-9][a-z0-9-]+\.[a-z.]{2,6}$/i,$=r,aa=r;if("undefined"!==typeof JSON)$=JSON.stringify,aa=JSON.parse;$=$||c.ia;aa=aa||c.T;c.toArray=c.Q;c.isObject=c.g;c.JSONEncode=c.ia;c.JSONDecode=c.T;c.isBlockedUA=c.Mb;c.isEmptyObject=c.ta;c.info=
    c.info;c.info.device=c.info.Eb;c.info.browser=c.info.na;c.info.browserVersion=c.info.Ja;c.info.properties=c.info.ba;E.prototype.pa=function(){};E.prototype.Pa=function(){};E.prototype.Ha=function(){};E.prototype.Va=function(a){this.Ob=a;return this};E.prototype.o=function(a,b,d,h){var f=this,e=c.Gc(a);if(0===e.length)o.error("The DOM query ("+a+") returned 0 elements");else return c.a(e,function(a){c.Xb(a,this.Sb,function(a){var c={},e=f.pa(d,this),g=f.Ob.c("track_links_timeout");f.Pa(a,this,c);window.setTimeout(f.lc(h,
    e,c,m),g);f.Ob.o(b,e,f.lc(h,e,c))})},this),m};E.prototype.lc=function(a,b,c,h){var h=h||D,f=this;return function(){if(!c.Dc)c.Dc=m,a&&a(h,b)===D||f.Ha(b,c,h)}};E.prototype.pa=function(a,b){return"function"===typeof a?a(b):c.extend({},a)};c.Lb(M,E);M.prototype.pa=function(a,b){var c=M.rd.pa.apply(this,arguments);if(b.href)c.url=b.href;return c};M.prototype.Pa=function(a,b,c){c.Pb=2===a.which||a.metaKey||a.ctrlKey||"_blank"===b.target;c.href=b.href;c.Pb||a.preventDefault()};M.prototype.Ha=function(a,
    b){b.Pb||setTimeout(function(){window.location=b.href},0)};c.Lb(T,E);T.prototype.Pa=function(a,b,c){c.element=b;a.preventDefault()};T.prototype.Ha=function(a,b){setTimeout(function(){b.element.submit()},0)};var Sa=ga("lock");qa.prototype.ib=function(a,b,c){function h(){k.setItem(o,"1");try{a()}finally{k.removeItem(o),k.getItem(q)===j&&k.removeItem(q),k.getItem(n)===j&&k.removeItem(n)}}function f(){k.setItem(n,j);v(e,function(){k.getItem(n)===j?h():i(function(){k.getItem(q)!==j?f():v(function(){return!k.getItem(o)},
    h)})})}function e(){var a=k.getItem(q);if(a&&a!==j)return D;k.setItem(q,j);if(k.getItem(q)===j)return m;if(!U(k,m))throw Error("localStorage support dropped while acquiring lock");return D}function v(a,b){a()?b():i(function(){v(a,b)})}function i(a){(new Date).getTime()-p>B?(Sa.error("Timeout waiting for mutex on "+s+"; clearing lock. ["+j+"]"),k.removeItem(o),k.removeItem(q),f()):setTimeout(function(){try{a()}catch(c){b&&b(c)}},w*(Math.random()+0.1))}!c&&"function"!==typeof b&&(c=b,b=r);var j=c||
    (new Date).getTime()+"|"+Math.random(),p=(new Date).getTime(),s=this.P,w=this.Vb,B=this.jc,k=this.j,n=s+":X",q=s+":Y",o=s+":Z";try{if(U(k,m))f();else throw Error("localStorage support check failed");}catch(u){b&&b(u)}};var ra=ga("batch");G.prototype.Oa=function(a,b,d){var h={id:ea(),flushAfter:(new Date).getTime()+2*b,payload:a};this.v?this.Ya.ib(c.bind(function(){var b;try{var c=this.ea();c.push(h);(b=this.ab(c))&&this.D.push(h)}catch(e){this.h("Error enqueueing item",a),b=D}d&&d(b)},this),c.bind(function(a){this.h("Error acquiring storage lock",
    a);d&&d(D)},this),this.wa):(this.D.push(h),d&&d(m))};G.prototype.Ic=function(a){var b=this.D.slice(0,a);if(this.v&&b.length<a){var d=this.ea();if(d.length){var h={};c.a(b,function(a){h[a.id]=m});for(var e=0;e<d.length;e++){var g=d[e];if((new Date).getTime()>g.flushAfter&&!h[g.id]&&(g.Wc=m,b.push(g),b.length>=a))break}}}return b};G.prototype.Yc=function(a,b){var d={};c.a(a,function(a){d[a]=m});this.D=pa(this.D,d);if(this.v){var h=c.bind(function(){var b;try{var c=this.ea(),c=pa(c,d);if(b=this.ab(c))for(var c=
    this.ea(),h=0;h<c.length;h++){var e=c[h];if(e.id&&d[e.id])return this.h("Item not removed from storage"),D}}catch(j){this.h("Error removing items",a),b=D}return b},this);this.Ya.ib(function(){var a=h();b&&b(a)},c.bind(function(a){var c=D;this.h("Error acquiring storage lock",a);if(!U(this.j,m)&&(c=h(),!c))try{this.j.removeItem(this.P)}catch(d){this.h("Error clearing queue",d)}b&&b(c)},this),this.wa)}else b&&b(m)};G.prototype.Ad=function(a){this.D=oa(this.D,a);this.v&&this.Ya.ib(c.bind(function(){try{var b=
    this.ea(),b=oa(b,a);this.ab(b)}catch(c){this.h("Error updating items",a)}},this),c.bind(function(a){this.h("Error acquiring storage lock",a)},this),this.wa)};G.prototype.ea=function(){var a;try{if(a=this.j.getItem(this.P))a=aa(a),c.isArray(a)||(this.h("Invalid storage entry:",a),a=r)}catch(b){this.h("Error retrieving queue",b),a=r}return a||[]};G.prototype.ab=function(a){try{return this.j.setItem(this.P,$(a)),m}catch(b){return this.h("Error saving queue",b),D}};G.prototype.clear=function(){this.D=
    [];this.v&&this.j.removeItem(this.P)};var R=ga("batch");C.prototype.Oa=function(a,b){this.ca.Oa(a,this.qa,b)};C.prototype.start=function(){this.fa=D;this.Ka=0;this.flush()};C.prototype.stop=function(){this.fa=m;if(this.eb)clearTimeout(this.eb),this.eb=r};C.prototype.clear=function(){this.ca.clear()};C.prototype.$b=function(){this.G=this.C.batch_size};C.prototype.N=function(){this.bc(this.C.batch_flush_interval_ms)};C.prototype.bc=function(a){this.qa=a;if(!this.fa)this.eb=setTimeout(c.bind(function(){this.fa||
    this.flush()},this),this.qa)};C.prototype.flush=function(a){try{if(this.Zb)R.log("Flush: Request already in progress");else{var a=a||{},b=this.C.batch_request_timeout_ms,d=(new Date).getTime(),h=this.G,e=this.ca.Ic(h),g=e.length===h,v=[],i={};c.a(e,function(a){var b=a.payload;this.ma&&!a.Wc&&(b=this.ma(b));if(b){b.event&&b.properties&&(b.properties=c.extend({},b.properties,{mp_sent_by_lib_version:"2.56.0"}));var d=m,h=a.id;if(h){if(5<(this.J[h]||0))this.h("[dupe] item ID sent too many times, not sending",
    {item:a,G:e.length,td:this.J[h]}),d=D}else this.h("[dupe] found item with no ID",{item:a});d&&v.push(b)}i[a.id]=b},this);if(1>v.length)this.N();else{this.Zb=m;var j=c.bind(function(j){this.Zb=D;try{var k=D;if(a.nc)this.ca.Ad(i);else if(c.g(j)&&"timeout"===j.error&&(new Date).getTime()-d>=b)this.h("Network timeout; retrying"),this.flush();else if(c.g(j)&&(500<=j.sa||429===j.sa||0>=j.sa&&!Ga()||"timeout"===j.error)){var v=2*this.qa;j.ac&&(v=1E3*parseInt(j.ac,10)||v);v=Math.min(6E5,v);this.h("Error; retry in "+
    v+" ms");this.bc(v)}else if(c.g(j)&&413===j.sa)if(1<e.length){var p=Math.max(1,Math.floor(h/2));this.G=Math.min(this.G,p,e.length-1);this.h("413 response; reducing batch size to "+this.G);this.N()}else this.h("Single-event request too large; dropping",e),this.$b(),k=m;else k=m;k&&(this.ca.Yc(c.map(e,function(a){return a.id}),c.bind(function(a){a?(this.Ka=0,this.Fb&&!g?this.N():this.flush()):(this.h("Failed to remove items from queue"),5<++this.Ka?(this.h("Too many queue failures; disabling batching system."),
    this.od()):this.N())},this)),c.a(e,c.bind(function(a){var b=a.id;b?(this.J[b]=this.J[b]||0,this.J[b]++,5<this.J[b]&&this.h("[dupe] item ID sent too many times",{item:a,G:e.length,td:this.J[b]})):this.h("[dupe] found item with no ID while removing",{item:a})},this)))}catch(s){this.h("Error handling API response",s),this.N()}},this),k={method:"POST",rc:m,Oc:m,kc:b};if(a.nc)k.gb="sendBeacon";R.log("MIXPANEL REQUEST:",v);this.dd(v,k,j)}}}catch(s){this.h("Error flushing request queue",s),this.N()}};C.prototype.h=
    function(a,b){R.error.apply(R.error,arguments);if(this.L)try{b instanceof Error||(b=Error(a)),this.L(a,b)}catch(c){R.error(c)}};var Oa="__mp_opt_in_out_",A={dc:function(a,b){var d={},h={};c.g(a)?c.a(a,function(a,b){this.A(b)||(h[b]=a)},this):h[a]=b;d.$set=h;return d},oc:function(a){var b={},d=[];c.isArray(a)||(a=[a]);c.a(a,function(a){this.A(a)||d.push(a)},this);b.$unset=d;return b},gc:function(a,b){var d={},h={};c.g(a)?c.a(a,function(a,b){this.A(b)||(h[b]=a)},this):h[a]=b;d.$set_once=h;return d},
    mc:function(a,b){var d={},h={};c.g(a)?c.a(a,function(a,b){this.A(b)||(h[b]=c.isArray(a)?a:[a])},this):h[a]=c.isArray(b)?b:[b];d.$union=h;return d},Bc:function(a,b){var d={},h={};c.g(a)?c.a(a,function(a,b){this.A(b)||(h[b]=a)},this):h[a]=b;d.$append=h;return d},Yb:function(a,b){var d={},h={};c.g(a)?c.a(a,function(a,b){this.A(b)||(h[b]=a)},this):h[a]=b;d.$remove=h;return d},Gd:function(){return{$delete:""}}};c.extend(t.prototype,A);t.prototype.Y=function(a,b,c){this.d=a;this.Da=b;this.Ca=c};t.prototype.set=
    N(function(a,b,d){var h=this.dc(a,b);c.g(a)&&(d=b);return this.k(h,d)});t.prototype.O=N(function(a,b,d){var h=this.gc(a,b);c.g(a)&&(d=b);return this.k(h,d)});t.prototype.xa=N(function(a,b){return this.k(this.oc(a),b)});t.prototype.ha=N(function(a,b,d){c.g(a)&&(d=b);return this.k(this.mc(a,b),d)});t.prototype["delete"]=N(function(a){return this.k({$delete:""},a)});t.prototype.remove=N(function(a,b,c){return this.k(this.Yb(a,b),c)});t.prototype.k=function(a,b){a.$group_key=this.Da;a.$group_id=this.Ca;
    a.$token=this.p("token");return this.d.Ga({type:"groups",data:c.Na(a),H:this.p("api_host")+"/"+this.p("api_routes").groups,Ia:this.d.u.Sa},b)};t.prototype.A=function(a){return"$group_key"===a||"$group_id"===a};t.prototype.p=function(a){return this.d.c(a)};t.prototype.toString=function(){return this.d.toString()+".group."+this.Da+"."+this.Ca};t.prototype.remove=t.prototype.remove;t.prototype.set=t.prototype.set;t.prototype.set_once=t.prototype.O;t.prototype.union=t.prototype.ha;t.prototype.unset=t.prototype.xa;
    t.prototype.toString=t.prototype.toString;c.extend(k.prototype,A);k.prototype.Y=function(a){this.d=a};k.prototype.set=H(function(a,b,d){var h=this.dc(a,b);c.g(a)&&(d=b);this.p("save_referrer")&&this.d.persistence.hb(document.referrer);h.$set=c.extend({},c.info.Xc(),h.$set);return this.k(h,d)});k.prototype.O=H(function(a,b,d){var h=this.gc(a,b);c.g(a)&&(d=b);return this.k(h,d)});k.prototype.xa=H(function(a,b){return this.k(this.oc(a),b)});k.prototype.Kb=H(function(a,b,d){var h={},e={};c.g(a)?(c.a(a,
    function(a,b){this.A(b)||(isNaN(parseFloat(a))?o.error("Invalid increment value passed to mixpanel.people.increment - must be a number"):e[b]=a)},this),d=b):(c.e(b)&&(b=1),e[a]=b);h.$add=e;return this.k(h,d)});k.prototype.append=H(function(a,b,d){c.g(a)&&(d=b);return this.k(this.Bc(a,b),d)});k.prototype.remove=H(function(a,b,d){c.g(a)&&(d=b);return this.k(this.Yb(a,b),d)});k.prototype.ha=H(function(a,b,d){c.g(a)&&(d=b);return this.k(this.mc(a,b),d)});k.prototype.wd=H(function(a,b,d){if(!c.Nb(a)&&
    (a=parseFloat(a),isNaN(a))){o.error("Invalid value passed to mixpanel.people.track_charge - must be a number");return}return this.append("$transactions",c.extend({$amount:a},b),d)});k.prototype.vb=function(a){return this.set("$transactions",[],a)};k.prototype.Db=function(){if(this.Ea())return this.k({$delete:this.d.I()});o.error("mixpanel.people.delete_user() requires you to call identify() first")};k.prototype.toString=function(){return this.d.toString()+".people"};k.prototype.k=function(a,b){a.$token=
    this.p("token");a.$distinct_id=this.d.I();var d=this.d.s("$device_id"),h=this.d.s("$user_id"),e=this.d.s("$had_persisted_distinct_id");d&&(a.$device_id=d);h&&(a.$user_id=h);e&&(a.$had_persisted_distinct_id=e);d=c.Na(a);return!this.Ea()?(this.vc(a),c.e(b)||(this.p("verbose")?b({status:-1,error:r}):b(-1)),c.truncate(d,255)):this.d.Ga({type:"people",data:d,H:this.p("api_host")+"/"+this.p("api_routes").engage,Ia:this.d.u.$a},b)};k.prototype.p=function(a){return this.d.c(a)};k.prototype.Ea=function(){return this.d.V.Ib===
    m};k.prototype.vc=function(a){"$set"in a?this.d.persistence.q("$set",a):"$set_once"in a?this.d.persistence.q("$set_once",a):"$unset"in a?this.d.persistence.q("$unset",a):"$add"in a?this.d.persistence.q("$add",a):"$append"in a?this.d.persistence.q("$append",a):"$remove"in a?this.d.persistence.q("$remove",a):"$union"in a?this.d.persistence.q("$union",a):o.error("Invalid call to _enqueue():",a)};k.prototype.W=function(a,b,d,e){var f=this,g=c.extend({},this.d.persistence.aa(a)),k=g;!c.e(g)&&c.g(g)&&!c.ta(g)&&
    (f.d.persistence.w(a,g),f.d.persistence.save(),e&&(k=e(g)),b.call(f,k,function(b,e){0===b&&f.d.persistence.q(a,g);c.e(d)||d(b,e)}))};k.prototype.wc=function(a,b,d,e,f,g,k){var i=this;this.W("$set",this.set,a);this.W("$set_once",this.O,e);this.W("$unset",this.xa,g,function(a){return c.keys(a)});this.W("$add",this.Kb,b);this.W("$union",this.ha,f);a=this.d.persistence.aa("$append");if(!c.e(a)&&c.isArray(a)&&a.length)for(var j,b=function(a,b){0===a&&i.d.persistence.q("$append",j);c.e(d)||d(a,b)},e=a.length-
    1;0<=e;e--)a=this.d.persistence.aa("$append"),j=a.pop(),i.d.persistence.save(),c.ta(j)||i.append(j,b);a=this.d.persistence.aa("$remove");if(!c.e(a)&&c.isArray(a)&&a.length)for(var p,b=function(a,b){0===a&&i.d.persistence.q("$remove",p);c.e(k)||k(a,b)},e=a.length-1;0<=e;e--)a=this.d.persistence.aa("$remove"),p=a.pop(),i.d.persistence.save(),c.ta(p)||i.remove(p,b)};k.prototype.A=function(a){return"$distinct_id"===a||"$token"===a||"$device_id"===a||"$user_id"===a||"$had_persisted_distinct_id"===a};k.prototype.set=
    k.prototype.set;k.prototype.set_once=k.prototype.O;k.prototype.unset=k.prototype.xa;k.prototype.increment=k.prototype.Kb;k.prototype.append=k.prototype.append;k.prototype.remove=k.prototype.remove;k.prototype.union=k.prototype.ha;k.prototype.track_charge=k.prototype.wd;k.prototype.clear_charges=k.prototype.vb;k.prototype.delete_user=k.prototype.Db;k.prototype.toString=k.prototype.toString;var Ta="__mps,__mpso,__mpus,__mpa,__mpap,__mpr,__mpu,$people_distinct_id,__alias,__timers".split(",");q.prototype.ba=
    function(){var a={};this.load();c.a(this.props,function(b,d){c.Ua(Ta,d)||(a[d]=b)});return a};q.prototype.load=function(){if(!this.disabled){var a=this.j.parse(this.name);a&&(this.props=c.extend({},a))}};q.prototype.Bd=function(){var a;this.j===c.localStorage?(a=c.cookie.parse(this.name),c.cookie.remove(this.name),c.cookie.remove(this.name,m),a&&this.z(a)):this.j===c.cookie&&(a=c.localStorage.parse(this.name),c.localStorage.remove(this.name),a&&this.z(a))};q.prototype.save=function(){this.disabled||
    this.j.set(this.name,c.ia(this.props),this.Qa,this.La,this.cc,this.Bb,this.oa)};q.prototype.va=function(a){this.load();return this.props[a]};q.prototype.remove=function(){this.j.remove(this.name,D,this.oa);this.j.remove(this.name,m,this.oa)};q.prototype.clear=function(){this.remove();this.props={}};q.prototype.z=function(a,b,d){return c.g(a)?("undefined"===typeof b&&(b="None"),this.Qa="undefined"===typeof d?this.Cb:d,this.load(),c.a(a,function(a,c){if(!this.props.hasOwnProperty(c)||this.props[c]===
    b)this.props[c]=a},this),this.save(),m):D};q.prototype.m=function(a,b){return c.g(a)?(this.Qa="undefined"===typeof b?this.Cb:b,this.load(),c.extend(this.props,a),this.save(),m):D};q.prototype.S=function(a){this.load();a in this.props&&(delete this.props[a],this.save())};q.prototype.qc=function(a){this.m(c.info.bd(a))};q.prototype.hb=function(a){this.z({$initial_referrer:a||"$direct",$initial_referring_domain:c.info.Wb(a)||"$direct"},"")};q.prototype.Mc=function(){return c.ga({$initial_referrer:this.props.$initial_referrer,
    $initial_referring_domain:this.props.$initial_referring_domain})};q.prototype.pc=function(a){this.Cb=this.Qa=a.cookie_expiration;this.fc(a.disable_persistence);this.gd(a.cookie_domain);this.hd(a.cross_site_cookie);this.jd(a.cross_subdomain_cookie);this.md(a.secure_cookie)};q.prototype.fc=function(a){(this.disabled=a)?this.remove():this.save()};q.prototype.gd=function(a){if(a!==this.oa)this.remove(),this.oa=a,this.save()};q.prototype.hd=function(a){if(a!==this.Bb)this.Bb=a,this.remove(),this.save()};
    q.prototype.jd=function(a){if(a!==this.La)this.La=a,this.remove(),this.save()};q.prototype.Kc=function(){return this.La};q.prototype.md=function(a){if(a!==this.cc)this.cc=a?m:D,this.remove(),this.save()};q.prototype.q=function(a,b){var d=this.ka(a),e=b[a],f=this.F("$set"),g=this.F("$set_once"),k=this.F("$unset"),i=this.F("$add"),j=this.F("$union"),p=this.F("$remove",[]),n=this.F("$append",[]);"__mps"===d?(c.extend(f,e),this.w("$add",e),this.w("$union",e),this.w("$unset",e)):"__mpso"===d?(c.a(e,function(a,
    b){b in g||(g[b]=a)}),this.w("$unset",e)):"__mpus"===d?c.a(e,function(a){c.a([f,g,i,j],function(b){a in b&&delete b[a]});c.a(n,function(b){a in b&&delete b[a]});k[a]=m}):"__mpa"===d?(c.a(e,function(a,b){b in f?f[b]+=a:(b in i||(i[b]=0),i[b]+=a)},this),this.w("$unset",e)):"__mpu"===d?(c.a(e,function(a,b){c.isArray(a)&&(b in j||(j[b]=[]),j[b]=j[b].concat(a))}),this.w("$unset",e)):"__mpr"===d?(p.push(e),this.w("$append",e)):"__mpap"===d&&(n.push(e),this.w("$unset",e));o.log("MIXPANEL PEOPLE REQUEST (QUEUED, PENDING IDENTIFY):");
    o.log(b);this.save()};q.prototype.w=function(a,b){var d=this.props[this.ka(a)];c.e(d)||c.a(b,function(b,e){"$append"===a||"$remove"===a?c.a(d,function(a){a[e]===b&&delete a[e]}):delete d[e]},this)};q.prototype.aa=function(a){return this.va(this.ka(a))};q.prototype.ka=function(a){if("$set"===a)return"__mps";if("$set_once"===a)return"__mpso";if("$unset"===a)return"__mpus";if("$add"===a)return"__mpa";if("$append"===a)return"__mpap";if("$remove"===a)return"__mpr";if("$union"===a)return"__mpu";o.error("Invalid queue:",
    a)};q.prototype.F=function(a,b){var d=this.ka(a),b=c.e(b)?{}:b;return this.props[d]||(this.props[d]=b)};q.prototype.kd=function(a){var b=(new Date).getTime(),c=this.va("__timers")||{};c[a]=b;this.props.__timers=c;this.save()};q.prototype.Zc=function(a){var b=(this.va("__timers")||{})[a];c.e(b)||(delete this.props.__timers[a],this.save());return b};var ca,x,O=n.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest,ma=!O&&-1===z.indexOf("MSIE")&&-1===z.indexOf("Mozilla"),ba=r;I.sendBeacon&&(ba=function(){return I.sendBeacon.apply(I,
    arguments)});var A={track:"track/",engage:"engage/",groups:"groups/",record:"record/"},Aa={api_host:"https://api-js.mixpanel.com",api_routes:A,api_method:"POST",api_transport:"XHR",api_payload_format:"base64",app_host:"https://mixpanel.com",cdn:"https://cdn.mxpnl.com",cross_site_cookie:D,cross_subdomain_cookie:m,error_reporter:P,persistence:"cookie",persistence_name:"",cookie_domain:"",cookie_name:"",loaded:P,mp_loader:r,track_marketing:m,track_pageview:D,skip_first_touch_marketing:D,store_google:m,
    stop_utm_persistence:D,save_referrer:m,test:D,verbose:D,img:D,debug:D,track_links_timeout:300,cookie_expiration:365,upgrade:D,disable_persistence:D,disable_cookie:D,secure_cookie:D,ip:m,opt_out_tracking_by_default:D,opt_out_persistence_by_default:D,opt_out_tracking_persistence_type:"localStorage",opt_out_tracking_cookie_prefix:r,property_blacklist:[],xhr_headers:{},ignore_dnt:D,batch_requests:m,batch_size:50,batch_flush_interval_ms:5E3,batch_request_timeout_ms:9E4,batch_autostart:m,hooks:{},record_block_class:/^(mp-block|fs-exclude|amp-block|rr-block|ph-no-capture)$/,
    record_block_selector:"img, video",record_collect_fonts:D,record_idle_timeout_ms:18E5,record_mask_text_class:/^(mp-mask|fs-mask|amp-mask|rr-mask|ph-mask)$/,record_mask_text_selector:"*",record_max_ms:864E5,record_min_ms:0,record_sessions_percent:0,recorder_src:"https://cdn.mxpnl.com/libs/mixpanel-recorder.min.js"},la=D;e.prototype.Va=function(a,b,d){if(c.e(d))this.l("You must name your new library: init(token, config, name)");else if("mixpanel"===d)this.l("You must initialize the main mixpanel object right after you include the Mixpanel js snippet");
    else return a=S(a,b,d),x[d]=a,a.la(),a};e.prototype.Y=function(a,b,d){b=b||{};this.__loaded=m;this.config={};var e={};"api_payload_format"in b||(b.api_host||Aa.api_host).match(/\.mixpanel\.com/)&&(e.api_payload_format="json");this.ec(c.extend({},Aa,e,b,{name:d,token:a,callback_fn:("mixpanel"===d?d:"mixpanel."+d)+"._jsc"}));this._jsc=P;this.za=[];this.Aa=[];this.ya=[];this.V={disable_all_events:D,identify_called:D};this.u={};if(this.U=this.c("batch_requests"))if(!c.localStorage.ua(m)||!O)this.U=D,
    o.log("Turning off Mixpanel request-queueing; needs XHR and localStorage support"),c.a(this.Gb(),function(a){o.log("Clearing batch queue "+a.da);c.localStorage.remove(a.da)});else if(this.Pc(),ba&&n.addEventListener){var f=c.bind(function(){this.u.M.fa||this.u.M.flush({nc:m})},this);n.addEventListener("pagehide",function(a){a.persisted&&f()});n.addEventListener("visibilitychange",function(){"hidden"===u.visibilityState&&f()})}this.persistence=this.cookie=new q(this.config);this.R={};this.xc();a=c.kb();
    this.I()||this.z({distinct_id:"$device:"+a,$device_id:a},"");(a=this.c("track_pageview"))&&this.yc(a);0<this.c("record_sessions_percent")&&100*Math.random()<=this.c("record_sessions_percent")&&this.ic()};e.prototype.ic=K(function(){if(n.MutationObserver){var a=c.bind(function(){this.K=this.K||new n.__mp_recorder(this);this.K.startRecording()},this);c.e(n.__mp_recorder)?na(this.c("recorder_src"),a):a()}else o.B("Browser does not support MutationObserver; skipping session recording")});e.prototype.qd=
    function(){this.K?this.K.stopRecording():o.B("Session recorder module not loaded")};e.prototype.Hb=function(){var a={},b=this.pb();b&&(a.$mp_replay_id=b);return a};e.prototype.Nc=function(){var a=r,b=this.pb();b&&(a="https://mixpanel.com/projects/replay-redirect?"+c.jb({replay_id:b,distinct_id:this.I(),token:this.c("token")}));return a};e.prototype.pb=function(){var a=r;this.K&&(a=this.K.replayId);return a||r};e.prototype.la=function(){this.c("loaded")(this);this.tb();this.people.O(this.persistence.Mc());
    if(this.c("store_google")&&this.c("stop_utm_persistence")){var a=c.info.Z(r);c.a(a,function(a,c){this.S(c)}.bind(this))}};e.prototype.tb=function(){this.persistence.qc(u.referrer);this.c("store_google")&&!this.c("stop_utm_persistence")&&this.m(c.info.Z());this.c("save_referrer")&&this.persistence.hb(u.referrer)};e.prototype.uc=function(){c.a(this.za,function(a){this.Fa.apply(this,a)},this);this.$()||c.a(this.Aa,function(a){this.k.apply(this,a)},this);delete this.za;delete this.Aa};e.prototype.Fa=
    function(a,b){if(this.c("img"))return this.l("You can't use DOM tracking functions with img = true."),D;if(!la)return this.za.push([a,b]),D;var c=(new a).Va(this);return c.o.apply(c,b)};e.prototype.yc=function(a){var b="";this.fb()&&(b=c.info.Ma());if(c.Ua(["full-url","url-with-path-and-query-string","url-with-path"],a)){n.addEventListener("popstate",function(){n.dispatchEvent(new Event("mp_locationchange"))});n.addEventListener("hashchange",function(){n.dispatchEvent(new Event("mp_locationchange"))});
    var d=n.history.pushState;if("function"===typeof d)n.history.pushState=function(a,b,c){d.call(n.history,a,b,c);n.dispatchEvent(new Event("mp_locationchange"))};var e=n.history.replaceState;if("function"===typeof e)n.history.replaceState=function(a,b,c){e.call(n.history,a,b,c);n.dispatchEvent(new Event("mp_locationchange"))};n.addEventListener("mp_locationchange",function(){var d=c.info.Ma(),e=D;"full-url"===a?e=d!==b:"url-with-path-and-query-string"===a?e=d.split("#")[0]!==b.split("#")[0]:"url-with-path"===
    a&&(e=d.split("#")[0].split("?")[0]!==b.split("#")[0].split("?")[0]);e&&this.fb()&&(b=d)}.bind(this))}};e.prototype.qb=function(a,b){if(c.e(a))return r;if(O)return function(c){a(c,b)};var d=this._jsc,e=""+Math.floor(1E8*Math.random()),f=this.c("callback_fn")+"["+e+"]";d[e]=function(c){delete d[e];a(c,b)};return f};e.prototype.k=function(a,b,d,e){var f=m;if(ma)return this.Aa.push(arguments),f;var g={method:this.c("api_method"),gb:this.c("api_transport"),rc:this.c("verbose")},k=r;if(!e&&(c.Wa(d)||"string"===
    typeof d))e=d,d=r;d=c.extend(g,d||{});if(!O)d.method="GET";var g="POST"===d.method,i=ba&&g&&"sendbeacon"===d.gb.toLowerCase(),j=d.rc;b.verbose&&(j=m);this.c("test")&&(b.test=1);j&&(b.verbose=1);this.c("img")&&(b.img=1);if(!O)if(e)b.callback=e;else if(j||this.c("test"))b.callback="(function(){})";b.ip=this.c("ip")?1:0;b._=(new Date).getTime().toString();g&&(k="data="+encodeURIComponent(b.data),delete b.data);var a=a+("?"+c.jb(b)),p=this;if("img"in b)k=u.createElement("img"),k.src=a,u.body.appendChild(k);
    else if(i){try{f=ba(a,k)}catch(n){p.l(n),f=D}try{e&&e(f?1:0)}catch(q){p.l(q)}}else if(O)try{var o=new XMLHttpRequest;o.open(d.method,a,m);var t=this.c("xhr_headers");g&&(t["Content-Type"]="application/x-www-form-urlencoded");c.a(t,function(a,b){o.setRequestHeader(b,a)});if(d.kc&&"undefined"!==typeof o.timeout){o.timeout=d.kc;var x=(new Date).getTime()}o.withCredentials=m;o.onreadystatechange=function(){if(4===o.readyState)if(200===o.status){if(e)if(j){var a;try{a=c.T(o.responseText)}catch(b){if(p.l(b),
    d.Oc)a=o.responseText;else return}e(a)}else e(Number(o.responseText))}else a=o.timeout&&!o.status&&(new Date).getTime()-x>=o.timeout?"timeout":"Bad HTTP status: "+o.status+" "+o.statusText,p.l(a),e&&(j?e({status:0,sa:o.status,error:a,ac:(o.responseHeaders||{})["Retry-After"]}):e(0))};o.send(k)}catch(y){p.l(y),f=D}else k=u.createElement("script"),k.type="text/javascript",k.async=m,k.defer=m,k.src=a,t=u.getElementsByTagName("script")[0],t.parentNode.insertBefore(k,t);return f};e.prototype.Ba=function(a){function b(a,
    b){c.a(a,function(a){if(c.isArray(a[0])){var d=b;c.a(a,function(a){d=d[a[0]].apply(d,a.slice(1))})}else this[a[0]].apply(this,a.slice(1))},b)}var d,e=[],f=[],g=[];c.a(a,function(a){a&&(d=a[0],c.isArray(d)?g.push(a):"function"===typeof a?a.call(this):c.isArray(a)&&"alias"===d?e.push(a):c.isArray(a)&&-1!==d.indexOf("track")&&"function"===typeof this[d]?g.push(a):f.push(a))},this);b(e,this);b(f,this);b(g,this)};e.prototype.ub=function(){return!!this.u.M};e.prototype.Gb=function(){var a="__mpq_"+this.c("token"),
    b=this.c("api_routes");return this.lb=this.lb||{M:{type:"events",H:"/"+b.track,da:a+"_ev"},$a:{type:"people",H:"/"+b.engage,da:a+"_pp"},Sa:{type:"groups",H:"/"+b.groups,da:a+"_gr"}}};e.prototype.Pc=function(){if(!this.ub()){var a=c.bind(function(a){return new C(a.da,{C:this.config,L:this.c("error_reporter"),ed:c.bind(function(b,c,e){this.k(this.c("api_host")+a.H,this.nb(b),c,this.qb(e,b))},this),ma:c.bind(function(b){return this.sb("before_send_"+a.type,b)},this),pd:c.bind(this.cb,this),v:m})},this),
    b=this.Gb();this.u={M:a(b.M),$a:a(b.$a),Sa:a(b.Sa)}}this.c("batch_autostart")&&this.bb()};e.prototype.bb=function(){this.sc=m;if(this.ub())this.U=m,c.a(this.u,function(a){a.start()})};e.prototype.cb=function(){this.U=D;c.a(this.u,function(a){a.stop();a.clear()})};e.prototype.push=function(a){this.Ba([a])};e.prototype.disable=function(a){"undefined"===typeof a?this.V.Fc=m:this.ya=this.ya.concat(a)};e.prototype.nb=function(a){a=c.ia(a);"base64"===this.c("api_payload_format")&&(a=c.Cc(a));return{data:a}};
    e.prototype.Ga=function(a,b){var d=c.truncate(a.data,255),e=a.H,f=a.Ia,g=a.nd,k=a.fd||{},b=b||P,i=m,j=c.bind(function(){k.hc||(d=this.sb("before_send_"+a.type,d));return d?(o.log("MIXPANEL REQUEST:"),o.log(d),this.k(e,this.nb(d),k,this.qb(b,d))):r},this);this.U&&!g?f.Oa(d,function(a){a?b(1,d):j()}):i=j();return i&&d};e.prototype.o=K(function(a,b,d,e){!e&&"function"===typeof d&&(e=d,d=r);var d=d||{},f=d.transport;if(f)d.gb=f;f=d.send_immediately;"function"!==typeof e&&(e=P);if(c.e(a))this.l("No event name provided to mixpanel.track");
    else if(this.ob(a))e(0);else{b=c.extend({},b);b.token=this.c("token");var g=this.persistence.Zc(a);c.e(g)||(b.$duration=parseFloat((((new Date).getTime()-g)/1E3).toFixed(3)));this.tb();g=this.c("track_marketing")?c.info.Tc():{};b=c.extend({},c.info.ba({mp_loader:this.c("mp_loader")}),g,this.persistence.ba(),this.R,this.Hb(),b);g=this.c("property_blacklist");c.isArray(g)?c.a(g,function(a){delete b[a]}):this.l("Invalid value for property_blacklist config: "+g);return this.Ga({type:"events",data:{event:a,
    properties:b},H:this.c("api_host")+"/"+this.c("api_routes").track,Ia:this.u.M,nd:f,fd:d},e)}});e.prototype.ld=K(function(a,b,d){c.isArray(b)||(b=[b]);var e={};e[a]=b;this.m(e);return this.people.set(a,b,d)});e.prototype.zc=K(function(a,b,c){var e=this.s(a),f={};e===l?(f[a]=[b],this.m(f)):-1===e.indexOf(b)&&(e.push(b),f[a]=e,this.m(f));return this.people.ha(a,b,c)});e.prototype.$c=K(function(a,b,c){var e=this.s(a);if(e!==l){var f=e.indexOf(b);-1<f&&(e.splice(f,1),this.m({Hd:e}));0===e.length&&this.S(a)}return this.people.remove(a,
    b,c)});e.prototype.zd=K(function(a,b,d,e){var f=c.extend({},b||{});c.a(d,function(a,b){a!==r&&a!==l&&(f[b]=a)});return this.o(a,f,e)});e.prototype.tc=function(a,b){return a+"_"+JSON.stringify(b)};e.prototype.Lc=function(a,b){var c=this.tc(a,b),e=this.mb[c];if(e===l||e.Da!==a||e.Ca!==b)e=new t,e.Y(this,a,b),this.mb[c]=e;return e};e.prototype.fb=K(function(a,b){"object"!==typeof a&&(a={});var b=b||{},d=b.event_name||"$mp_web_page_view",e=c.extend(c.info.Uc(),c.info.Z(),c.info.xb()),e=c.extend({},e,
    a);return this.o(d,e)});e.prototype.yd=function(){return this.Fa.call(this,M,arguments)};e.prototype.xd=function(){return this.Fa.call(this,T,arguments)};e.prototype.sd=function(a){c.e(a)?this.l("No event name provided to mixpanel.time_event"):this.ob(a)||this.persistence.kd(a)};var Ea={persistent:m};e.prototype.m=function(a,b){var d=da(b);d.persistent?this.persistence.m(a,d.days):c.extend(this.R,a)};e.prototype.z=function(a,b,d){d=da(d);d.persistent?this.persistence.z(a,b,d.days):("undefined"===
    typeof b&&(b="None"),c.a(a,function(a,c){if(!this.R.hasOwnProperty(c)||this.R[c]===b)this.R[c]=a},this))};e.prototype.S=function(a,b){b=da(b);b.persistent?this.persistence.S(a):delete this.R[a]};e.prototype.rb=function(a,b){var c={};c[a]=b;this.m(c)};e.prototype.Ta=function(a,b,c,e,f,g,k,i){var j=this.I();if(a&&j!==a){if("string"===typeof a&&0===a.indexOf("$device:"))return this.l("distinct_id cannot have $device: prefix"),-1;this.m({$user_id:a})}this.s("$device_id")||this.z({$had_persisted_distinct_id:m,
    $device_id:j},"");a!==j&&a!==this.s("__alias")&&(this.S("__alias"),this.m({distinct_id:a}));this.V.Ib=m;this.people.wc(b,c,e,f,g,k,i);a!==j&&this.o("$identify",{distinct_id:a,$anon_distinct_id:j},{hc:m})};e.prototype.reset=function(){this.persistence.clear();this.V.Ib=D;var a=c.kb();this.z({distinct_id:"$device:"+a,$device_id:a},"")};e.prototype.I=function(){return this.s("distinct_id")};e.prototype.Ac=function(a,b){if(a===this.s("$people_distinct_id"))return this.l("Attempting to create alias for existing People user - aborting."),
    -2;var d=this;c.e(b)&&(b=this.I());if(a!==b)return this.rb("__alias",a),this.o("$create_alias",{alias:a,distinct_id:b},{hc:m},function(){d.Ta(a)});this.l("alias matches current distinct_id - skipping api call.");this.Ta(a);return-1};e.prototype.Vc=function(a){this.rb("mp_name_tag",a)};e.prototype.ec=function(a){if(c.g(a))c.extend(this.config,a),a.batch_size&&c.a(this.u,function(a){a.$b()}),this.c("persistence_name")||(this.config.persistence_name=this.config.cookie_name),this.c("disable_persistence")||
    (this.config.disable_persistence=this.config.disable_cookie),this.persistence&&this.persistence.pc(this.config),J=J||this.c("debug")};e.prototype.c=function(a){return this.config[a]};e.prototype.sb=function(a){var b=(this.config.hooks[a]||Fa).apply(this,L.call(arguments,1));"undefined"===typeof b&&(this.l(a+" hook did not return a value"),b=r);return b};e.prototype.s=function(a){return this.persistence.va([a])};e.prototype.toString=function(){var a=this.c("name");"mixpanel"!==a&&(a="mixpanel."+a);
    return a};e.prototype.ob=function(a){return c.Mb(z)||this.V.Fc||c.Ua(this.ya,a)};e.prototype.xc=function(){"localStorage"===this.c("opt_out_tracking_persistence_type")&&c.localStorage.ua()&&(!this.ra()&&this.ra({persistence_type:"cookie"})&&this.Qb({enable_persistence:D}),!this.$()&&this.$({persistence_type:"cookie"})&&this.Za({clear_persistence:D}),this.wb({persistence_type:"cookie",enable_persistence:D}));if(this.$())this.ja({clear_persistence:m});else if(!this.ra()&&(this.c("opt_out_tracking_by_default")||
    c.cookie.get("mp_optout")))c.cookie.remove("mp_optout"),this.Za({clear_persistence:this.c("opt_out_persistence_by_default")})};e.prototype.ja=function(a){if(a&&a.clear_persistence)a=m;else if(a&&a.enable_persistence)a=D;else return;!this.c("disable_persistence")&&this.persistence.disabled!==a&&this.persistence.fc(a);a?this.cb():this.sc&&this.bb()};e.prototype.X=function(a,b){b=c.extend({track:c.bind(this.o,this),persistence_type:this.c("opt_out_tracking_persistence_type"),cookie_prefix:this.c("opt_out_tracking_cookie_prefix"),
    cookie_expiration:this.c("cookie_expiration"),cross_site_cookie:this.c("cross_site_cookie"),cross_subdomain_cookie:this.c("cross_subdomain_cookie"),cookie_domain:this.c("cookie_domain"),secure_cookie:this.c("secure_cookie"),ignore_dnt:this.c("ignore_dnt")},b);c.localStorage.ua()||(b.persistence_type="cookie");return a(this.c("token"),{o:b.track,ud:b.track_event_name,vd:b.track_properties,Ub:b.persistence_type,Tb:b.cookie_prefix,yb:b.cookie_domain,zb:b.cookie_expiration,Ec:b.cross_site_cookie,Ab:b.cross_subdomain_cookie,
    cd:b.secure_cookie,Jb:b.ignore_dnt})};e.prototype.Qb=function(a){a=c.extend({enable_persistence:m},a);this.X(Ja,a);this.ja(a)};e.prototype.Za=function(a){a=c.extend({clear_persistence:m,delete_user:m},a);a.delete_user&&this.people&&this.people.Ea()&&(this.people.Db(),this.people.vb());this.X(Ka,a);this.ja(a)};e.prototype.ra=function(a){return this.X(La,a)};e.prototype.$=function(a){return this.X(ua,a)};e.prototype.wb=function(a){a=c.extend({enable_persistence:m},a);this.X(Na,a);this.ja(a)};e.prototype.l=
    function(a,b){o.error.apply(o.error,arguments);try{!b&&!(a instanceof Error)&&(a=Error(a)),this.c("error_reporter")(a,b)}catch(c){o.error(c)}};e.prototype.init=e.prototype.Va;e.prototype.reset=e.prototype.reset;e.prototype.disable=e.prototype.disable;e.prototype.time_event=e.prototype.sd;e.prototype.track=e.prototype.o;e.prototype.track_links=e.prototype.yd;e.prototype.track_forms=e.prototype.xd;e.prototype.track_pageview=e.prototype.fb;e.prototype.register=e.prototype.m;e.prototype.register_once=
    e.prototype.z;e.prototype.unregister=e.prototype.S;e.prototype.identify=e.prototype.Ta;e.prototype.alias=e.prototype.Ac;e.prototype.name_tag=e.prototype.Vc;e.prototype.set_config=e.prototype.ec;e.prototype.get_config=e.prototype.c;e.prototype.get_property=e.prototype.s;e.prototype.get_distinct_id=e.prototype.I;e.prototype.toString=e.prototype.toString;e.prototype.opt_out_tracking=e.prototype.Za;e.prototype.opt_in_tracking=e.prototype.Qb;e.prototype.has_opted_out_tracking=e.prototype.$;e.prototype.has_opted_in_tracking=
    e.prototype.ra;e.prototype.clear_opt_in_out_tracking=e.prototype.wb;e.prototype.get_group=e.prototype.Lc;e.prototype.set_group=e.prototype.ld;e.prototype.add_group=e.prototype.zc;e.prototype.remove_group=e.prototype.$c;e.prototype.track_with_groups=e.prototype.zd;e.prototype.start_batch_senders=e.prototype.bb;e.prototype.stop_batch_senders=e.prototype.cb;e.prototype.start_session_recording=e.prototype.ic;e.prototype.stop_session_recording=e.prototype.qd;e.prototype.get_session_recording_properties=
    e.prototype.Hb;e.prototype.get_session_replay_url=e.prototype.Nc;e.prototype.DEFAULT_API_ROUTES=A;q.prototype.properties=q.prototype.ba;q.prototype.update_search_keyword=q.prototype.qc;q.prototype.update_referrer_info=q.prototype.hb;q.prototype.get_cross_subdomain=q.prototype.Kc;q.prototype.clear=q.prototype.clear;var F={};(function(a){na=a;ca=1;x=n.mixpanel;c.e(x)?o.B('"mixpanel" object not initialized. Ensure you are using the latest version of the Mixpanel JS Library along with the snippet we provide.'):
    x.__loaded||x.config&&x.persistence?o.B("The Mixpanel library has already been downloaded at least once. Ensure that the Mixpanel code snippet only appears once on the page (and is not double-loaded by a tag manager) in order to avoid errors."):1.1>(x.__SV||0)?o.B("Version mismatch; please ensure you're using the latest version of the Mixpanel code snippet."):(c.a(x._i,function(a){a&&c.isArray(a)&&(F[a[a.length-1]]=S.apply(this,a))}),Ca(),x.init(),c.a(F,function(a){a.la()}),Ba())})(function(a,b){var c=
    document.createElement("script");c.type="text/javascript";c.async=m;c.onload=b;c.src=a;document.head.appendChild(c)})})();
    })();