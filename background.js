import {
  enhancePromptRequest,
  promptAnalysis,
  validateCredits,
  deductCredits,
  savePromptToHistory,
  saveResponseToHistory,
  EnhancePromptV2,
  addContext
} from "./api.js";
import { trackGAEvent } from './ga4-measurement-protocol.js';

// --- Helper Functions ---
/**
 * Get browser information for tracking
 * @returns {string} Browser name and version if available
 */
function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  let browserName = "unknown";

  if (userAgent.indexOf("Chrome") > -1) {
    browserName = "Chrome";
  } else if (userAgent.indexOf("Firefox") > -1) {
    browserName = "Firefox";
  } else if (userAgent.indexOf("Safari") > -1) {
    browserName = "Safari";
  } else if (userAgent.indexOf("Edge") > -1) {
    browserName = "Edge";
  } else if (userAgent.indexOf("Opera") > -1 || userAgent.indexOf("OPR") > -1) {
    browserName = "Opera";
  }

  return browserName;
}

// --- Mixpanel Tracking Function ---
/**
 * Track an event in Mixpanel from background script
 * @param {string} eventName - The name of the event to track
 * @param {Object} properties - Properties to include with the event
 */
function trackMixpanelEvent(eventName, properties = {}) {
  // Send message to popup to track the event
  chrome.runtime.sendMessage({
    action: "track_mixpanel_event",
    eventName: eventName,
    properties: {
      ...properties,
      source: "background",
      timestamp: new Date().toISOString()
    }
  }).catch(error => {
    // console.error("Error sending Mixpanel tracking message:", error);

    // If popup is not available, we'll store the event to track later
    chrome.storage.local.get(['pendingMixpanelEvents'], function(result) {
      const pendingEvents = result.pendingMixpanelEvents || [];
      pendingEvents.push({
        eventName,
        properties: {
          ...properties,
          source: "background",
          timestamp: new Date().toISOString()
        }
      });
      chrome.storage.local.set({ 'pendingMixpanelEvents': pendingEvents });
    });
  });
}

// --- GA4 Session Tracking Variables ---
let platformSessions = {};
let platformButtonClicks = {};
let apiCallCounts = {};

// --- GA4 Session Tracking Functions ---
function startPlatformSession(platform) {
  const now = Date.now();
  platformSessions[platform] = now;
  platformButtonClicks[platform] = 0;
  apiCallCounts = {};

  trackGAEvent("platform_session_start", {
    platform: platform,
    timestamp_millis: now
  }).catch(error => {/* console.error("GA4 Error tracking session start:", error) */});
}

function endPlatformSession(platform) {
  if (platformSessions[platform]) {
    const startTime = platformSessions[platform];
    const duration = Date.now() - startTime;
    const clicks = platformButtonClicks[platform] || 0;

    trackGAEvent("platform_session_end", {
      platform: platform,
      engagement_time_msec: duration.toString(),
      button_clicks: clicks,
    }).catch(error => {/* console.error("GA4 Error tracking session end:", error) */});

    delete platformSessions[platform];
  }
}

// --- Core Extension Logic ---
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === "install") {
    trackGAEvent("extension_installed", {
      reason: details.reason,
      language: chrome.i18n.getUILanguage(),
    }).catch(error => {/* console.error("GA4 Error tracking install:", error) */});


    // Initialize free trial settings when extension is installed
    // - FreeUser: true - marks the user as on free trial
    // - freeUsage: 0 - tracks number of enhances used (limit is 3)
    // - firstInstall: true - flags this as first installation
    // - welcomeDismissed: true - prevents welcome message from showing
    // - firstTimeOpened: false - will be set to true after first time opening the extension
    // - tutorialShown: false - will be set to true after completing the tutorial
    chrome.storage.local.set({
      enabled: true,
      FreeUser: true,
      freeUsage: 0,
      firstInstall: true,
      welcomeDismissed: true,  // Set to true to disable welcome message
      firstTimeOpened: false,  // Initialize to false so tutorial shows on first open
      tutorialShown: false     // Initialize to false so tutorial can be shown
    });

    // Track Free Trial Started event with Mixpanel
    trackMixpanelEvent("Free Trial Started", {
      installation_source: details.previousVersion ? "update" : "new_install",
      browser: getBrowserInfo(),
      language: chrome.i18n.getUILanguage(),
      timestamp: new Date().toISOString()
    });

    // Open popup instead of registration page
    chrome.action.openPopup();
  } else {
    chrome.storage.local.get("enabled", ({ enabled }) => {
      if (enabled === undefined) {
        chrome.storage.local.set({ enabled: true });
      }
    });
  }
   // Set a flag in localStorage to indicate the extension has been installed for lander to check
   chrome.storage.local.set({ 'extensionInstalled': true });
});

chrome.runtime.setUninstallURL("https://thinkvelocity.in/reviews", () => {
  if (chrome.runtime.lastError) {
    // console.error("Error setting uninstall URL:", chrome.runtime.lastError);
  }
});

// Shows notification when a paid user runs out of credits
// Only shows for logged-in users who are not on free trial (FreeUser: false)
function showCreditsFinishedNotification() {
  // Check if user is logged in and not a free user before showing notification
  chrome.storage.local.get(["FreeUser", "token"], (data) => {
    if (data.token && data.FreeUser === false) {
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs && tabs[0]) {
          chrome.scripting.executeScript({
            target: {tabId: tabs[0].id},
            function: () => {
              if (window.showCreditsFinishedNotification) {
                window.showCreditsFinishedNotification();
              }
            }
          }).catch(err => {/* console.error("Failed to show credits notification:", err) */});
        }
      });
    }
  });
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Handle opening login page when free trial has ended
  if (message.action === "openLoginPage") {
    // Track the event
    trackMixpanelEvent("Free Trial Login Clicked", {
      source: "toast_message",
      url: sender.tab?.url || "unknown"
    });

    // Open the login page
    chrome.tabs.create({ url: "https://thinkvelocity.in/login" });

    sendResponse({ success: true });
    return true;
  }

  // Handle Mixpanel tracking from content scripts
  if (message.action === "trackMixpanelEvent") {
    // Forward to popup for tracking
    chrome.runtime.sendMessage({
      action: "track_mixpanel_event",
      eventName: message.eventName,
      properties: {
        ...message.properties,
        source: "content_script",
        url: sender.tab?.url || "unknown",
        tab_id: sender.tab?.id || "unknown"
      }
    }).catch(error => {
      // console.error("Error forwarding Mixpanel event:", error);

      // Store for later if popup is not available
      chrome.storage.local.get(['pendingMixpanelEvents'], function(result) {
        const pendingEvents = result.pendingMixpanelEvents || [];
        pendingEvents.push({
          eventName: message.eventName,
          properties: {
            ...message.properties,
            source: "content_script",
            url: sender.tab?.url || "unknown",
            tab_id: sender.tab?.id || "unknown",
            timestamp: new Date().toISOString()
          }
        });
        chrome.storage.local.set({ 'pendingMixpanelEvents': pendingEvents });
      });
    });

    sendResponse({ success: true });
    return true;
  }

  if (message.action === "button_injected") {
    // Track in both GA4 and Mixpanel
    trackGAEvent("button_injected", {
      platform: message.platform,
    }).catch(error => {/* console.error("GA4 Error tracking button injection:", error) */});

    // Also track in Mixpanel
    trackMixpanelEvent("Button Injected", {
      platform: message.platform,
      url: sender.tab?.url || "unknown"
    });
  }

  if (message.action === "button_clicked") {
    if (message.platform && platformSessions[message.platform]) {
        platformButtonClicks[message.platform] = (platformButtonClicks[message.platform] || 0) + 1;
    }
    trackGAEvent("enhance_button_clicked", {
      platform: message.platform,
    }).catch(error => {/* console.error("GA4 Error tracking button click:", error) */});
  }

  if (message.action === "toggle_state") {
    trackGAEvent("extension_toggle", {
      enabled: message.enabled,
      platform: message.platform
    }).catch(error => {/* console.error("GA4 Error tracking toggle state:", error) */});
  }

  if (message.action === "enhancePrompt") {
    // Check free trial status and usage count
    chrome.storage.local.get(["FreeUser", "freeUsage"], (freeUserData) => {
      // If user is on free trial (FreeUser: true) and has used all 3 enhances
      if (freeUserData.FreeUser === true && freeUserData.freeUsage >= 3) {
        // We'll only show the popup when the button is clicked, not here
        // Just return the error response
        sendResponse({ success: false, error: "Free trial limit reached. Please upgrade your account." });
      } else if (freeUserData.FreeUser === true) {
        // Free trial user with remaining uses
        enhancePromptRequest(message, "user")
          .then((data) => {
            if (data.success) {
              trackGAEvent("enhance_prompt_success", {
                platform: message.platform,
                enhanced_length: data.data?.enhanced_prompt?.length || 0
              }).catch(error => {/* console.error("GA4 Error tracking enhance success:", error) */});

              // Increment usage count when enhancement is successful
              const newUsageCount = (freeUserData.freeUsage || 0) + 1;
              chrome.storage.local.set({ freeUsage: newUsageCount });

              sendResponse({
                success: true,
                data: data,
                platform: message.platform,
              });
            } else {
              trackGAEvent("enhance_prompt_error", {
                platform: message.platform,
                error_message: data.error || "Unknown API error"
              }).catch(error => {/* console.error("GA4 Error tracking enhance error:", error) */});
              sendResponse({ success: false, error: data.error });
            }
          })
          .catch((error) => {
            trackGAEvent("enhance_prompt_error", {
              platform: message.platform,
              error_message: error.message
            }).catch(e => {/* console.error("GA4 tracking error:", e) */});
            sendResponse({ success: false, error: error.message });
          });
      } else {
        validateCredits(message)
          .then((creditValidation) => {
            if (creditValidation.success) {
              // Setting default role to "user" without fetchUserOnboardingData
              const role = "user";
              enhancePromptRequest(message, role)
                .then((data) => {
                  if (data.success) {
                    trackGAEvent("enhance_prompt_success", {
                      platform: message.platform,
                      enhanced_length: data.data?.enhanced_prompt?.length || 0
                    }).catch(error => {/* console.error("GA4 Error tracking enhance success:", error) */});

                    deductCredits(creditValidation.requiredCredits)
                      .then(async () => {
                        try {
                          const promptData = await savePromptToHistory(
                            message
                          );
                          const originalPromptId = promptData.data.history_id;
                          await saveResponseToHistory(
                            data.data.enhanced_prompt,
                            originalPromptId,
                            message
                          );
                          sendResponse({
                            success: true,
                            data: data,
                            platform: message.platform,
                          });
                        } catch (error) {
                          sendResponse({
                            success: false,
                            error: error.message,
                          });
                        }
                      })
                      .catch((error) => {
                        trackGAEvent("credit_deduction_error", {
                          platform: message.platform,
                          error_message: error.message
                        }).catch(e => {/* console.error("GA4 tracking error:", e) */});
                        sendResponse({
                          success: false,
                          error: error.message,
                        });
                      });
                  } else {
                    trackGAEvent("enhance_prompt_error", {
                      platform: message.platform,
                      error_message: data.error || "Unknown API error"
                    }).catch(error => {/* console.error("GA4 Error tracking enhance error:", error) */});
                    sendResponse({ success: false, error: data.error });
                  }
                })
                .catch((error) => {
                  trackGAEvent("enhance_prompt_error", {
                    platform: message.platform,
                    error_message: error.message
                  }).catch(e => {/* console.error("GA4 tracking error:", e) */});
                  sendResponse({ success: false, error: error.message });
                });
            } else {
              trackGAEvent("credit_validation_failed", {
                platform: message.platform,
                reason: "Insufficient credits"
              }).catch(e => {/* console.error("GA4 tracking error:", e) */});

              showCreditsFinishedNotification();

              sendResponse({ success: false, error: "Insufficient credits" });
            }
          })
          .catch((error) => {
            trackGAEvent("credit_validation_error", {
              platform: message.platform,
              error_message: error.message
            }).catch(e => {/* console.error("GA4 tracking error:", e) */});

            showCreditsFinishedNotification();

            sendResponse({ success: false, error: error.message });
          });
      }
    });
    return true;
  }

  if (message.action === "EnhancePromptV2") {
    message.sourceTabId = sender.tab?.id;
    //console.log("[Velocity DEBUG] Received EnhancePromptV2 request:", {
    //   promptLength: message.prompt?.length || 0,
    //   writing_style: message.writing_style,
    //   llm: message.llm,
    //   sourceTabId: message.sourceTabId
    // });

    // Make sure style is mapped to writing_style for backward compatibility
    if (message.style && !message.writing_style) {
      message.writing_style = message.style;
      //console.log("[Velocity DEBUG] Mapped style to writing_style for compatibility");
    }

    // Similar check for free trial status
    chrome.storage.local.get(["FreeUser", "freeUsage"], (freeUserData) => {
      //console.log("[Velocity DEBUG] Free user data:", freeUserData);

      // If user is on free trial and has used all 3 enhances
      if (freeUserData.FreeUser === true && freeUserData.freeUsage >= 3) {
        //console.log("[Velocity DEBUG] Free trial limit reached");
        sendResponse({ success: false, error: "Free trial limit reached. Please upgrade your account." });
      } else if (freeUserData.FreeUser === true) {
        // Free trial user with remaining uses
        //console.log("[Velocity DEBUG] Free trial user with remaining uses, calling EnhancePromptV2");

        EnhancePromptV2(message, "user")
          .then((data) => {
            //console.log("[Velocity DEBUG] EnhancePromptV2 response:", {
            //   success: data.success,
            //   errorMessage: data.error || "none",
            //   dataType: typeof data.data,
            //   hasEnhancedPrompt: data.data?.enhanced_prompt ? "yes" : "no"
            // });

            if (data.success) {
              trackGAEvent("enhance_prompt_v2_success", {
                platform: message.platform,
                enhanced_length: data.data?.enhanced_prompt?.length || 0
              }).catch(error => {/* console.error("GA4 Error tracking enhance success:", error) */});

              // Increment usage count when enhancement is successful
              const newUsageCount = (freeUserData.freeUsage || 0) + 1;
              chrome.storage.local.set({ freeUsage: newUsageCount });

              // Return success response immediately
              //console.log("[Velocity DEBUG] Sending success response to content script");
              sendResponse({
                success: true,
                data: data,
                platform: message.platform,
              });
            } else {
              // console.error("[Velocity DEBUG] EnhancePromptV2 returned error:", data.error || "Unknown API error");
              trackGAEvent("enhance_prompt_v2_error", {
                platform: message.platform,
                error_message: data.error || "Unknown API error"
              }).catch(error => {/* console.error("GA4 Error tracking enhance error:", error) */});

              sendResponse({ success: false, error: data.error });
            }
          })
          .catch(error => {
            // console.error("[Velocity DEBUG] EnhancePromptV2 caught exception:", error);
            trackGAEvent("enhance_prompt_v2_error", {
              platform: message.platform,
              error_message: error.message
            }).catch(e => {/* console.error("GA4 tracking error:", e) */});

            sendResponse({ success: false, error: error.message });
          });
      } else {
        //console.log("[Velocity DEBUG] Paid user, validating credits");
        validateCredits(message)
          .then((creditValidation) => {
            //console.log("[Velocity DEBUG] Credit validation result:", creditValidation);
            if (creditValidation.success) {
              // Setting default role to "user" without fetchUserOnboardingData
              const role = "user";

              //console.log("[Velocity DEBUG] Calling EnhancePromptV2 with role:", role);
              EnhancePromptV2(message, role)
                .then((data) => {
                  //console.log("[Velocity DEBUG] EnhancePromptV2 response for paid user:", {
                  //   success: data.success,
                  //   errorMessage: data.error || "none",
                  //   dataType: typeof data.data,
                  //   hasEnhancedPrompt: data.data?.enhanced_prompt ? "yes" : "no"
                  // });

                  if (data.success) {
                    trackGAEvent("enhance_prompt_v2_success", {
                      platform: message.platform,
                      enhanced_length: data.data?.enhanced_prompt?.length || 0
                    }).catch(error => {/* console.error("GA4 Error tracking enhance success:", error) */});

                    // Save prompt history and deduct credits
                    savePromptToHistory(message)
                      .then((promptData) => {
                        //console.log("[Velocity DEBUG] Prompt saved to history:", promptData);
                        const originalPromptId = promptData.data.history_id;
                        const enhancedPrompt = data.data.enhanced_prompt || data.data;

                        deductCredits(creditValidation.requiredCredits)
                          .then(() => {
                            //console.log("[Velocity DEBUG] Credits deducted successfully");
                            saveResponseToHistory(
                              enhancedPrompt,
                              originalPromptId,
                              message
                            ).catch(error => {
                              // console.error("[Velocity DEBUG] Error saving response:", error);
                            });

                            // Return success response with data
                            //console.log("[Velocity DEBUG] Sending success response to content script");
                            sendResponse({
                              success: true,
                              data: data,
                              platform: message.platform,
                            });
                          })
                          .catch(error => {
                            // console.error("[Velocity DEBUG] Credit deduction error:", error);
                            trackGAEvent("credit_deduction_error", {
                              platform: message.platform,
                              error_message: error.message
                            }).catch(e => {/* console.error("GA4 tracking error:", e) */});

                            sendResponse({ success: false, error: error.message });
                          });
                      })
                      .catch(error => {
                        // console.error("[Velocity DEBUG] Save prompt history error:", error);
                        trackGAEvent("save_prompt_history_error", {
                          platform: message.platform,
                          error_message: error.message
                        }).catch(e => {/* console.error("GA4 tracking error:", e) */});

                        sendResponse({ success: false, error: error.message });
                      });
                  } else {
                    // console.error("[Velocity DEBUG] EnhancePromptV2 returned error for paid user:", data.error);
                    trackGAEvent("enhance_prompt_v2_error", {
                      platform: message.platform,
                      error_message: data.error || "Unknown API error"
                    }).catch(error => {/* console.error("GA4 Error tracking enhance error:", error) */});

                    sendResponse({ success: false, error: data.error });
                  }
                })
                .catch(error => {
                  // console.error("[Velocity DEBUG] EnhancePromptV2 caught exception for paid user:", error);
                  trackGAEvent("enhance_prompt_v2_error", {
                    platform: message.platform,
                    error_message: error.message
                  }).catch(e => {/* console.error("GA4 tracking error:", e) */});

                  sendResponse({ success: false, error: error.message });
                });
            } else {
              // console.error("[Velocity DEBUG] Credit validation failed:", creditValidation);
              trackGAEvent("credit_validation_failed", {
                platform: message.platform,
                reason: "Insufficient credits"
              }).catch(e => {/* console.error("GA4 tracking error:", e) */});

              showCreditsFinishedNotification();
              sendResponse({ success: false, error: "Insufficient credits" });
            }
          })
          .catch((error) => {
            // console.error("[Velocity DEBUG] Credit validation exception:", error);
            trackGAEvent("credit_validation_error", {
              platform: message.platform,
              error_message: error.message
            }).catch(e => {/* console.error("GA4 tracking error:", e) */});

            showCreditsFinishedNotification();
            sendResponse({ success: false, error: error.message });
          });
      }
    });
    return true;
  }

  // if (message.action === "streamEnhancePrompt") {
  //   // This is the old handler which we are replacing
  //   // Return an error message to inform clients to use EnhancePromptV2 instead
  //   sendResponse({
  //     success: false,
  //     error: "This API endpoint is deprecated. Please use EnhancePromptV2 instead."
  //   });
  //   return true;
  // }

  if (message.action === "addContext") {
    //console.log("[DEBUG] Background received addContext message:", {
    //   hasPrompt: !!message.prompt,
    //   promptLength: message.prompt?.length || 0,
    //   hasEnhancedPrompt: !!message.enhanced_prompt,
    //   enhancedPromptLength: message.enhanced_prompt?.length || 0,
    //   qa_pairs: message.qa_pairs
    // });

    // Check which parameter is being passed to the API function
    const apiParams = {
      prompt: message.prompt,
      enhanced_prompt: message.enhanced_prompt,
      qa_pairs: message.qa_pairs
    };

    //console.log("[DEBUG] Parameters being passed to addContext API:", apiParams);

    addContext(message)
      .then((data) => {
        //console.log("[DEBUG] addContext API response:", data);
        sendResponse(data);
      })
      .catch((error) => {
        // console.error("[DEBUG] addContext API error:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  if (message.action === "promptAnalysis") {
    //console.log("[DEBUG] Background received promptAnalysis message:", {
    //   promptLength: message.prompt?.length || 0,
    //   platform: message.platform,
    //   style: message.style
    // });

    promptAnalysis(message)
      .then((data) => {
        //console.log("[DEBUG] promptAnalysis API response:", data);

        // Pass the complete data structure directly to the content script
        // This preserves all fields including framework_analysis
        if (data.success) {
          sendResponse({
            success: true,
            data: data.data,
            // Add these fields at the top level for easier access
            metrics: data.data.metrics || {},
            recommendations: data.data.recommendations || {},
            framework_analysis: data.data.framework_analysis || {}
          });
        } else {
          sendResponse({ success: false, error: data.error || "Unknown error" });
        }
      })
      .catch((error) => {
        // console.error("[DEBUG] promptAnalysis API error:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  if (message.action === "storeUserData") {
    trackGAEvent("user_data_stored", {}).catch(error => {/* console.error("GA4 error tracking user data stored:", error) */});

    // Track User Logged In event with Mixpanel
    trackMixpanelEvent("User Logged In", {
      user_id: message.userId,
      user_name: message.userName,
      user_email: message.userEmail,
      login_source: message.source || "extension",
      browser: getBrowserInfo(),
      is_free_trial_conversion: message.wasOnFreeTrial === true
    });

    chrome.storage.local.set(
      {
        token: message.token,
        userName: message.userName,
        userId: message.userId,
        userEmail: message.userEmail,
        FreeUser: false // Set FreeUser to false when logged in - user is now a paid user
      },
      () => {
        chrome.tabs.query({
          url: [
            "*://chat.openai.com/*",
            "*://chatgpt.com/*",
            "*://claude.ai/*",
            "*://thinkvelocity.in/*",
            "*://gemini.google.com/*",
            "*://www.perplexity.ai/*",
            "*://grok.com/*",
            "*://bolt.new/*",
            "*://v0.dev/*",
            "*://gamma.app/*",
            "*://chat.mistral.ai/*",
            "*://mistral.ai/*",
            "*://lovable.dev/*",
            "*://replit.com/*",
            "*://app.runwayml.com/*"
          ],
        }, (tabs) => {
          tabs.forEach((tab) => {
            chrome.scripting.executeScript({
              target: { tabId: tab.id },
              files: ["suggestionBox.js", "promptReview.js", "content-script.js"],
            }).catch(err => {/* console.warn(`Failed to inject scripts into tab ${tab.id}:`, err) */});
          });
        });
        sendResponse({ success: true });
      }
    );
    return true;
  }

  if (message.action === "prompt_characteristics") {
    trackGAEvent("prompt_characteristics", {
      platform: message.platform,
      promptLength: message.promptLength,
      wordCount: message.wordCount,
      sentenceCount: message.sentenceCount,
      hasBulletPoints: message.hasBulletPoints,
      hasNumberedList: message.hasNumberedList,
    }).catch(error => {/* console.error("GA4 Error tracking prompt characteristics:", error) */});
  }

  if (message.action === "ping") {
    sendResponse({ success: true });
    return true;
  }

  if (message.action === "open_extension_popup") {
    trackGAEvent("extension_popup_opened", {
      trigger: message.trigger || "unknown"
    }).catch(error => {/* console.error("GA4 Error tracking popup opened:", error) */});
    chrome.action.openPopup();
  }

  if (message.action === "openLoginPage") {
    trackGAEvent("free_trial_login_clicked", {
      platform: sender?.tab?.url || "unknown"
    }).catch(error => {/* console.error("GA4 Error tracking free trial login click:", error) */});

    chrome.tabs.create({ url: "https://thinkvelocity.in/login" });
    sendResponse({ success: true });
    return true;
  }
});

// When a tab is updated, check if user has hit free trial limit
// If so, show notification in the tab
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (
    changeInfo.status === "complete" &&
    tab.url &&
    (/^(https|http):\/\/chat\.openai\.com/.test(tab.url) ||
     /^https:\/\/chatgpt\.com/.test(tab.url) ||
     /^https:\/\/claude\.ai/.test(tab.url) ||
     /^https:\/\/thinkvelocity\.in/.test(tab.url) ||
     /^https:\/\/gemini\.google\.com/.test(tab.url) ||
     /^https:\/\/www\.perplexity\.ai/.test(tab.url) ||
     /^https:\/\/perplexity\.ai/.test(tab.url) ||
     /^https:\/\/grok\.com/.test(tab.url) ||
     /^https:\/\/bolt\.new/.test(tab.url) ||
     /^https:\/\/v0\.dev/.test(tab.url) ||
     /^https:\/\/gamma\.app/.test(tab.url) ||
     /^https:\/\/mistral\.ai\/chat/.test(tab.url) ||
     /^https:\/\/chat\.mistral\.ai/.test(tab.url) ||
     /^https:\/\/lovable\.dev/.test(tab.url) ||
     /^https:\/\/replit\.com/.test(tab.url) ||
     /^https:\/\/app\.runwayml\.com/.test(tab.url))
  ) {
    chrome.storage.local.get(
      ["enabled", "token", "userName", "userId", "userEmail", "FreeUser", "freeUsage"],
      ({ enabled, token, userName, userId, userEmail, FreeUser, freeUsage }) => {
        // We no longer show the notification automatically on page load
        // The notification will only appear when the button is clicked
        // by a free user who has used all their free uses

        if (enabled && token && userName && userId && userEmail) {
          //console.log(`Injecting scripts into tab ${tabId} (${tab.url})`);
          chrome.scripting.executeScript({
            target: { tabId },
            files: ["suggestionBox.js", "promptReview.js", "content-script.js"],
          })
            .then(() => {
              //console.log(`Successfully injected scripts into tab ${tabId}`);
              trackGAEvent("content_script_injected", {
                url: tab.url,
                trigger: "tab_updated"
              }).catch(error => {/* console.error("GA4 Error tracking content script injection:", error) */});
            })
            .catch(err => {
              // console.warn(`Failed to inject scripts into tab ${tabId} (${tab.url}):`, err);
              trackGAEvent("content_script_injection_failed", {
                url: tab.url,
                error: err.message
              }).catch(e => {/* console.error("GA4 tracking error:", e) */});
            });
        }
      }
    );
  }
});

chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === "local" && "enabled" in changes) {
    const isEnabled = changes.enabled.newValue;
    trackGAEvent("extension_toggle_storage", { enabled: isEnabled })
      .catch(e => {/* console.error("GA4 tracking error:", e) */});

    chrome.tabs.query({
      url: [
        "*://chat.openai.com/*",
        "*://chatgpt.com/*",
        "*://claude.ai/*",
        "*://thinkvelocity.in/*",
        "*://gemini.google.com/*",
        "*://www.perplexity.ai/*",
        "*://grok.com/*",
        "*://bolt.new/*",
        "*://v0.dev/*",
        "*://gamma.app/*",
        "*://chat.mistral.ai/*",
        "*://mistral.ai/*",
        "*://lovable.dev/*",
        "*://replit.com/*",
        "*://app.runwayml.com/*"
      ],
    }, (tabs) => {
      tabs.forEach((tab) => {
        chrome.tabs.sendMessage(tab.id, {
          action: "extensionStateChanged",
          enabled: isEnabled
        }).catch(err => {/* console.warn(`Failed to send state change message to tab ${tab.id}:`, err) */});
      });
    });
  }
});



// Add this to your background.js
chrome.runtime.onInstalled.addListener(async () => {
  // Store the API key securely
  await chrome.storage.local.set({
    apiAuthKey: "a1cacd98586a0e974faad626dd85f3f4b4fe120b710686773300f2d8c51d63bf"
  });


  // Clean up unnecessary storage items
  const keysToRemove = [
    // Timestamps
    "tabOpenTimestamp",
    "generationStartTime",
    "generationCompleteTime",
    "installTimestamp",
    "loginTimestamp",
    "platformSelectionTimestamp",

    // Redundant prompts
    "generatedPrompt",

    // Redundant platform storage
    "lastSelectedPlatform",
    "lastSelectedPlatformKey",
    "lastSelectedPlatformUrl",
    "platformSelectionTimestamp"
  ];

  // Get all items from storage to check what exists
  chrome.storage.local.get(null, function(items) {
    // Remove keys that exist
    const keysToRemoveFiltered = keysToRemove.filter(key => key in items);
    if (keysToRemoveFiltered.length > 0) {
      chrome.storage.local.remove(keysToRemoveFiltered);
    }
  });
});

