
window.velocityWrapperState = {
  wrapper: null,
  container: null,
  inputBox: null,
  button: null,
  anchors: {},
  isDragging: false,
  platform: null,
  dragOffsetX: 0,
  dragOffsetY: 0,
  hoverBox: true,
  animationsDisabled: false,
  buttonSystem: null
};


/**
 * Initialize the new button animation system with optional button parameter
 * @param {Object} platformConfig - Platform configuration for animations
 * @param {HTMLElement} button - Optional button element to use
 */
function initializeButtonAnimationSystem(platformConfig, button = null) {
 
  if (window.velocityHoverBoxIntegration && typeof window.velocityHoverBoxIntegration.cleanupMessageBoxes === 'function') {
    // console.log('[Velocity Init] Cleaning up message boxes using hover box integration');
    window.velocityHoverBoxIntegration.cleanupMessageBoxes();
  } else {
    // Fallback direct cleanup if integration module is not available
    // console.log('[Velocity Init] Using fallback message box cleanup');
    const messageBoxes = document.querySelectorAll('.velocity-message-box');
    // console.log(`[Velocity Init] Found ${messageBoxes.length} message boxes to clean up`);

    messageBoxes.forEach(box => {
      if (box && box.parentNode) {
        box.parentNode.removeChild(box);
      }
    });
  }

  // Ensure cleanup of any existing system
  if (window.velocityWrapperState.buttonSystem) {
    // console.log('[Velocity Init] Resetting existing button system');
    window.velocityWrapperState.buttonSystem.reset();
  }

  // Create and initialize the new system
  // console.log('[Velocity Init] Creating new button animation system');
  window.velocityWrapperState.buttonSystem = window.createVelocityButtonSystem(platformConfig);

  // If a button is provided, use it directly instead of finding it
  if (button) {
    // console.log('[Velocity Init] Using provided button for animation system');
    window.velocityWrapperState.buttonSystem.state.currentButton = button;

    // Find the input box
    const inputBox = window.velocityWrapperState.inputBox ||
                    document.querySelector(platformConfig.textAreaSelector);

    if (inputBox) {
      // console.log('[Velocity Init] Setting up event listeners for input box');
      window.velocityWrapperState.buttonSystem.setupEventListeners(inputBox, button);

      // Use the animation controller to handle button reinitialization
      // console.log('[Velocity Init] Using animation controller to handle button reinitialization');
      // This will start animations immediately and delay messages for 5 seconds
      window.velocityWrapperState.buttonSystem.animationController.handleButtonReinitialization(button);
    } else {
      // console.log('[Velocity Init] ⚠️ No input box found for event listeners');
    }
  } else {
    // Fall back to the original findButton logic
    // console.log('[Velocity Init] No button provided, using findButton logic');
    window.velocityWrapperState.buttonSystem.init(platformConfig);
  }

  // console.log('[Velocity Init] ✅ Button animation system initialized with message box cleanup');
}

// Function to disable all animations
function disableAllAnimations() {
  window.velocityWrapperState.animationsDisabled = true;

  if (window.velocityWrapperState.buttonSystem) {
    window.velocityWrapperState.buttonSystem.disableAnimations();
  } else {
    if (window.velocityAnimations) {
      if (window.velocityAnimations.state) {
        window.velocityAnimations.state.disableAnimations = true;
      }

      if (typeof window.velocityAnimations.disableAllAnimations === 'function') {
        window.velocityAnimations.disableAllAnimations();
      }
    }

    const buttons = document.querySelectorAll('.velocity-button-container button, .custom-injected-button button');
    buttons.forEach(button => {
      button.classList.remove(
        'velocity-loading-animation',
        'velocity-half-circle-glow',
        'velocity-inner-pulse-bounce',
        'velocity-inner-pulse-bounce-shake',
        'velocity-idle-typing-effect',
        'velocity-success-idle-effect',
        'velocity-multi-ring-container',
        'velocity-multi-ring',
        'velocity-splash',
        'velocity-highlight-pulse',
        'velocity-enhanced-highlight',
        'velocity-enhanced-scale'
      );

      // Remove multi-ring container elements
      const multiRingContainer = button.querySelector('.velocity-multi-ring-container');
      if (multiRingContainer) {
        try {
          button.removeChild(multiRingContainer);
        } catch (e) {
          // console.error("[Velocity] Error removing multi-ring container:", e);
        }
      }

      // Remove any standalone multi-ring elements
      const multiRings = button.querySelectorAll('.velocity-multi-ring');
      multiRings.forEach(ring => {
        try {
          if (ring.parentNode) {
            ring.parentNode.removeChild(ring);
          }
        } catch (e) {
          // console.error("[Velocity] Error removing multi-ring element:", e);
        }
      });

      if (window.velocityAnimations && window.velocityAnimations.cleanupLoadingAnimation) {
        window.velocityAnimations.cleanupLoadingAnimation(button);
      }
    });
  }

  // Clear any animation intervals
  if (window._velocityAnimationIntervals) {
    window._velocityAnimationIntervals.forEach(interval => clearInterval(interval));
    window._velocityAnimationIntervals = [];
  }

  // Also look for any other multi-ring containers in the document
  const documentMultiRings = document.querySelectorAll('.velocity-multi-ring-container, .velocity-multi-ring');
  documentMultiRings.forEach(element => {
    try {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    } catch (e) {
      // console.error("[Velocity] Error removing multi-ring element from document:", e);
    }
  });

  //console.log("[Velocity] All animations disabled");
}

// Ensure animations are disabled before showing the trial finished notification
function showTrialFinishedPopupWithDisabledAnimations() {
  // Always disable animations first
  disableAllAnimations();

  // Clear any existing trial popup
  if (window.trailFinishedInstance && window.trailFinishedInstance.parentNode) {
    window.trailFinishedInstance.parentNode.removeChild(window.trailFinishedInstance);
    window.trailFinishedInstance = null;
  }

  // Remove any existing popups with the same ID
  document.querySelectorAll('#trail-finished-popup, .trail-finished').forEach(popup => {
    if (popup.parentNode) popup.parentNode.removeChild(popup);
  });

  // Show the trial finished notification
  if (window.showTrailFinishedNotification) {
    window.showTrailFinishedNotification();
    disableAllAnimations();
    // Also show the toast message
  } else {
    // If function not available, try loading the script
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('trail-finished.js');
    script.onload = function() {
      if (window.showTrailFinishedNotification) {
        window.showTrailFinishedNotification();
        disableAllAnimations();
        // Also show the toast message
        // Patch the original function to ensure animations stay disabled
        const originalShowTrailFinishedNotification = window.showTrailFinishedNotification;
        window.showTrailFinishedNotification = function() {
          disableAllAnimations();
          // Also show the toast message when the patched function is called
          return originalShowTrailFinishedNotification.apply(this, arguments);
        };
      }
    };
    document.head.appendChild(script);
  }
}

// Function to inject enhanced highlight styles
function injectEnhancedHighlightStyles() {
  if (document.getElementById('velocity-highlight-styles')) return;

  const styleEl = document.createElement('style');
  styleEl.id = 'velocity-highlight-styles';
  styleEl.innerHTML = `
    @keyframes velocity-enhanced-highlight {
      0% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
      30% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      70% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      100% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
    }

    @keyframes velocity-enhanced-scale {
      0% { transform: scale(1); }
      30% { transform: scale(1.03); }
      70% { transform: scale(1.03); }
      100% { transform: scale(1); }
    }

    .velocity-enhanced-highlight {
      animation: velocity-enhanced-highlight 1s ease-in-out forwards;
      border-color: #0088cb !important;
      transition: all 0.3s ease;
    }

    .velocity-enhanced-scale {
      animation: velocity-enhanced-scale 1s ease-in-out forwards;
    }

    .text-pop-effect {
      animation: text-pop 0.3s ease-in-out;
    }

    @keyframes text-pop {
      0% { transform: scale(1); }
      50% { transform: scale(1.02); }
      100% { transform: scale(1); }
    }
  `;

  document.head.appendChild(styleEl);
}

// Enhanced function to inject prompt into input field with better error handling
function injectPromptIntoInputField(prompt) {
  // console.log("[Velocity Injection] 🔄 injectPromptIntoInputField called, prompt length:", prompt ? prompt.length : 0);

  if (!prompt || prompt === "none") {
    // console.log("[Velocity Injection] ❌ No valid prompt to inject");
    return false;
  }

  // console.log("[Velocity Injection] Starting prompt injection process");

  // Get the platform-specific input field
  const platform = window.velocityWrapperState?.platform;
  // console.log("[DEBUG] Current platform:", platform);

  // Detect the current platform if not available in state
  const currentURL = window.location.href;
  let detectedPlatform = platform;

  if (!detectedPlatform) {
    if (currentURL.includes("chat.openai.com") || currentURL.includes("chatgpt.com")) {
      detectedPlatform = "chatgpt";
    } else if (currentURL.includes("claude.ai")) {
      detectedPlatform = "claude";
    } else if (currentURL.includes("gemini.google.com")) {
      detectedPlatform = "gemini";
    } else if (currentURL.includes("perplexity.ai")) {
      detectedPlatform = "perplexity";
    } else if (currentURL.includes("grok.com")) {
      detectedPlatform = "grok";
    } else if (currentURL.includes("mistral.ai")) {
      detectedPlatform = "mistral";
    } else if (currentURL.includes("bolt.new")) {
      detectedPlatform = "bolt";
    } else if (currentURL.includes("v0.dev")) {
      detectedPlatform = "v0";
    } else if (currentURL.includes("gamma.app")) {
      detectedPlatform = "gamma";
    } else if (currentURL.includes("lovable.dev")) {
      detectedPlatform = "lovable";
    } else if (currentURL.includes("replit.com")) {
      detectedPlatform = "replit";
    } else if (currentURL.includes("app.runwayml.com")) {
      detectedPlatform = "runway";
    }
  }

  // console.log("[DEBUG] Platform for injection:", detectedPlatform);

  // Try to find the input field
  let inputField = null;

  // First check if we have an input box in the wrapper state
  if (window.velocityWrapperState && window.velocityWrapperState.inputBox) {
    inputField = window.velocityWrapperState.inputBox;
    // console.log("[DEBUG] Using input field from velocityWrapperState");
  }
  // If not, try platform-specific selectors
  else {

    // If platform-specific selector didn't work, try generic selectors
    if (!inputField) {
      const genericSelectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '[role="textbox"]',
        '.ProseMirror',
        '.ql-editor',
        'div[class*="editor"]',
        'div[class*="input"]',
        'div[class*="textarea"]'
      ];

      for (const selector of genericSelectors) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          // Find the visible one that's not disabled
          for (const el of elements) {
            if (el.offsetParent !== null && !el.disabled) {
              inputField = el;
              // console.log("[DEBUG] Found input field with selector:", selector);
              break;
            }
          }
          if (inputField) break;
        }
      }
    }
  }

  if (!inputField) {
    // console.error("[DEBUG] No input field found for injection");
    return false;
  }

  // Make sure highlight styles are injected
  injectEnhancedHighlightStyles();

  // Inject the prompt based on input field type
  try {
    if (inputField.tagName === "TEXTAREA" || inputField.tagName === "INPUT") {
      // console.log("[DEBUG] Injecting into TEXTAREA or INPUT");
      inputField.value = prompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (inputField.hasAttribute("contenteditable")) {
      // console.log("[DEBUG] Injecting into contenteditable element");
      inputField.innerHTML = prompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
      // console.log("[DEBUG] Injecting into generic element");
      if (typeof inputField.value !== 'undefined') {
        inputField.value = prompt;
      } else {
        inputField.textContent = prompt;
      }
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // Focus the input field
    inputField.focus();

    // Apply the highlight and scale effects
    inputField.classList.add("velocity-enhanced-highlight", "velocity-enhanced-scale");

    // If the input is in a container, also highlight that
    const inputContainer = inputField.closest('.chat-input-container, .input-container, [role="textbox"]');
    if (inputContainer && inputContainer !== inputField) {
      inputContainer.classList.add("velocity-enhanced-highlight");
    }

    // After animation completes, remove highlight classes
    setTimeout(function() {
      inputField.classList.remove("velocity-enhanced-highlight", "velocity-enhanced-scale");
      if (inputContainer && inputContainer !== inputField) {
        inputContainer.classList.remove("velocity-enhanced-highlight");
      }
    }, 1000);

    // console.log("[DEBUG] Successfully injected prompt");
    return true;
  } catch (error) {
    // console.error("[DEBUG] Error during injection:", error.message, error.stack);
    return false;
  }
}

// Listen for messages
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // console.log("[DEBUG] Message received in content script:", request.action);

  // Handle insertEnhancedPrompt action
  if (request.action === "insertEnhancedPrompt" && request.prompt) {
    // console.log("[Velocity Injection] 📥 Received request to insert prompt from main UI, length:", request.prompt.length);

    try {
      // console.log("[Velocity Injection] Attempting to inject prompt into input field");
      const success = injectPromptIntoInputField(request.prompt);

      if (success) {
        // console.log("[Velocity Injection] ✅ Prompt injection successful");
        // Use the animation controller to handle prompt injection
        if (window.velocityWrapperState.buttonSystem) {
          const button = window.velocityWrapperState.buttonSystem.state.currentButton;
          if (button) {
            // console.log("[Velocity Injection] Using animation controller to handle prompt injection");
            // Use the new handlePromptInjection method
            window.velocityWrapperState.buttonSystem.handlePromptInjection(button);
          } else {
            // console.log("[Velocity Injection] ⚠️ No button found for animation");
          }
        } else {
          // console.log("[Velocity Injection] ⚠️ Button system not initialized");
        }
        sendResponse({ success: true });
      } else {
        // console.log("[Velocity Injection] ❌ Initial injection failed, will retry");

        // Retry after a short delay
        setTimeout(function() {
          // console.log("[Velocity Injection] Retrying prompt injection");
          const retrySuccess = injectPromptIntoInputField(request.prompt);
          // console.log("[Velocity Injection] Retry injection result:", retrySuccess ? "✅ Success" : "❌ Failed");

          if (retrySuccess && window.velocityWrapperState.buttonSystem) {
            const button = window.velocityWrapperState.buttonSystem.state.currentButton;
            if (button) {
              // console.log("[Velocity Injection] Using animation controller for retry success");
              // Use the new handlePromptInjection method
              window.velocityWrapperState.buttonSystem.handlePromptInjection(button);
            }
          }

          sendResponse({
            success: retrySuccess,
            message: retrySuccess ? "Injection successful on retry" : "Injection failed after retry"
          });
        }, 500);
      }
    } catch (error) {
      // console.error("[DEBUG] Error in insertEnhancedPrompt handler:", error);
      sendResponse({ success: false, error: error.message });
    }

    return true; // Keep the message channel open for the async response
  }

  if (request.action === "insertHere" && request.prompt) {
    const success = injectPromptIntoInputField(request.prompt);

    if (success) {
      // Use the animation controller to handle prompt injection
      if (window.velocityWrapperState && window.velocityWrapperState.buttonSystem) {
        const button = window.velocityWrapperState.buttonSystem.state.currentButton;
        if (button) {
          // Use setTimeout to ensure the state change happens after the injection
          setTimeout(() => {
            window.velocityWrapperState.buttonSystem.handlePromptInjection(button);
          }, 100);
        }
      }
      sendResponse({ success: true });
    } else {
      sendResponse({ success: false });
    }
    return true; // Keep the message channel open
  }



  return true; // Keep the message channel open for other message types
});

// Special initialization for supported LLM platforms
if (["chatgpt.com", "claude.ai", "gemini.google.com", "perplexity.ai",
     "gamma.app", "v0.dev", "boltai.com", "grok.com", "lovable.dev", "replit.com", "app.runwayml.com"].includes(window.location.hostname)) {
  // console.log(`[DEBUG] ${window.location.hostname} detected, initializing prompt injection capabilities`);

  window.velocityLLMReady = true;
  window.velocityChatGPTReady = window.location.hostname === "chatgpt.com"; // For backward compatibility

  // Check for stored prompt to inject
  chrome.storage.local.get(["storedResponse", "suggestedLLM", "tabOpenTimestamp"], function(data) {
 

    if (data.storedResponse && data.storedResponse !== "none") {
      // Check if this is a recent tab open (within last 30 seconds)
      const isRecent = data.tabOpenTimestamp && (Date.now() - data.tabOpenTimestamp < 30000);

      if (isRecent) {
        // console.log("[DEBUG] Found recent stored prompt, injecting:", data.storedResponse.substring(0, 50) + "...");

        // Try to inject with increasing delays until successful
        const attemptInjection = (attempt = 1) => {
          // console.log(`[DEBUG] Attempting prompt injection (attempt ${attempt})`);

          // Try to inject the prompt directly
          const success = injectPromptIntoInputField(data.storedResponse);
          // console.log(`[DEBUG] Prompt injection result (attempt ${attempt}):`, success);

          if (success) {
            // console.log("[DEBUG] Prompt injection successful!");

            // Clear the timestamp to prevent re-injection but keep the prompt
            chrome.storage.local.set({
              "tabOpenTimestamp": null
            });
          } else if (attempt < 15) {
            // Retry with increasing delay - up to 15 attempts (about 15 seconds total)
            const delay = Math.min(1000, attempt * 200); // Gradually increase delay, max 1 second
            // console.log(`[DEBUG] Will retry injection in ${delay}ms (attempt ${attempt+1})`);
            setTimeout(() => attemptInjection(attempt + 1), delay);
          } else {
            // console.log("[DEBUG] Maximum injection attempts reached, giving up");
          }
        };

        // Start injection attempts after a short initial delay to allow page to load
        setTimeout(() => attemptInjection(), 2000);
      } else {
        // console.log("[DEBUG] Found stored prompt but it's not recent, not injecting");
      }
    }
  });
}

function runInputElementDetection() {
  const potentialSelectors = [
    'textarea',
    'div[contenteditable="true"]',
    'div[role="textbox"]',
    'div[class*="absolute"]',
    'div[class*="input"]',
    'div[class*="editor"]',
    'div[class*="text-input"]',
    'div[class*="secondary"]',
    'div[class*="composer"]',
    '.ProseMirror',
    '.ql-editor',
    'form textarea',
    'form input[type="text"]'
  ];

  setTimeout(() => {
    const foundElements = {};

    potentialSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        foundElements[selector] = elements.length;

        elements.forEach((el, index) => {
          const rect = el.getBoundingClientRect();
        });
      }
    });

    const possibleChatInputs = Array.from(document.querySelectorAll('div, textarea, input'))
      .filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.bottom > window.innerHeight * 0.7 &&
          rect.width > 200 &&
          rect.height > 20 &&
          window.getComputedStyle(el).display !== 'none';
      });

    const keywordElements = Array.from(document.querySelectorAll('*'))
      .filter(el => {
        const classAndId = (el.className || '') + ' ' + (el.id || '');
        return classAndId.match(/input|text|chat|message|editor|compose/i);
      });

  }, 2000);
}

function detectPlatform() {
  const currentURL = window.location.href;

  if (!window.platforms) {
    // console.error("[Velocity] Platforms not available - platforms.js may not be loaded correctly");
  }

  for (const key in window.platforms) {
    if (window.platforms[key].urlPattern && window.platforms[key].urlPattern.test(currentURL)) {
      return key;
    }
  }

  return null;
}

// Authentication handler
(function () {
  if (window.location.hostname.includes("thinkvelocity.in")) {
    function sendUserData() {
      const token = localStorage.getItem("token");
      const userName = localStorage.getItem("userName");
      const userId = localStorage.getItem("userId");
      const userEmail = localStorage.getItem("userEmail");

      if (token && userName && userId && userEmail) {
        chrome.runtime.sendMessage(
          { action: "storeUserData", token, userName, userId, userEmail },
          (response) => {
            checkAuthAndInjectButton();
          }
        );
        // Always keep enabled true when user logs in
        chrome.storage.local.set({ enabled: true });
      }

      if (!token && !userName && !userId && !userEmail) {
        // Only remove auth data but do NOT change enabled state
        chrome.storage.local.remove(
          ["token", "userName", "userId", "userEmail"],
          () => { }
        );
        // Do not set enabled to false here
      }
    }
    sendUserData();
    window.addEventListener("storage", sendUserData);

    const documentObserver = new MutationObserver(() => {
      sendUserData();
    });
    documentObserver.observe(document.body, { childList: true, subtree: true });
  }
})();

function checkAuthAndInjectButton() {
  chrome.storage.local.get(
    ["enabled", "token", "userName", "userId", "userEmail"],
    (data) => {
      // First cleanup any existing suggestion system to prevent duplicates
      cleanupSuggestionSystem();

      // Then remove the button
      removeButton();

      // Inject button if enabled is not explicitly false, regardless of auth status
      if (data.enabled !== false) {
        initializeWelcomeBox();
        injectButton();
      }
    }
  );
}

/**
 * Clean up the suggestion system completely before reinitializing
 * This prevents duplicate connections and UI elements when auth state changes
 */
function cleanupSuggestionSystem() {
  // Clean up suggestion box UI elements
  if (window.velocitySuggestions && typeof window.velocitySuggestions.cleanup === 'function') {
    window.velocitySuggestions.cleanup();
  }

  // Clean up port connection
  if (window.velocitySuggestionState && window.velocitySuggestionState.directPort) {
    try {
      // Attempt to disconnect the port
      window.velocitySuggestionState.directPort.disconnect();
    } catch (e) {
      // console.log("[Velocity] Error disconnecting port:", e);
    }
    window.velocitySuggestionState.directPort = null;
  }

  // Remove the suggestion box element
  const suggestionBox = document.getElementById('velocity-suggestion-box');
  if (suggestionBox) {
    suggestionBox.remove();
  }

  // Clear any reconnection timers
  if (window.velocitySuggestionReconnectTimer) {
    clearTimeout(window.velocitySuggestionReconnectTimer);
    window.velocitySuggestionReconnectTimer = null;
  }

  // Reset all state variables
  if (window.velocitySuggestionState) {
    window.velocitySuggestionState = {
      suggestion: "",
      all_corrections: [],
      currentCorrectionIndex: 0,
      inputElement: null,
      lastProcessedText: "",
      debounceTimer: null,
      isVisible: false,
      lastMonitoredElement: null,
      currentWord: "",
      wordStartPosition: 0,
      isBoxHidden: false,
      sendButtonPressed: false,
      userUsingBox: false,
      directPort: null,
      connected: false,
      platform: window.velocitySuggestionState.platform, // Preserve platform info
      ignoredWords: [],
      isShortText: false,
      enhancedPromptActive: false,
      enhancedPromptLength: 0
    };
  }

  // Clean up input monitoring
  if (window.inputObserver) {
    window.inputObserver.disconnect();
    window.inputObserver = null;
  }

  // Remove monitored attribute from input elements
  document.querySelectorAll('[data-velocity-monitored]').forEach(el => {
    el.removeAttribute('data-velocity-monitored');
  });

  // console.log("[Velocity] Suggestion system cleaned up successfully");
}

// Modified initSuggestionSystem function with state checking to prevent duplicate initialization
function initSuggestionSystem() {
  // Check if we already have an active suggestion system
  if (window.velocitySuggestionState &&
      window.velocitySuggestionState.directPort &&
      window.velocitySuggestionState.initialized) {
    // console.log("[Velocity] Suggestion system already initialized, skipping");
    return;
  }

  const currentPlatform = detectPlatform();

  if (!currentPlatform) {
    return;
  }

  window.velocityWrapperState = window.velocityWrapperState || {};
  window.velocityWrapperState.platform = currentPlatform;

  // Check if the user is on a free trial and has used all their credits
  chrome.storage.local.get(["FreeUser", "freeUsage", "token"], (data) => {
    // If user is on free trial and has used all 3 enhances and is not logged in
    if (data.FreeUser === true && data.freeUsage >= 3 && !data.token) {
      // console.log("[Velocity] Free trial limit reached, suggestion system disabled");
      // Show toast message for free trial ended
      return; // Exit early - don't initialize suggestion system
    }

    // Continue with normal initialization
    if (window.velocitySuggestions && typeof window.velocitySuggestions.init === 'function') {
      window.velocitySuggestions.init();
      // Mark as initialized
      if (window.velocitySuggestionState) {
        window.velocitySuggestionState.initialized = true;
      }
    } else {
      // console.warn("[Velocity] Suggestion system not available - initializing fallback");
      window.velocitySuggestions = window.velocitySuggestions || {};
      window.velocitySuggestions.init = function() {
        window.initSuggestionBox = window.initSuggestionBox || function() {};
        return this;
      };
      window.velocitySuggestions.monitor = function(config) {
        monitorInputFields(config);
      };
      window.velocitySuggestions.hideBox = function() {
        const suggestionBox = document.getElementById('velocity-suggestion-box');
        if (suggestionBox) {
          suggestionBox.style.display = 'none';
        }
      };
      window.velocitySuggestions.init();
      // Mark as initialized
      if (window.velocitySuggestionState) {
        window.velocitySuggestionState.initialized = true;
      }
    }

    if (typeof window.velocitySuggestions.monitor === 'function') {
      const platformConfig = window.platforms[currentPlatform];
      window.velocitySuggestions.monitor(platformConfig);
    }
  });
}

function initializeWelcomeBox() {
  chrome.storage.local.get(["firstInstall", "welcomeDismissed"], (data) => {
    if (data.firstInstall !== false && !data.welcomeDismissed) {
      if (data.firstInstall === undefined) {
        chrome.storage.local.set({ firstInstall: true });
      }

      if (typeof window.showWelcomeMessage === 'function') {
        window.showWelcomeMessage();
      } else {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('welcomeBox.js');
        script.onload = function () {
          if (typeof window.showWelcomeMessage === 'function') {
            window.showWelcomeMessage();
          } else {
            // console.error("[Velocity] Welcome message function still not available after script load");
          }
        };
        document.head.appendChild(script);
      }
    }
  });
}

function injectButton() {
  chrome.storage.local.get("enabled", ({ enabled }) => {
    if (enabled === false) {
      return;
    }

    const platform = window.velocityWrapperState.platform;
    if (!platform || !window.platforms) {
      return;
    }

    // Always clean up existing button and related components to prevent duplicates
    if (window.velocityWrapperState.wrapper) {
      removeButton();
    }

    // Clean up any stray hover boxes or message boxes using the integration module if available
    if (window.velocityHoverBoxIntegration && typeof window.velocityHoverBoxIntegration.cleanupAll === 'function') {
      window.velocityHoverBoxIntegration.cleanupAll();
    }
    // Fall back to direct cleanup if integration module is not available
    else {
      document.querySelectorAll('.velocity-hover-box').forEach(box => {
        if (box && box.parentNode) {
          box.parentNode.removeChild(box);
        }
      });

      document.querySelectorAll('.velocity-message-box').forEach(box => {
        if (box && box.parentNode) {
          box.parentNode.removeChild(box);
        }
      });

      // Reset hover box state
      if (window.velocityHoverBoxState) {
        window.velocityHoverBoxState.initialized = false;
        window.velocityHoverBoxState.activeHoverBox = null;
      }
    }

    const platformConfig = window.platforms[platform];
    if (!platformConfig || !platformConfig.textAreaSelector) {
      // console.error("[Velocity] Invalid platform configuration");
      return;
    }

    // Track Button Injected event with Mixpanel
    chrome.runtime.sendMessage({
      action: "trackMixpanelEvent",
      eventName: "Button Injected",
      properties: {
        platform: platform,
        url: window.location.href
      }
    });

    findInputContainer(platformConfig.textAreaSelector);
  });
}

function findInputContainer(selector) {
  const findContainerForInput = (inputElement) => {
    if (!inputElement) return null;

    const inputRect = inputElement.getBoundingClientRect();

    let container = inputElement.parentElement;
    let depth = 0;
    const MAX_DEPTH = 8;

    const completeInputClassPatterns = [
      'input-container', 'chat-input', 'composer', 'message-composer',
      'chat-composer', 'input-box', 'text-box', 'message-box',
      'editor-container', 'input-wrapper', 'chat-box', 'textarea-container',
      'text-input-container', 'input-area', 'chat-input-container',
      'prompt-container', 'input-form', 'form', 'chat-form', 'message-form',
      'composer-container', 'message-input', 'text-input', 'editor-wrapper',
      'main-input', 'chat-input-panel', 'conversation-input', 'message-creator'
    ];

    while (container && depth < MAX_DEPTH) {
      const className = container.className.toLowerCase();
      const id = (container.id || '').toLowerCase();

      const hasInputContainerClass = completeInputClassPatterns.some(pattern =>
        className.includes(pattern) || id.includes(pattern)
      );

      const isFormElement = container.tagName === 'FORM' ||
        (container.getAttribute('role') === 'form') ||
        className.includes('form');

      const style = window.getComputedStyle(container);
      const hasBorder = style.border !== 'none' && style.border !== '';
      const hasBackground = style.backgroundColor !== 'transparent' && style.backgroundColor !== 'rgba(0, 0, 0, 0)';
      const hasBoxShadow = style.boxShadow !== 'none' && style.boxShadow !== '';
      const isFlexOrGrid = style.display.includes('flex') || style.display.includes('grid');

      const hasButtons = container.querySelectorAll('button, [role="button"]').length > 0;

      const containerRect = container.getBoundingClientRect();
      const isSignificantlyLarger = containerRect.width > (inputRect.width * 1.2) &&
        containerRect.height > (inputRect.height * 1.1);

      if ((hasInputContainerClass || isFormElement) &&
        (hasButtons || (isSignificantlyLarger && (hasBorder || hasBackground || hasBoxShadow || isFlexOrGrid)))) {

        const textArea = findTextAreaInContainer(container, inputElement);

        if (textArea) {
          const textAreaStyle = window.getComputedStyle(textArea);
          const hasOverflow = textAreaStyle.overflow === 'auto' ||
                             textAreaStyle.overflow === 'scroll' ||
                             textAreaStyle.overflowY === 'auto' ||
                             textAreaStyle.overflowY === 'scroll';

          if (hasOverflow) {
            return {
              container: container,
              inputElement: inputElement,
              textArea: textArea,
              rect: container.getBoundingClientRect(),
              textAreaRect: container.getBoundingClientRect()
            };
          }
        }

        return {
          container: container,
          inputElement: inputElement,
          textArea: textArea || inputElement,
          rect: container.getBoundingClientRect(),
          textAreaRect: (textArea || inputElement).getBoundingClientRect()
        };
      }

      container = container.parentElement;
      depth++;
    }

    container = inputElement.parentElement;
    depth = 0;

    while (container && depth < MAX_DEPTH) {
      const childButtons = container.querySelectorAll('button, svg, img, [role="button"]').length;
      const childDivs = container.querySelectorAll('div').length;
      const childSpans = container.querySelectorAll('span').length;
      const childIconElements = container.querySelectorAll('svg, i, .icon, [class*="icon"]').length;

      const hasSendButton = Array.from(container.querySelectorAll('button, [role="button"]')).some(button => {
        const text = button.textContent.toLowerCase();
        const classes = button.className.toLowerCase();
        return text.includes('send') ||
          classes.includes('send') ||
          classes.includes('submit') ||
          button.querySelector('svg') !== null;
      });

      const hasFormatting = container.querySelectorAll('[class*="format"], [class*="toolbar"], [class*="counter"]').length > 0;

      const style = window.getComputedStyle(container);
      const hasBorder = style.border !== 'none' && style.border !== '';
      const hasBackground = style.backgroundColor !== 'transparent' && style.backgroundColor !== 'rgba(0, 0, 0, 0)';
      const hasShadow = style.boxShadow !== 'none' && style.boxShadow !== '';
      const hasPadding = parseInt(style.padding) > 0 || parseInt(style.paddingTop) > 0;
      const hasRoundedCorners = parseInt(style.borderRadius) > 0;

      const containerRect = container.getBoundingClientRect();
      const isWider = containerRect.width > inputRect.width + 40;
      const isAtBottom = containerRect.bottom > window.innerHeight * 0.6;

      let containerScore = 0;
      if (childButtons > 0) containerScore += 3;
      if (hasSendButton) containerScore += 5;
      if (hasFormatting) containerScore += 4;
      if (childIconElements > 0) containerScore += 2;
      if (hasBorder) containerScore += 2;
      if (hasBackground) containerScore += 2;
      if (hasShadow) containerScore += 2;
      if (hasRoundedCorners) containerScore += 3;
      if (hasPadding) containerScore += 1;
      if (isWider) containerScore += 2;
      if (isAtBottom) containerScore += 2;
      if (style.display.includes('flex') || style.display.includes('grid')) containerScore += 2;
      if (childDivs > 3 || childSpans > 2) containerScore += 1;

      if (containerScore >= 6) {
        const textArea = findTextAreaInContainer(container, inputElement);

        return {
          container: container,
          inputElement: inputElement,
          textArea: textArea || inputElement,
          rect: containerRect,
          textAreaRect: (textArea || inputElement).getBoundingClientRect()
        };
      }

      container = container.parentElement;
      depth++;
    }

    container = inputElement.parentElement;
    depth = 0;
    let bestContainer = {
      element: inputElement,
      rect: inputRect,
      score: 0
    };

    while (container && depth < MAX_DEPTH) {
      const containerRect = container.getBoundingClientRect();

      const widthDiff = containerRect.width - inputRect.width;
      const heightDiff = containerRect.height - inputRect.height;
      const isTooLarge = containerRect.width > window.innerWidth * 0.95 ||
        containerRect.height > window.innerHeight * 0.8;

      if (!isTooLarge) {
        let score = 0;

        if (widthDiff > 20 && widthDiff < 300 && heightDiff > 10 && heightDiff < 200) {
          score = (widthDiff + heightDiff) / 2;
        }

        const style = window.getComputedStyle(container);
        if (style.border !== 'none' && style.border !== '') score += 30;
        if (style.borderRadius && parseInt(style.borderRadius) > 0) score += 20;
        if (style.boxShadow !== 'none' && style.boxShadow !== '') score += 20;
        if (style.backgroundColor !== 'transparent' && style.backgroundColor !== 'rgba(0, 0, 0, 0)') score += 15;

        if (containerRect.bottom > window.innerHeight * 0.6) {
          score += 25;
        }

        const hasButtons = container.querySelectorAll('button, [role="button"], svg, [class*="icon"]').length > 0;
        if (hasButtons) score += 40;

        if (score > bestContainer.score) {
          bestContainer = {
            element: container,
            rect: containerRect,
            score: score
          };
        }
      }

      container = container.parentElement;
      depth++;
    }

    const textArea = findTextAreaInContainer(bestContainer.element, inputElement);

    return {
      container: bestContainer.element,
      inputElement: inputElement,
      textArea: textArea || inputElement,
      rect: bestContainer.rect,
      textAreaRect: (textArea || inputElement).getBoundingClientRect()
    };
  };

  let inputElement = null;

  if (selector.includes(',')) {
    const selectors = selector.split(',').map(s => s.trim());
    for (const singleSelector of selectors) {
      const element = document.querySelector(singleSelector);
      if (element) {
        inputElement = element;
        break;
      }
    }
  } else {
    inputElement = document.querySelector(selector);
  }

  if (inputElement) {
    const containerInfo = findContainerForInput(inputElement);
    if (containerInfo) {
      createFullWrapper(containerInfo);
      window.currentInputBox = containerInfo.textArea;
    } else {
      createFullWrapper({
        container: inputElement,
        inputElement: inputElement,
        textArea: inputElement,
        rect: inputElement.getBoundingClientRect(),
        textAreaRect: inputElement.getBoundingClientRect()
      });
    }
  } else {
    let debounceTimer = null;
    const inputObserver = new MutationObserver((mutations) => {
      if (debounceTimer) clearTimeout(debounceTimer);

      debounceTimer = setTimeout(() => {
        let delayedInput = null;
        if (selector.includes(',')) {
          const selectors = selector.split(',').map(s => s.trim());
          for (const singleSelector of selectors) {
            const element = document.querySelector(singleSelector);
            if (element) {
              delayedInput = element;
              break;
            }
          }
        } else {
          delayedInput = document.querySelector(selector);
        }

        if (delayedInput) {
          const containerInfo = findContainerForInput(delayedInput);
          if (containerInfo) {
            createFullWrapper(containerInfo);
            window.currentInputBox = containerInfo.textArea;
          } else {
            createFullWrapper({
              container: delayedInput,
              inputElement: delayedInput,
              textArea: delayedInput,
              rect: delayedInput.getBoundingClientRect(),
              textAreaRect: delayedInput.getBoundingClientRect()
            });
          }

          inputObserver.disconnect();
        }
      }, 300);
    });

    window._velocityObservers = window._velocityObservers || [];
    window._velocityObservers.push(inputObserver);

    inputObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });

    setTimeout(() => {
      if (!window.velocityWrapperState.wrapper) {
        inputObserver.disconnect();
        tryAlternativeContainers();
      }
    }, 1000);
  }
}

function findTextAreaInContainer(container, fallbackInput) {
  if (!container) return fallbackInput;

  const editableSelectors = [
    '.ProseMirror',
    '.ql-editor',
    '.CodeMirror',
    '.tox-edit-area',
    'textarea',
    'div[contenteditable="true"]',
    'div[role="textbox"]',
    'input[type="text"]',
    'div[class*="editor"]',
    'div[class*="input"]',
    'div[class*="textarea"]',
    'div[class*="text-field"]',
    '[contenteditable]',
    '[role="textbox"]'
  ];

  for (const selector of editableSelectors) {
    const elements = container.querySelectorAll(selector);
    if (elements.length > 0) {
      if (elements.length > 1) {
        const elementsArray = Array.from(elements);

        const visibleElements = elementsArray.filter(el => {
          const rect = el.getBoundingClientRect();
          const style = window.getComputedStyle(el);
          return rect.width > 50 &&
            rect.height > 20 &&
            style.display !== 'none' &&
            style.visibility !== 'hidden';
        });

        if (visibleElements.length > 0) {
          return visibleElements.reduce((largest, current) => {
            const largestRect = largest.getBoundingClientRect();
            const currentRect = current.getBoundingClientRect();

            const largestArea = largestRect.width * largestRect.height;
            const currentArea = currentRect.width * currentRect.height;

            return currentArea > largestArea ? current : largest;
          });
        }
      }

      return elements[0];
    }
  }

  const allElements = container.querySelectorAll('*');
  let bestTextArea = null;
  let bestScore = 0;

  for (const el of allElements) {
    const rect = el.getBoundingClientRect();
    const style = window.getComputedStyle(el);

    if (rect.width < 50 ||
      rect.height < 20 ||
      style.display === 'none' ||
      style.visibility === 'hidden') {
      continue;
    }

    let score = 0;

    if (el.tagName === 'TEXTAREA') score += 100;
    if (el.tagName === 'INPUT' && el.type === 'text') score += 90;
    if (el.getAttribute('contenteditable') === 'true') score += 100;
    if (el.getAttribute('role') === 'textbox') score += 80;

    const className = el.className.toLowerCase();
    if (className.includes('editor')) score += 70;
    if (className.includes('input')) score += 60;
    if (className.includes('text')) score += 40;
    if (className.includes('content')) score += 30;

    if (el.getAttribute('placeholder')) score += 60;

    if (style.backgroundColor !== 'transparent' &&
      style.backgroundColor !== 'rgba(0, 0, 0, 0)') score += 20;
    if (style.border !== 'none' && style.border !== '') score += 30;
    if (parseInt(style.borderRadius) > 0) score += 10;

    const area = rect.width * rect.height;
    const containerRect = container.getBoundingClientRect();
    const containerArea = containerRect.width * containerRect.height;

    if (area > containerArea * 0.3 && area < containerArea * 0.9) {
      score += 50;
    }

    if (score > bestScore) {
      bestScore = score;
      bestTextArea = el;
    }
  }

  if (bestScore > 100) {
    return bestTextArea;
  }

  return fallbackInput;
}

function createFullWrapper(containerInfo) {
  const { container, inputElement, textArea, rect, textAreaRect } = containerInfo;

  const existingWrappers = document.querySelectorAll('.velocity-wrapper');
  existingWrappers.forEach(wrapper => {
    if (wrapper && wrapper.parentNode) {
      wrapper.parentNode.removeChild(wrapper);
    }
  });

  const existingButtons = document.querySelectorAll('.velocity-button-container');
  existingButtons.forEach(button => {
    if (button && button.parentNode) {
      button.parentNode.removeChild(button);
    }
  });

  // Clean up quality analyzer if it exists
  if (window.velocityQualityAnalyzer && typeof window.velocityQualityAnalyzer.cleanup === 'function') {
    window.velocityQualityAnalyzer.cleanup();
  }

  const containerRect = container.getBoundingClientRect();


  const paddingTop = 40;
  const paddingRight = 60;
  const paddingBottom = 60;
  const paddingLeft = 60;

  const wrapper = document.createElement('div');
  wrapper.className = 'velocity-wrapper';
  wrapper.style.cssText = `
    position: fixed;
    top: ${containerRect.top - paddingTop}px;
    left: ${containerRect.left - paddingLeft}px;
    width: ${containerRect.width + (paddingLeft + paddingRight)}px;
    height: ${containerRect.height + (paddingTop + paddingBottom)}px;
    z-index: 9998;
    pointer-events: none;
    // border: 1px dashed rgba(0, 136, 255, 0.2);
    box-sizing: border-box;
  `;

  let positionDebounceTimer = null;
  const updateWrapperPosition = () => {
    if (positionDebounceTimer) clearTimeout(positionDebounceTimer);

    positionDebounceTimer = setTimeout(() => {
      if (!container || !document.body.contains(container)) {
        if (wrapper && wrapper.parentNode) {
          wrapper.parentNode.removeChild(wrapper);
        }
        return;
      }

      const newContainerRect = container.getBoundingClientRect();

      wrapper.style.top = `${newContainerRect.top - paddingTop}px`;
      wrapper.style.left = `${newContainerRect.left - paddingLeft}px`;
      wrapper.style.width = `${newContainerRect.width + (paddingLeft + paddingRight)}px`;
      wrapper.style.height = `${newContainerRect.height + (paddingTop + paddingBottom)}px`;

      const button = window.velocityWrapperState.button;
      if (button) {
        const position = button.dataset.anchorPosition || 'top-right';
        const anchors = window.velocityWrapperState.anchors;
        let anchor = null;

        if (position === 'top-left') anchor = anchors.topLeft;
        else if (position === 'top-right') anchor = anchors.topRight;
        else if (position === 'bottom-left') anchor = anchors.bottomLeft;
        else if (position === 'bottom-right') anchor = anchors.bottomRight;

        if (anchor) {
          snapButtonToAnchor(button, anchor);
        }
      }
    }, 100);
  };

  const mutationObserver = new MutationObserver(() => {
    updateWrapperPosition();
  });

  mutationObserver.observe(container, {
    attributes: true,
    attributeFilter: ['style', 'class']
  });

  if (textArea && textArea !== container) {
    mutationObserver.observe(textArea, {
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  const resizeObserver = new ResizeObserver(() => {
    updateWrapperPosition();
  });

  const scrollHandler = () => {
    updateWrapperPosition();
  };

  resizeObserver.observe(container);
  if (textArea && textArea !== container) {
    resizeObserver.observe(textArea);
  }

  window.addEventListener('scroll', scrollHandler, { passive: true });
  let parent = container.parentElement;
  while (parent) {
    const style = window.getComputedStyle(parent);
    if (style.overflow === 'auto' ||
      style.overflow === 'scroll' ||
      style.overflowX === 'auto' ||
      style.overflowX === 'scroll' ||
      style.overflowY === 'auto' ||
      style.overflowY === 'scroll') {
      parent.addEventListener('scroll', scrollHandler, { passive: true });
    }
    parent = parent.parentElement;
  }

  window.addEventListener('resize', scrollHandler, { passive: true });

  window._velocityObservers = window._velocityObservers || [];
  window._velocityObservers.push(resizeObserver);
  window._velocityObservers.push(mutationObserver);
  window._velocityScrollHandlers = window._velocityScrollHandlers || [];
  window._velocityScrollHandlers.push({
    element: window,
    handler: scrollHandler
  });
  window._velocityScrollHandlers.push({
    element: window,
    handler: window.addEventListener('resize', scrollHandler, { passive: true })
  });

  window.velocityWrapperState.wrapper = wrapper;
  window.velocityWrapperState.container = container;
  window.velocityWrapperState.inputBox = textArea;

  createAnchorPoints(wrapper);

  const buttonContainer = createDraggableButton(wrapper);

  document.body.appendChild(wrapper);

  updateWrapperPosition();

  return wrapper;
}

function createAnchorPoints(wrapper) {
  // Create anchor elements
  const anchors = {
    topLeft: createAnchor('top-left'),
    topRight: createAnchor('top-right'),
    bottomLeft: createAnchor('bottom-left'),
    bottomRight: createAnchor('bottom-right')
  };

  // Get the current platform
  const platform = window.velocityWrapperState.platform;

  // Default anchor positions (fallback if platform-specific positions aren't available)
  let anchorPositions = {
    topLeft: { top: "0", left: "0%" },
    topRight: { top: "0", right: "0%" },
    bottomLeft: { bottom: "0%", left: "0%" },
    bottomRight: { bottom: "14%", right: "17%" },
    defaultAnchor: "topRight"
  };

  // Get platform-specific anchor positions if available
  if (platform && window.platforms && window.platforms[platform] && window.platforms[platform].anchorPositions) {
    anchorPositions = window.platforms[platform].anchorPositions;
    // console.log(`[Velocity] Using platform-specific anchor positions for ${platform}`);
  } else {
    // console.log(`[Velocity] Using default anchor positions (platform: ${platform})`);
  }

  // Apply positions to anchor elements
  for (const [key, anchor] of Object.entries(anchors)) {
    if (anchorPositions[key]) {
      // Apply each CSS property from the position object
      Object.entries(anchorPositions[key]).forEach(([prop, value]) => {
        anchor.style[prop] = value;
      });
    }
  }

  // Add anchors to the wrapper
  Object.values(anchors).forEach(anchor => {
    wrapper.appendChild(anchor);
  });

  // Store anchors and default anchor position in the state
  window.velocityWrapperState.anchors = anchors;
  window.velocityWrapperState.defaultAnchor = anchorPositions.defaultAnchor || "topRight";
}

function handleButtonClick(event, button, innerDiv) {
  event.stopPropagation();
  event.preventDefault();

  // Hide the quality indicator when button is clicked
  if (window.velocityQualityState && window.velocityQualityState.indicator) {
    window.velocityQualityState.indicator.style.display = 'none';
    // Store that the indicator was hidden by a button click
    window.velocityQualityState.hiddenByButtonClick = true;
    // console.log("[Velocity] Quality indicator hidden on button click");
  }

  // Hide any existing prompt review box when button is clicked
  if (window.togglePromptReviewBox) {
    window.togglePromptReviewBox(false);
  }

  // Ensure animation system is initialized
  if (!window.velocityWrapperState.buttonSystem) {
    const platformKey = window.velocityWrapperState.platform;
    const platformConfig = window.platforms ? window.platforms[platformKey] : null;
    initializeButtonAnimationSystem(platformConfig, button);
  }

  // Get the input box and prompt length for tracking
  const platform = window.velocityWrapperState.platform;
  const inputBox = window.velocityWrapperState.inputBox;
  let promptLength = 0;

  // Log the platform we're using
  //console.log("[Velocity DEBUG] Using platform:", platform);

  if (inputBox) {
    if (inputBox.tagName === "TEXTAREA") {
      promptLength = inputBox.value.trim().length;
    } else if (inputBox.hasAttribute("contenteditable")) {
      promptLength = inputBox.innerText.trim().length;
    } else {
      promptLength = inputBox.textContent.trim().length;
    }
  }

  // Track Platform Button Clicked event with Mixpanel
  chrome.runtime.sendMessage({
    action: "trackMixpanelEvent",
    eventName: "Platform Button Clicked",
    properties: {
      platform: platform,
      prompt_length: promptLength
    }
  });

  // First check if user is on free trial and has reached the limit (3 uses)
  chrome.storage.local.get(["FreeUser", "freeUsage", "token"], (freeUserData) => {
    // Check if user is on free trial (FreeUser === true) and has used all 3 enhances
    // Also check they are not logged in (no token)
    if (freeUserData.FreeUser === true && freeUserData.freeUsage >= 3 && !freeUserData.token) {
      // console.log("[Velocity] Free trial limit reached, showing notification from button click");

      // Use the centralized function to show the popup with disabled animations
      showTrialFinishedPopupWithDisabledAnimations();
      return; // Stop further processing - no animations or API requests
    }

    // Continue with normal button click behavior for users with remaining credits
    const platform = window.velocityWrapperState.platform;
    const inputBox = window.velocityWrapperState.inputBox;

    if (!inputBox) {
      // console.error("[Velocity] Input box not found");
      return;
    }

    let userPrompt = "";
    if (inputBox.tagName === "TEXTAREA") {
      userPrompt = inputBox.value.trim();
    } else if (inputBox.hasAttribute("contenteditable")) {
      userPrompt = inputBox.innerText.trim();
    } else {
      userPrompt = inputBox.textContent.trim();
    }

    if (!userPrompt) {
      if (window.velocityWrapperState.buttonSystem) {
        window.velocityWrapperState.buttonSystem.messageSystem.showMessage('warning', {
          text: 'Please enter some text before enhancing!',
          type: 'warning',
          button: button,
          positionStrategy: 'relativeToButton',
          duration: 3000
        });

        // Restore quality indicator after showing the warning message
        setTimeout(() => {
          if (window.velocityQualityState && window.velocityQualityState.indicator && window.velocityQualityState.hiddenByButtonClick) {
            window.velocityQualityState.indicator.style.display = '';
            window.velocityQualityState.hiddenByButtonClick = false;
            // console.log("[Velocity] Quality indicator restored after empty input warning");
          }

          // Remove the clicked-by-user class to allow indicator to show again
          if (button) {
            button.classList.remove('clicked-by-user');
          }
        }, 3100); // Wait slightly longer than the message duration
      } else {
        if (window.velocityAnimations && window.velocityAnimations.createMessageBox) {
          const msgBox = window.velocityAnimations.createMessageBox(
            'Please enter some text before enhancing!',
            'warning',
            button
          );

          setTimeout(() => {
            if (msgBox && msgBox.parentNode) {
              msgBox.parentNode.removeChild(msgBox);
            }

            // Restore quality indicator after showing the warning message
            if (window.velocityQualityState && window.velocityQualityState.indicator && window.velocityQualityState.hiddenByButtonClick) {
              window.velocityQualityState.indicator.style.display = '';
              window.velocityQualityState.hiddenByButtonClick = false;
              // console.log("[Velocity] Quality indicator restored after empty input warning");
            }

            // Remove the clicked-by-user class to allow indicator to show again
            if (button) {
              button.classList.remove('clicked-by-user');
            }
          }, 3000);
        } else {
          alert("Please enter some text before enhancing!");

          // Restore quality indicator after showing the alert
          if (window.velocityQualityState && window.velocityQualityState.indicator && window.velocityQualityState.hiddenByButtonClick) {
            window.velocityQualityState.indicator.style.display = '';
            window.velocityQualityState.hiddenByButtonClick = false;
            // console.log("[Velocity] Quality indicator restored after empty input warning");
          }

          // Remove the clicked-by-user class to allow indicator to show again
          if (button) {
            button.classList.remove('clicked-by-user');
          }
        }
      }
      return;
    }

    chrome.storage.local.get(["selectedStyle"], (result) => {
      let selectedStyle = result.selectedStyle || "Descriptive";

      if (window.velocitySuggestions && window.velocitySuggestions.hideBox) {
        window.velocitySuggestions.hideBox();
      }

      window.velocitySuggestionState = window.velocitySuggestionState || {};
      window.velocitySuggestionState.isBoxHidden = false;
      window.velocitySuggestionState.sendButtonPressed = false;

      // Make sure to pass the selectedStyle as writing_style
      sendEnhanceRequest(userPrompt, selectedStyle, platform, null, innerDiv);
    });



    // Request prompt analysis in parallel
    chrome.storage.local.get(["selectedStyle"], (result) => {
      const selectedStyle = result.selectedStyle || "Descriptive";
      //console.log("[Velocity DEBUG] Initiating prompt analysis with prompt length:", userPrompt.length);
      // console.log("[Velocity DEBUG] Initiating prompt analysis with prompt length:", selectedStyle);
      // Create a variable to store the analysis response at a higher scope
      // so it can be accessed by both API callbacks
      window.savedAnalysisResponse = null;

      // Now call the analyze endpoint and store the response for later use
      chrome.runtime.sendMessage(
        {
          action: "promptAnalysis",
          prompt: userPrompt,
          style: selectedStyle, // Keep this as style for existing API
        },
        (userPromptAnalysisResponse) => {
          if (userPromptAnalysisResponse && userPromptAnalysisResponse.success) {
            //console.log("[Velocity DEBUG] Analysis response received successfully:", userPromptAnalysisResponse);
            //console.log("[Velocity DEBUG] Analysis metrics:", userPromptAnalysisResponse.metrics);
            //console.log("[Velocity DEBUG] Analysis recommendations:", userPromptAnalysisResponse.recommendations);

            // Save the response for later use in the window variable
            window.savedAnalysisResponse = userPromptAnalysisResponse;
            // console.log("[Velocity DEBUG] Saved analysis response to window.savedAnalysisResponse");
          } else {
            // console.error("[Velocity DEBUG] Analysis request failed:", userPromptAnalysisResponse);
            // console.error("[Velocity DEBUG] Error details:", userPromptAnalysisResponse?.error || "No error details");
          }
        }
      );

      chrome.runtime.sendMessage(
        {
          action: "EnhancePromptV2",
          prompt: userPrompt,
          style: selectedStyle, // Keep this as style for existing API
          platform: platform,
        },
        (response) => {
          if (response && response.success) {
            // We'll use this response to show the analysis UI later
            const analysisResponse = response;

            // Wait a moment for the enhanced prompt to be ready
            setTimeout(() => {
              // Show review UI with the analysis data
              if (window.togglePromptReviewBox) {
                chrome.storage.local.get(["selectedStyle"], (result) => {
                  const currentStyle = result.selectedStyle || "Descriptive";

                  // Use the saved analysis response if available, otherwise create a fallback structure
                  let analysisData;

                  if (window.savedAnalysisResponse && window.savedAnalysisResponse.success) {
                    // Use the direct API response which has the correct structure
                    analysisData = {
                      // Include the original API response structure
                      ...window.savedAnalysisResponse,
                      // Add additional metadata
                      originalPrompt: userPrompt,
                      streamEnhancedPrompt: inputBox.value || inputBox.innerText || "",
                      platform: platform,
                      style: currentStyle
                    };
                  } else {
                    // Fallback to the old structure if the analysis response is not available
                    analysisData = {
                      analysis: analysisResponse.data.data,
                      metrics: analysisResponse.data.metrics || analysisResponse.data.data.metrics || {},
                      recommendations: analysisResponse.data.recommendations || analysisResponse.data.data.recommendations || {},
                      framework_analysis: analysisResponse.data.framework_analysis || analysisResponse.data.data.framework_analysis || {},
                      originalPrompt: userPrompt,
                      streamEnhancedPrompt: inputBox.value || inputBox.innerText || "",
                      platform: platform,
                      style: currentStyle,
                    };
                  }
                  //console.log("[Velocity DEBUG] Analysis data structure being passed to UI:", {
                  //   hasMetrics: !!analysisData.metrics,
                  //   hasRecommendations: !!analysisData.recommendations,
                  //   hasFrameworkAnalysis: !!analysisData.framework_analysis,
                  //   metricsKeys: analysisData.metrics ? Object.keys(analysisData.metrics) : [],
                  //   recommendationsKeys: analysisData.recommendations ? Object.keys(analysisData.recommendations) : [],
                  //   fullData: analysisData
                  // });
                  const enhancedPromptData = {
                    enhanced_prompt: analysisResponse.data.data.enhanced_prompt,
                    questions:
                      analysisResponse.data.data.questions ||
                      [],
                  };
                  //console.log("ddddddddddddddddddddddddddd",enhancedPromptData);

                  // Show the UI with the data
                  window.togglePromptReviewBox(true, analysisData);

                  // Also update enhanced prompt data if available
                  const existingUI = document.querySelector(".analysis-ui-container");
                  if (existingUI && existingUI.updateEnhancedPrompt) {
                    existingUI.updateEnhancedPrompt(enhancedPromptData);
                  }
                });
              } else {
                // Fallback to old method if togglePromptReviewBox is not available
                const reviewUI = window.initAnalysisUI ? window.initAnalysisUI() : null;
                if (reviewUI) {
                  document.body.appendChild(reviewUI);

                  reviewUI.addEventListener('close', () => {
                    setTimeout(() => {
                      if (window.updatePromptReviewStatus) {
                        window.updatePromptReviewStatus(false);
                      }
                    }, 50);
                  });

                  chrome.storage.local.get(["selectedStyle"], (result) => {
                    const currentStyle = result.selectedStyle || "Descriptive";

                    // Use the saved analysis response if available, otherwise create a fallback structure
                    let analysisData;

                    if (window.savedAnalysisResponse && window.savedAnalysisResponse.success) {
                      // Use the direct API response which has the correct structure
                      analysisData = {
                        // Include the original API response structure
                        ...window.savedAnalysisResponse,
                        // Add additional metadata
                        originalPrompt: userPrompt,
                        EnhancedPromptV2: inputBox.value || inputBox.innerText || "",
                        platform: platform,
                        style: currentStyle
                      };
                    } else {
                      // Fallback to the old structure if the analysis response is not available
                      analysisData = {
                        analysis: analysisResponse.data.data,
                        metrics: analysisResponse.data.metrics || analysisResponse.data.data.metrics || {},
                        recommendations: analysisResponse.data.recommendations || analysisResponse.data.data.recommendations || {},
                        framework_analysis: analysisResponse.data.framework_analysis || analysisResponse.data.data.framework_analysis || {},
                        originalPrompt: userPrompt,
                        EnhancedPromptV2: inputBox.value || inputBox.innerText || "",
                        platform: platform,
                        style: currentStyle,
                      };
                    }

                    // Log the analysis data structure for debugging
                    //console.log("[Velocity DEBUG] Fallback method - Analysis data structure:", {
                    //   hasMetrics: !!analysisData.metrics,
                    //   hasRecommendations: !!analysisData.recommendations,
                    //   hasFrameworkAnalysis: !!analysisData.framework_analysis,
                    //   metricsKeys: analysisData.metrics ? Object.keys(analysisData.metrics) : [],
                    //   recommendationsKeys: analysisData.recommendations ? Object.keys(analysisData.recommendations) : [],
                    //   fullData: analysisData
                    // });

                    const enhancedPromptData = {
                      questions:
                        analysisResponse.data.data.questions ||
                        analysisResponse.data.data.questions ||
                        [],
                    };

                    reviewUI.show(analysisData, enhancedPromptData);

                    if (window.updatePromptReviewStatus) {
                      window.updatePromptReviewStatus(true);
                    }
                  });
                }
              }
            }, 1500); // Wait 1.5 seconds for the enhanced prompt to be ready
          }
        }
      );
    });
  });
}

function setupSuggestionBoxIntegration() {
  if (window.velocitySuggestions && typeof window.velocitySuggestions.init === 'function') {
    const platform = window.velocityWrapperState.platform;
    const platformConfig = window.platforms ? window.platforms[platform] : null;

    window.velocitySuggestions.init();

    if (platformConfig && typeof window.velocitySuggestions.monitor === 'function') {
      window.velocitySuggestions.monitor(platformConfig);
    }
  }

  if (window.velocityWrapperState.button) {
    const button = window.velocityWrapperState.button;

    if (window.velocitySuggestionState) {
      window.velocitySuggestionState.velocityButton = button;

      const originalHideMethod = window.velocitySuggestions.hideBox;
      if (originalHideMethod) {
        window.velocitySuggestions.hideBox = function() {
          originalHideMethod();

          setTimeout(() => {
            if (window.velocitySuggestionState) {
              window.velocitySuggestionState.isBoxHidden = false;
            }
          }, 1000);
        };
      }
    }
  }
}

function integrateWithSuggestionSystem() {
  setupSuggestionBoxIntegration();

  if (window.velocityWrapperState && window.velocityWrapperState.button) {
    const button = window.velocityWrapperState.button;

    button.addEventListener('mouseenter', () => {
      if (window.velocitySuggestionState && window.velocitySuggestionState.isBoxHidden) {
        window.velocitySuggestionState.isBoxHidden = false;
      }
    });
  }
}

function createAnchor(position) {
  const anchor = document.createElement('div');
  anchor.className = `velocity-anchor velocity-anchor-${position}`;
  anchor.dataset.position = position;

  // Initially hide the anchor points
  anchor.style.cssText = `
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(0, 136, 255, 0.5);
    border-radius: 50%;
    z-index: 9999;
    pointer-events: auto;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s, opacity 0.2s;
    opacity: 0; /* Start hidden */
    visibility: hidden; /* Start hidden */
  `;

  anchor.addEventListener('mouseenter', () => {
    anchor.style.backgroundColor = 'rgba(0, 136, 255, 0.8)';
    anchor.style.transform = 'scale(1.2)';
  });

  anchor.addEventListener('mouseleave', () => {
    anchor.style.backgroundColor = 'rgba(0, 136, 255, 0.5)';
    anchor.style.transform = 'scale(1)';
  });

  return anchor;
}

function createDraggableButton(wrapper) {
  const buttonContainer = document.createElement("div");
  buttonContainer.className = "velocity-button-container custom-injected-button";
  buttonContainer.style.cssText = `
    position: fixed;
    z-index: 10000;
    pointer-events: auto;
    cursor: move;
  `;

  const button = document.createElement("button");
  button.className = "velocity-button";
  button.style.cssText = `
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    background-color: black;
    height: 36px;
    width: 36px;
    transition: opacity 0.2s;
    border: none;
    outline: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    cursor: pointer;
    pointer-events: auto;
    z-index: 10;
  `;

  button.addEventListener("mouseenter", () => (button.style.opacity = "0.7"));
  button.addEventListener("mouseleave", () => (button.style.opacity = "1"));

  const innerDiv = document.createElement('div');
  innerDiv.className = 'velocity-button-inner';

  const coinImage = document.createElement("img");
  coinImage.src = chrome.runtime.getURL("assets/Velocity.png");
  coinImage.style.height = "36px";
  coinImage.style.width = "36px";
  innerDiv.appendChild(coinImage);

  button.appendChild(innerDiv);
  buttonContainer.appendChild(button);

  let isDragging = false;
  let startX, startY;
  let offsetX = 0, offsetY = 0;

  buttonContainer.addEventListener('mousedown', (e) => {
    isDragging = true;
    startX = e.clientX;
    startY = e.clientY;

    const rect = buttonContainer.getBoundingClientRect();
    offsetX = startX - rect.left;
    offsetY = startY - rect.top;

    window.velocityWrapperState.isDragging = true;
    window.velocityWrapperState.dragOffsetX = offsetX;
    window.velocityWrapperState.dragOffsetY = offsetY;

    // Show anchor points when dragging starts
    const anchors = window.velocityWrapperState.anchors;
    if (anchors) {
      Object.values(anchors).forEach(anchor => {
        anchor.style.opacity = '1';
        anchor.style.visibility = 'visible';
      });
    }

    // Hide any message boxes during dragging
    const messageBoxes = document.querySelectorAll('.velocity-message-box');
    messageBoxes.forEach(box => {
      box.style.display = 'none';
    });

    e.preventDefault();
  });

  document.addEventListener('mousemove', (e) => {
    if (!isDragging) return;

    const wrapperRect = wrapper.getBoundingClientRect();

    let newX = e.clientX - offsetX;
    let newY = e.clientY - offsetY;

    newX = Math.max(wrapperRect.left, Math.min(newX, wrapperRect.right - buttonContainer.offsetWidth));
    newY = Math.max(wrapperRect.top, Math.min(newY, wrapperRect.bottom - buttonContainer.offsetHeight));

    buttonContainer.style.left = newX + 'px';
    buttonContainer.style.top = newY + 'px';
  });

  document.addEventListener('mouseup', (e) => {
    if (!isDragging) return;
    isDragging = false;
    window.velocityWrapperState.isDragging = false;

    snapToNearestAnchor(buttonContainer, wrapper);

    // Hide anchor points when dragging stops
    const anchors = window.velocityWrapperState.anchors;
    if (anchors) {
      Object.values(anchors).forEach(anchor => {
        anchor.style.opacity = '0';
        anchor.style.visibility = 'hidden';
      });
    }
  });

  button.addEventListener('click', (e) => {
    if (window.velocityWrapperState.isDragging) {
      e.stopPropagation();
      return;
    }

    button.classList.add('clicked-by-user');
    handleButtonClick(e, button, innerDiv);
  });

  wrapper.appendChild(buttonContainer);
  window.velocityWrapperState.button = buttonContainer;

  snapButtonToAnchor(buttonContainer, window.velocityWrapperState.anchors.topRight);

  // Initialize animations with the new system - pass the actual button
  const platformKey = window.velocityWrapperState.platform;
  const platformConfig = window.platforms ? window.platforms[platformKey] : null;
  initializeButtonAnimationSystem(platformConfig, button);  // Pass the actual button element

  // Initialize writing quality analyzer
  if (window.velocityQualityAnalyzer && typeof window.velocityQualityAnalyzer.init === 'function') {
    window.velocityQualityAnalyzer.init({
      platform: platformKey
    });
  }

  // Initialize hover box using the integration module if available
  if (window.velocityHoverBoxIntegration && typeof window.velocityHoverBoxIntegration.initializeOrReinitializeHoverBox === 'function') {
    try {
      window.velocityHoverBoxIntegration.initializeOrReinitializeHoverBox();
    } catch (e) {
      // console.error("[Velocity] Error initializing hover box:", e);
    }
  }
  // Fall back to direct initialization if integration module is not available
  else if (typeof window.initializeVelocityHoverBox === 'function') {
    try {
      window.initializeVelocityHoverBox();
    } catch (e) {
      // console.error("[Velocity] Error initializing hover box:", e);
    }
  }

  return buttonContainer;
}

function removeButton() {
  // Clean up quality analyzer if it exists
  if (window.velocityQualityAnalyzer && typeof window.velocityQualityAnalyzer.cleanup === 'function') {
    window.velocityQualityAnalyzer.cleanup();
  }

  // Clean up button animation system
  if (window.velocityWrapperState.buttonSystem) {
    window.velocityWrapperState.buttonSystem.reset();
    window.velocityWrapperState.buttonSystem = null;
  } else if (window.velocityAnimations && window.velocityAnimations.resetState) {
    window.velocityAnimations.resetState();
  }

  // Clean up hover box and message boxes using the integration module if available
  if (window.velocityHoverBoxIntegration && typeof window.velocityHoverBoxIntegration.cleanupAll === 'function') {
    window.velocityHoverBoxIntegration.cleanupAll();
  }
  // Fall back to direct cleanup if integration module is not available
  else {
    // Clean up hover box
    if (window.velocityHoverBoxState && window.velocityHoverBoxState.activeHoverBox) {
      const hoverBox = window.velocityHoverBoxState.activeHoverBox;
      if (hoverBox && hoverBox.parentNode) {
        hoverBox.parentNode.removeChild(hoverBox);
      }
      window.velocityHoverBoxState.activeHoverBox = null;
      window.velocityHoverBoxState.initialized = false;
    }

    // Remove any existing message boxes
    document.querySelectorAll('.velocity-message-box').forEach(box => {
      if (box && box.parentNode) {
        box.parentNode.removeChild(box);
      }
    });
  }

  const wrapper = window.velocityWrapperState.wrapper;
  if (wrapper && wrapper.parentNode) {
    if (window._velocityObservers && window._velocityObservers.length) {
      window._velocityObservers.forEach(observer => {
        try {
          observer.disconnect();
        } catch (e) {
          // console.error("[Velocity] Error disconnecting observer:", e);
        }
      });
      window._velocityObservers = [];
    }

    if (window._velocityScrollHandlers && window._velocityScrollHandlers.length) {
      window._velocityScrollHandlers.forEach(({ element, handler }) => {
        try {
          element.removeEventListener('scroll', handler);
        } catch (e) {
          // console.error("[Velocity] Error removing scroll listener:", e);
        }
      });
      window._velocityScrollHandlers = [];
    }

    wrapper.parentNode.removeChild(wrapper);
  }

  window.velocityWrapperState.wrapper = null;
  window.velocityWrapperState.container = null;
  window.velocityWrapperState.inputBox = null;
  window.velocityWrapperState.button = null;
  window.velocityWrapperState.anchors = {};
  window.velocityWrapperState.isDragging = false;
  window.velocityWrapperState.dragOffsetX = 0;
  window.velocityWrapperState.dragOffsetY = 0;
}

function snapToNearestAnchor(button, wrapper) {
  const buttonRect = button.getBoundingClientRect();
  const buttonCenterX = buttonRect.left + buttonRect.width / 2;
  const buttonCenterY = buttonRect.top + buttonRect.height / 2;

  const anchors = window.velocityWrapperState.anchors;

  const distances = {};
  for (const [position, anchor] of Object.entries(anchors)) {
    const anchorRect = anchor.getBoundingClientRect();
    const anchorCenterX = anchorRect.left + anchorRect.width / 2;
    const anchorCenterY = anchorRect.top + anchorRect.height / 2;

    distances[position] = Math.hypot(
      buttonCenterX - anchorCenterX,
      buttonCenterY - anchorCenterY
    );
  }

  let closestPosition = 'topLeft';
  let minDistance = Infinity;

  for (const [position, distance] of Object.entries(distances)) {
    if (distance < minDistance) {
      minDistance = distance;
      closestPosition = position;
    }
  }

  snapButtonToAnchor(button, anchors[closestPosition]);
}

function snapButtonToAnchor(button, anchor) {
  const anchorPosition = anchor.dataset.position;
  button.dataset.anchorPosition = anchorPosition;

  const anchorRect = anchor.getBoundingClientRect();
  const buttonRect = button.getBoundingClientRect();

  let offsetX = 0, offsetY = 0;

  switch (anchorPosition) {
    case 'top-left':
      offsetX = 0;
      offsetY = 0;
      break;
    case 'top-right':
      offsetX = -(buttonRect.width - anchorRect.width);
      offsetY = 0;
      break;
    case 'bottom-left':
      offsetX = 0;
      offsetY = -(buttonRect.height - anchorRect.height);
      break;
    case 'bottom-right':
      offsetX = -(buttonRect.width - anchorRect.width);
      offsetY = -(buttonRect.height - anchorRect.height);
      break;
  }

  const newLeft = anchorRect.left + offsetX;
  const newTop = anchorRect.top + offsetY;

  button.style.transition = 'left 0.3s, top 0.3s';
  button.style.left = newLeft + 'px';
  button.style.top = newTop + 'px';

  setTimeout(() => {
    button.style.transition = '';
  }, 300);
}

function sendEnhanceRequest(userPrompt, selectedStyle, platform, loadingHandler, innerDiv) {
  //console.log("[Velocity DEBUG] Starting sendEnhanceRequest with:", {
  //   promptLength: userPrompt?.length || 0,
  //   style: selectedStyle,
  //   platform: platform
  // });

  if (!userPrompt || userPrompt.trim() === "") {
    // console.error("[Velocity] Cannot enhance empty prompt");

    // Only stop animations if the request is invalid
    if (innerDiv) innerDiv.classList.remove("rotating");
    if (loadingHandler) loadingHandler.clear();
    return;
  }

  // Set enhanced prompt active flag and hide suggestion box
  if (window.velocitySuggestions && window.velocitySuggestions.setEnhancedPromptActive) {
    // Set the enhanced prompt active with the original prompt length
    window.velocitySuggestions.setEnhancedPromptActive(true, userPrompt.length);
    //console.log("[Velocity] Enhanced prompt active, hiding suggestions");
  }

  // Log the exact message structure being sent
  const message = {
    action: "EnhancePromptV2",
    prompt: userPrompt,
    writing_style: selectedStyle, // Use writing_style as per API requirements
    style: selectedStyle, // Keep style for backward compatibility
    platform: platform, // Make sure platform is included
    llm: ""
  };
  //console.log("[Velocity DEBUG] Sending message to background:", message);

  chrome.runtime.sendMessage(
    message,
    (response) => {
      //console.log("[Velocity DEBUG] Got response from background:", response);

      if (chrome.runtime.lastError) {
        // console.error("[Velocity DEBUG] Chrome runtime error:", chrome.runtime.lastError);
        // If error, reset enhanced prompt state
        if (window.velocitySuggestions && window.velocitySuggestions.setEnhancedPromptActive) {
          window.velocitySuggestions.setEnhancedPromptActive(false, 0);
        }

        // Only stop animations on error
        if (loadingHandler) {
          loadingHandler.clear();
        }
        if (innerDiv) innerDiv.classList.remove("rotating");

        // If using the button system, explicitly trigger error state
        if (window.velocityWrapperState && window.velocityWrapperState.buttonSystem) {
          const button = window.velocityWrapperState.buttonSystem.state.currentButton;
          if (button) {
            // Stop loading animations and go back to idle
            window.velocityWrapperState.buttonSystem.cleanupLoadingAnimation(button);
            window.velocityWrapperState.buttonSystem.animationManager.stopAnimation('loading');
            window.velocityWrapperState.buttonSystem.stateMachine.transition('idle', { button });

            // Show error message
            window.velocityWrapperState.buttonSystem.messageSystem.showMessage('error', {
              text: 'Error enhancing prompt: ' + (chrome.runtime.lastError.message || 'Unknown error'),
              type: 'error',
              button: button,
              positionStrategy: 'relativeToButton',
              duration: 5000
            });

            // Show the quality indicator again after error
            if (window.velocityQualityState && window.velocityQualityState.indicator) {
              window.velocityQualityState.indicator.style.display = '';
              // console.log("[Velocity] Quality indicator shown after error");
            }
          }
        }

        // console.error("[Velocity] Error from background script:", chrome.runtime.lastError);
        return;
      }

      if (!response || !response.success) {
        // console.error("[Velocity DEBUG] Response error:", response?.error || "No response or success flag");

        // If error, reset enhanced prompt state
        if (window.velocitySuggestions && window.velocitySuggestions.setEnhancedPromptActive) {
          window.velocitySuggestions.setEnhancedPromptActive(false, 0);
        }

        // Only stop animations on error
        if (loadingHandler) {
          loadingHandler.clear();
        }
        if (innerDiv) innerDiv.classList.remove("rotating");

        // If using the button system, explicitly trigger error state
        if (window.velocityWrapperState && window.velocityWrapperState.buttonSystem) {
          const button = window.velocityWrapperState.buttonSystem.state.currentButton;
          if (button) {
            // Stop loading animations and go back to idle
            window.velocityWrapperState.buttonSystem.cleanupLoadingAnimation(button);
            window.velocityWrapperState.buttonSystem.animationManager.stopAnimation('loading');
            window.velocityWrapperState.buttonSystem.stateMachine.transition('idle', { button });
            // Show error message to user
            window.velocityWrapperState.buttonSystem.messageSystem.showMessage('error', {
              text: 'Error enhancing prompt: ' + (response?.error || 'Unknown error'),
              type: 'error',
              button: button,
              positionStrategy: 'relativeToButton',
              duration: 5000
            });

            // Show the quality indicator again after error
            if (window.velocityQualityState && window.velocityQualityState.indicator) {
              window.velocityQualityState.indicator.style.display = '';
              // console.log("[Velocity] Quality indicator shown after error");
            }
          }
        }

        // console.error("[Velocity] Error from background script:", response?.error || "Unknown error");
        return;
      }

      // Handle successful response
      //console.log("[Velocity] Enhance request completed successfully");

      try {
        //console.log("[Velocity DEBUG] Full response data structure:", JSON.stringify(response.data));

        const enhancedPrompt = response.data.data.enhanced_prompt || response.data.data;
        //console.log("[Velocity DEBUG] Extracted enhanced prompt:", {
        //   length: enhancedPrompt?.length || 0,
        //   sample: enhancedPrompt?.substring(0, 50) + "..."
        // });

        // Update input with enhanced prompt
        const inputBox = window.velocityWrapperState.inputBox;
        if (inputBox) {
          if (inputBox.tagName === "TEXTAREA") {
            inputBox.value = enhancedPrompt;
            inputBox.dispatchEvent(new Event("input", { bubbles: true }));
          } else if (inputBox.hasAttribute("contenteditable")) {
            inputBox.innerText = enhancedPrompt;
            inputBox.dispatchEvent(new Event("input", { bubbles: true }));
          }
        } else {
          // console.error("[Velocity DEBUG] Input box not found after enhancement");
        }

        // Make sure highlight styles are injected
        injectEnhancedHighlightStyles();

        // Only now stop the loading animation and trigger success state
        if (window.velocityWrapperState.buttonSystem) {
          window.velocityWrapperState.buttonSystem.handleSuccessState(
            window.velocityWrapperState.buttonSystem.state.currentButton,
            false
          );
        }

        // Update the enhanced prompt length to match final result
        if (window.velocitySuggestions && window.velocitySuggestions.setEnhancedPromptActive && enhancedPrompt) {
          // Keep enhanced prompt active but update length to the complete enhanced prompt
          window.velocitySuggestions.setEnhancedPromptActive(true, enhancedPrompt.length);
          //console.log("[Velocity] Enhanced prompt complete, length updated to", enhancedPrompt.length);
        }

        // Apply the highlight and scale effects
        if (inputBox) {
          inputBox.classList.add("velocity-enhanced-highlight", "velocity-enhanced-scale");

          // If the input is in a container, also highlight that
          const inputContainer = inputBox.closest('.chat-input-container, .input-container, [role="textbox"]');
          if (inputContainer && inputContainer !== inputBox) {
            inputContainer.classList.add("velocity-enhanced-highlight");
          }

          // After animation completes, remove highlight classes
          setTimeout(() => {
            inputBox.classList.remove("velocity-enhanced-highlight", "velocity-enhanced-scale");
            if (inputContainer && inputContainer !== inputBox) {
              inputContainer.classList.remove("velocity-enhanced-highlight");
            }

            // Show the quality indicator again after enhancement is complete
            if (window.velocityQualityState && window.velocityQualityState.indicator) {
              window.velocityQualityState.indicator.style.display = '';
              // console.log("[Velocity] Quality indicator shown after enhancement");
            }
          }, 1000);
        }
      } catch (err) {
        // console.error("[Velocity DEBUG] Error processing successful response:", err);
        // console.error("[Velocity DEBUG] Response data that caused error:", response.data);
      }
    }
  );
}

function handleInsertHereButtonClick(prompt) {
  // Get current tab's URL
  const currentURL = window.location.href;
  let matchedPlatform = null;

  // Check if current URL matches any platform
  for (const platform in window.platforms) {
    if (window.platforms[platform].urlPattern.test(currentURL)) {
      matchedPlatform = platform;
      break;
    }
  }

  if (matchedPlatform) {
    // We're on a supported platform, inject directly
    const success = injectPromptIntoInputField(prompt);

    if (success) {
      // Update button state to success if button system exists
      if (window.velocityWrapperState.buttonSystem) {
        const button = window.velocityWrapperState.buttonSystem.state.currentButton;
        if (button) {
          window.velocityWrapperState.buttonSystem.handleSuccessState(button, false);
        }
      }
      return true;
    } else {
      // Retry once after a short delay
      setTimeout(() => {
        const retrySuccess = injectPromptIntoInputField(prompt);
        if (retrySuccess && window.velocityWrapperState.buttonSystem) {
          const button = window.velocityWrapperState.buttonSystem.state.currentButton;
          if (button) {
            window.velocityWrapperState.buttonSystem.handleSuccessState(button, false);
          }
        }
      }, 500);
      return retrySuccess;
    }
  }

  return false; // Not on a supported platform
}

function tryAlternativeContainers() {
  const alternativeSelectors = [
    'textarea',
    'div[contenteditable="true"]',
    '[role="textbox"]',
    '.ProseMirror',
    '.ql-editor',
    'div[class*="editor"]',
    'div[class*="input"]',
    'div[class*="textarea"]',
    'div[class*="bottom"]',
    'div[class*="footer"]',
    'div[class*="absolute"]',
    'div[class*="secondary"]',
    'div[class*="composer"]',
    '.chat-input-container',
    '.message-composer',
    '.input-container',
    '.editor-container',
    'form textarea',
    'form input[type="text"]',
    'form div[contenteditable]'
  ];

  for (const selector of alternativeSelectors) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
      const visibleElements = Array.from(elements).filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.width > 0 &&
          rect.height > 0 &&
          window.getComputedStyle(el).display !== 'none' &&
          window.getComputedStyle(el).visibility !== 'hidden';
      });

      if (visibleElements.length > 0) {
        const bottomElements = visibleElements.filter(el => {
          const rect = el.getBoundingClientRect();
          return rect.bottom > window.innerHeight * 0.7 && rect.width > 100;
        });

        if (bottomElements.length > 0) {
          const largestBottomElement = bottomElements.reduce((largest, current) => {
            const largestRect = largest.getBoundingClientRect();
            const currentRect = current.getBoundingClientRect();
            return (currentRect.width * currentRect.height) > (largestRect.width * largestRect.height) ? current : largest;
          }, bottomElements[0]);

          return useElementForWrapper(largestBottomElement);
        }

        const largestElement = visibleElements.reduce((largest, current) => {
          const largestRect = largest.getBoundingClientRect();
          const currentRect = current.getBoundingClientRect();
          return (currentRect.width * currentRect.height) > (largestRect.width * largestRect.height) ? current : largest;
        }, visibleElements[0]);

        return useElementForWrapper(largestElement);
      }
    }
  }

  const possibleInputs = Array.from(document.querySelectorAll('div, textarea, input'))
    .filter(el => {
      const rect = el.getBoundingClientRect();
      const style = window.getComputedStyle(el);
      return rect.bottom > window.innerHeight * 0.6 &&
        rect.width > 200 &&
        rect.height > 20 &&
        style.display !== 'none' &&
        style.visibility !== 'hidden';
    })
    .sort((a, b) => {
      const aRect = a.getBoundingClientRect();
      const bRect = b.getBoundingClientRect();
      return bRect.bottom - aRect.bottom;
    });

  if (possibleInputs.length > 0) {
    return useElementForWrapper(possibleInputs[0]);
  }
}

function useElementForWrapper(element) {
  let container = element.parentElement;
  let bestParent = null;
  let depth = 0;
  const MAX_DEPTH = 3;

  while (container && depth < MAX_DEPTH) {
    const style = window.getComputedStyle(container);
    if (style.display.includes('flex') || style.display.includes('grid') ||
      container.className.match(/input|editor|compose|chat/i)) {
      bestParent = container;
      break;
    }
    container = container.parentElement;
    depth++;
  }

  if (bestParent) {
    const textArea = findTextAreaInContainer(bestParent, element);

    createFullWrapper({
      container: bestParent,
      inputElement: element,
      textArea: textArea,
      rect: bestParent.getBoundingClientRect(),
      textAreaRect: textArea.getBoundingClientRect()
    });
  } else {
    createFullWrapper({
      container: element,
      inputElement: element,
      textArea: element,
      rect: element.getBoundingClientRect(),
      textAreaRect: element.getBoundingClientRect()
    });
  }

  window.currentInputBox = element;
  return true;
}

function getPositionFromButtonAnchor(button) {
  const buttonContainer = button.closest('.velocity-button-container');
  if (!buttonContainer) {
    return { side: 'right', verticalAlign: 'top' };
  }

  const anchorPosition = buttonContainer.dataset.anchorPosition || 'top-right';

  const side = anchorPosition.includes('left') ? 'left' : 'right';
  const verticalAlign = anchorPosition.includes('top') ? 'top' : 'bottom';

  return { side, verticalAlign };
}

// Setup event listeners
chrome.storage.local.get("enabled", ({ enabled }) => {
  // Default to true if enabled is not explicitly set to false
  if (enabled !== false) {
    (function () {
      injectButton();
      setupSimplifiedObserver();
      initializeWelcomeBox();
    })();
  }
});

function setupSimplifiedObserver() {
  const DEBOUNCE_TIME = 500;

  let lastInjectionTime = Date.now();
  let debounceTimer = null;

  const observer = new MutationObserver(() => {
    if (debounceTimer) clearTimeout(debounceTimer);

    debounceTimer = setTimeout(() => {
      const now = Date.now();
      if (now - lastInjectionTime < 2000) {
        return;
      }

      if (window.velocityWrapperState.wrapper &&
        document.contains(window.velocityWrapperState.wrapper)) {
        return;
      }

      if (window.velocityWrapperState.inputBox &&
        !document.contains(window.velocityWrapperState.inputBox)) {
        window.velocityWrapperState.wrapper = null;
        window.velocityWrapperState.container = null;
        window.velocityWrapperState.inputBox = null;
        window.velocityWrapperState.button = null;
        window.velocityWrapperState.anchors = {};
      }

      const currentPlatform = detectPlatform();
      if (currentPlatform !== window.velocityWrapperState.platform) {
        window.velocityWrapperState.platform = currentPlatform;
      }

      if (currentPlatform) {
        // Always initialize suggestion system on platform change
        integrateWithSuggestionSystem

        // Only inject button if enabled is not false, regardless of auth status
        chrome.storage.local.get(
          ["enabled"],
          (data) => {
            if (data.enabled !== false) {
              injectButton();
              lastInjectionTime = now;
            } else {
              // For free users, just make sure to monitor inputs
              const platformConfig = window.platforms ? window.platforms[currentPlatform] : null;
              if (platformConfig && platformConfig.textAreaSelector) {
                try {
                  if (window.velocitySuggestions && typeof window.velocitySuggestions.monitor === 'function') {
                    window.velocitySuggestions.monitor(platformConfig);
                  } else {
                    monitorInputFields(platformConfig);
                  }
                } catch (e) {
                  // console.error("[Velocity] Error setting up input monitoring for free users:", e);
                }
              }
            }
          }
        );
      }
    }, DEBOUNCE_TIME);
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });

  window._velocityObservers = window._velocityObservers || [];
  window._velocityObservers.push(observer);

  let lastUrl = window.location.href;

  const urlCheckInterval = setInterval(() => {
    const currentUrl = window.location.href;
    if (currentUrl !== lastUrl) {
      lastUrl = currentUrl;

      window.velocityWrapperState.wrapper = null;
      window.velocityWrapperState.container = null;
      window.velocityWrapperState.inputBox = null;
      window.velocityWrapperState.button = null;
      window.velocityWrapperState.anchors = {};
      window.velocityWrapperState.platform = detectPlatform();

      // Always initialize suggestion system on URL change
      integrateWithSuggestionSystem

      if (window.velocityWrapperState.platform) {
        chrome.storage.local.get(
          ["enabled"],
          (data) => {
            if (data.enabled !== false) {
              injectButton();
              lastInjectionTime = Date.now();
            }
          }
        );
      }
    }
  }, 1000);

  window._velocityIntervals = window._velocityIntervals || [];
  window._velocityIntervals.push(urlCheckInterval);
}

function cleanupObserversAndIntervals() {
  if (window._velocityObservers && window._velocityObservers.length) {
    window._velocityObservers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (e) {
        // console.error("[Velocity] Error disconnecting observer:", e);
      }
    });
    window._velocityObservers = [];
  }

  if (window._velocityIntervals && window._velocityIntervals.length) {
    window._velocityIntervals.forEach(interval => {
      try {
        clearInterval(interval);
      } catch (e) {
        // console.error("[Velocity] Error clearing interval:", e);
      }
    });
    window._velocityIntervals = [];
  }

  if (window._velocityScrollHandlers && window._velocityScrollHandlers.length) {
    window._velocityScrollHandlers.forEach(({ element, handler }) => {
      try {
        element.removeEventListener('scroll', handler);
      } catch (e) {
        // console.error("[Velocity] Error removing scroll listener:", e);
      }
    });
    window._velocityScrollHandlers = [];
  }
}

chrome.storage.onChanged.addListener((changes) => {
  if (
    changes.enabled ||
    changes.token ||
    changes.userName ||
    changes.userId ||
    changes.userEmail
  ) {
    //console.log("[Velocity] Auth state changed, reinitializing components");

    // Signal to suggestionBox that auth state is changing to prevent reconnection attempts
    if (window.velocitySuggestions && typeof window.velocitySuggestions.prepareForAuthChange === 'function') {
      window.velocitySuggestions.prepareForAuthChange();
    } else if (window.velocitySuggestionState) {
      // Fallback if the function isn't available
      window.velocitySuggestionState.authChanging = true;
    }

    checkAuthAndInjectButton();

    // Wait a short delay before reinitializing suggestion system
    // to ensure cleanup is complete
    setTimeout(() => {
      // Reset authChanging flag if not reset by prepareForAuthChange
      if (window.velocitySuggestionState) {
        window.velocitySuggestionState.authChanging = false;
      }

      initSuggestionSystem();
    }, 1000); // Longer delay to ensure proper cleanup
  }
});

chrome.runtime.onMessage.addListener((message) => {
  if (message.action === "toggleButton") {
    if (message.enabled) {
      injectButton();
      setupSimplifiedObserver();
    } else {
      removeButton();
      cleanupObserversAndIntervals();
    }
  }
});

// Initialize welcome box on all pages
document.addEventListener('DOMContentLoaded', function () {
  initializeWelcomeBox();
});

// Also run immediately if document is already loaded
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  initializeWelcomeBox();
}

// Initialize animations for existing buttons
function initializeExistingButtonAnimations() {
  const buttons = document.querySelectorAll('.velocity-button-container button, .custom-injected-button button');

  if (buttons.length > 0) {
    if (window.velocityAnimations && typeof window.velocityAnimations.initButtonAnimations === 'function') {
      buttons.forEach(button => {
        try {
          const platform = window.velocityWrapperState.platform;
          const platformConfig = window.platforms[platform];
          window.velocityAnimations.initButtonAnimations(platformConfig);

          const innerDiv = button.querySelector('div');
          // Only initialize SVG pulse effects if the button contains an SVG element
          const hasSvg = innerDiv && innerDiv.querySelector('svg');
          if (hasSvg && window.velocitySvgPulse && typeof window.velocitySvgPulse.initSvgPulseEffect === 'function') {
            window.velocitySvgPulse.initSvgPulseEffect(platformConfig, innerDiv);
          }
        } catch (e) {
          // console.error("[Velocity] Error initializing animations for existing button:", e);
        }
      });
    } else {
      //console.warn("[Velocity] Animation system not available for initialization");
    }
  }
}

// Run animation initialization on document load
document.addEventListener('DOMContentLoaded', function () {
  setTimeout(initializeExistingButtonAnimations, 500);
});

// Also initialize animations immediately if document is already loaded
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  setTimeout(initializeExistingButtonAnimations, 500);
}

// WebSocket-related functionality
function monitorInputFields(platformConfig) {
  if (!platformConfig || !platformConfig.textAreaSelector) {
    //console.warn("[monitorInputFields] Platform config missing or invalid");
    return;
  }

  const inputSelector = platformConfig.textAreaSelector;

  function handleInputChange(e) {
    const inputElement = e.target;
    window.velocitySuggestionState.lastMonitoredElement = inputElement;
    window.velocitySuggestionState.inputElement = inputElement;

    let text;
    if (inputElement.tagName === "TEXTAREA") {
      text = inputElement.value;
    } else if (inputElement.hasAttribute("contenteditable")) {
      text = inputElement.innerText;
    } else {
      text = inputElement.textContent;
    }

    if (!text || text.length < 5) {
      hideSuggestionBox();
      return;
    }

    if (text === window.velocitySuggestionState.lastProcessedText) {
      return;
    }
    window.velocitySuggestionState.lastProcessedText = text;

    clearTimeout(window.velocitySuggestionState.debounceTimer);
    window.velocitySuggestionState.debounceTimer = setTimeout(() => {
      chrome.runtime.sendMessage({
        action: "sendText",
        text: text,
        platform: window.velocityWrapperState.platform
      }, (response) => {
        if (chrome.runtime.lastError) {
          // console.error("[monitorInputFields] Error sending text:", chrome.runtime.lastError);
        }
      });
    }, 500);
  }

  function setupInputObservers() {
    if (window.inputObserver) {
      window.inputObserver.disconnect();
    }

    const inputElements = document.querySelectorAll(inputSelector);
    if (inputElements.length > 0) {
      inputElements.forEach(input => {
        if (!input.hasAttribute('data-velocity-monitored')) {
          input.setAttribute('data-velocity-monitored', 'true');

          if (input.tagName === "TEXTAREA") {
            input.addEventListener('input', handleInputChange);
          } else if (input.hasAttribute("contenteditable")) {
            input.addEventListener('input', handleInputChange);
          } else {
            input.addEventListener('input', handleInputChange);
          }
        }
      });
    }

    window.inputObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
          const newInputs = document.querySelectorAll(inputSelector + ':not([data-velocity-monitored])');
          newInputs.forEach(input => {
            input.setAttribute('data-velocity-monitored', 'true');

            if (input.tagName === "TEXTAREA") {
              input.addEventListener('input', handleInputChange);
            } else if (input.hasAttribute("contenteditable")) {
              input.addEventListener('input', handleInputChange);
            } else {
              input.addEventListener('input', handleInputChange);
            }
          });
        }
      });
    });

    window.inputObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  setupInputObservers();

  if (!window.domObserver) {
    window.domObserver = new MutationObserver((mutations) => {
      setupInputObservers();
    });

    window.domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Run initialization on document load
document.addEventListener('DOMContentLoaded', function () {
  initSuggestionSystem();
});

// Also run initialization immediately in case the DOM is already loaded
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  initSuggestionSystem();
}

// WebSocket Console Logging
//console.log("[Velocity] WebSocket monitoring initialized in content script");

// Function to check WebSocket status and log it
function checkWebSocketStatus() {
  chrome.runtime.sendMessage({ action: "ping" }, (response) => {
    if (chrome.runtime.lastError) {
      // console.error("[Velocity WebSocket] Connection error:", chrome.runtime.lastError);
    } else if (response) {
      //console.log("[Velocity WebSocket] Status:", response.socketStatus ? "Connected" : "Disconnected");
      //console.log("[Velocity WebSocket] Details:", response);
    }
  });
}

// Check WebSocket status periodically
setInterval(checkWebSocketStatus, 30000); // Check every 30 seconds
checkWebSocketStatus(); // Check immediately

// WebSocket message tracking
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === "ping" && sendResponse) {
    //console.log("[Velocity WebSocket] Received ping request from background script");
    sendResponse({ success: true, contentReady: true });
    return true;
  }

  if (message.action === "showFreeTrialPopup") {
    //console.log("[Velocity] Showing free trial popup from background request");
    if (window.showTrailFinishedNotification) {
      window.showTrailFinishedNotification();
    } else {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('trail-finished.js');
      script.onload = function() {
        if (window.showTrailFinishedNotification) {
          window.showTrailFinishedNotification();
          disableAllAnimations();
        } else {
          // console.error("[Velocity] Free trial notification function not available after script load");
        }
      };
      document.head.appendChild(script);
    }
    if (sendResponse) sendResponse({ success: true });
    return true;
  }

  if (message.action &&
      (message.action.includes("websocket") ||
       message.action === "displaySuggestions" ||
       message.action === "extensionStateChanged")) {
    //console.log("[Velocity WebSocket] Message received:", message);
  }

  return false;
});

// Special initialization for supported LLM platforms
if (["chatgpt.com", "claude.ai", "gemini.google.com", "perplexity.ai",
     "gamma.app", "v0.dev", "boltai.com", "grok.com", "lovable.dev", "replit.com", "app.runwayml.com"].includes(window.location.hostname)) {
  //console.log(`[Velocity] ${window.location.hostname} detected, initializing prompt injection capabilities`);

  window.velocityLLMReady = true;
  window.velocityChatGPTReady = window.location.hostname === "chatgpt.com"; // For backward compatibility

  chrome.storage.local.get(["storedResponse", "suggestedLLM"], function(result) {
    if (result.storedResponse && result.storedResponse !== "none") {
      //console.log(`[Velocity] Found stored prompt, injecting automatically into ${window.location.hostname}`);

      setTimeout(() => {
        const success = injectPromptIntoInputField(result.storedResponse);

        if (success !== false) {
          chrome.storage.local.set({ "storedResponse": "none", "suggestedLLM": null }, function() {
            //console.log("[Velocity] Cleared stored prompt after successful injection");
          });
        } else {
          setTimeout(() => {
            const retrySuccess = injectPromptIntoInputField(result.storedResponse);
            if (retrySuccess !== false) {
              chrome.storage.local.set({ "storedResponse": "none", "suggestedLLM": null }, function() {
                //console.log("[Velocity] Cleared stored prompt after successful retry injection");
              });
            } else {
              // console.error(`[Velocity] Failed to inject stored prompt into ${window.location.hostname} after retry`);
            }
          }, 10000);
        }
      }, 5000);
    } else {
      //console.log("[Velocity] No stored prompt found or prompt is 'none'");
    }
  });

  try {
    chrome.runtime.sendMessage({
      action: "chatgptContentScriptReady",
      url: window.location.href
    });
  } catch (e) {
    // console.error("[Velocity] Error sending ready message:", e);
  }
}



// Load quality indicator styles
const qualityIndicatorStyles = document.createElement('link');
qualityIndicatorStyles.rel = 'stylesheet';
qualityIndicatorStyles.href = chrome.runtime.getURL('quality-indicator.css');
document.head.appendChild(qualityIndicatorStyles);

// Test function to verify if messages are working
window.testMessageBox = function() {
  if (window.velocityWrapperState.buttonSystem) {
    const button = document.querySelector('.velocity-button');
    window.velocityWrapperState.buttonSystem.messageSystem.showMessage('test', {
      text: 'This is a test message!',
      type: 'info',
      button: button,
      positionStrategy: 'relativeToButton',
      duration: 5000
    });
  } else {
    // console.error('[Velocity] Button system not initialized');
  }
};

// Test function to verify message box cleanup during reinitialization
window.testMessageBoxCleanup = function() {
  // console.log('[Velocity] Testing message box cleanup during reinitialization');

  // First show a test message
  window.testMessageBox();

  // Then reinitialize the button system after a delay
  setTimeout(() => {
    // console.log('[Velocity] Reinitializing button system to test cleanup');
    const platform = window.velocityWrapperState.platform;
    const platformConfig = window.platforms ? window.platforms[platform] : null;
    const button = document.querySelector('.velocity-button');

    if (platformConfig && button) {
      initializeButtonAnimationSystem(platformConfig, button);
      // console.log('[Velocity] Button system reinitialized, message boxes should be cleaned up');

      // Show a new message after reinitialization
      setTimeout(() => {
        window.testMessageBox();
      }, 1000);
    } else {
      // console.error('[Velocity] Could not find platform config or button for reinitialization test');
    }
  }, 2000);
};

// Debug helper to check message system status
window.debugMessageSystem = function() {
  //console.log('🔍 Button System exists:', !!window.velocityWrapperState.buttonSystem);
  //console.log('💬 Message System exists:', !!window.velocityWrapperState.buttonSystem?.messageSystem);
  //console.log('🎨 Message styles injected:', !!document.getElementById('velocity-message-styles'));

  if (window.velocityWrapperState.buttonSystem?.messageSystem) {
    //console.log('📊 Active messages:', window.velocityWrapperState.buttonSystem.messageSystem.messages.size);
  }
};

// Function to inject enhanced highlight animation styles
function injectEnhancedHighlightStyles() {
  if (document.getElementById('velocity-highlight-styles')) return;

  const styleEl = document.createElement('style');
  styleEl.id = 'velocity-highlight-styles';
  styleEl.innerHTML = `
    @keyframes velocity-enhanced-highlight {
      0% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
      30% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      70% { background-color: rgba(0, 136, 255, 0.2); box-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
      100% { background-color: rgba(0, 136, 255, 0); box-shadow: 0 0 0 rgba(0, 136, 255, 0); }
    }

    @keyframes velocity-enhanced-scale {
      0% { transform: scale(1); }
      30% { transform: scale(1.03); }
      70% { transform: scale(1.03); }
      100% { transform: scale(1); }
    }

    .velocity-enhanced-highlight {
      animation: velocity-enhanced-highlight 1s ease-in-out forwards;
      border-color: #0088cb !important;
      transition: all 0.3s ease;
    }

    .velocity-enhanced-scale {
      animation: velocity-enhanced-scale 1s ease-in-out forwards;
    }

    .text-pop-effect {
      animation: text-pop 0.3s ease-in-out;
    }

    @keyframes text-pop {
      0% { transform: scale(1); }
      50% { transform: scale(1.02); }
      100% { transform: scale(1); }
    }
  `;

  document.head.appendChild(styleEl);
}
