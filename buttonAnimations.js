/**
 * buttonAnimationSystem.js - Scalable button animation system
 * Implements a modular, event-driven architecture for better maintainability
 */

// State Machine Implementation
class AnimationStateMachine {
  constructor() {
    this.states = new Map();
    this.currentState = null;
    this.listeners = new Set();
  }

  addState(name, config) {
    this.states.set(name, {
      enter: config.enter || (() => {}),
      exit: config.exit || (() => {}),
      transitions: config.transitions || new Map(),
      onUpdate: config.onUpdate || (() => {})
    });
  }

  transition(newState, data = {}) {
    if (!this.states.has(newState)) {
      throw new Error(`State ${newState} not found`);
    }

    if (this.currentState) {
      const currentConfig = this.states.get(this.currentState);
      currentConfig.exit(data);
    }

    const newConfig = this.states.get(newState);
    this.currentState = newState;
    newConfig.enter(data);
    this.notifyListeners(newState, data);
  }

  getCurrentState() {
    return this.currentState;
  }

  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  notifyListeners(state, data) {
    this.listeners.forEach(listener => listener(state, data));
  }
}

// Animation Manager
class AnimationManager {
  constructor() {
    this.animations = new Map();
    this.activeAnimations = new Map();
  }

  registerAnimation(name, animationConfig) {
    this.animations.set(name, {
      start: animationConfig.start,
      stop: animationConfig.stop,
      update: animationConfig.update || (() => {})
    });
  }

  startAnimation(name, element, data = {}) {
    if (!this.animations.has(name)) {
      throw new Error(`Animation ${name} not found`);
    }

    const animation = this.animations.get(name);
    const animationInstance = animation.start(element, data);
    this.activeAnimations.set(name, {instance: animationInstance, element});

    return animationInstance;
  }

  stopAnimation(name) {
    const active = this.activeAnimations.get(name);
    if (active) {
      const animation = this.animations.get(name);
      animation.stop(active.instance, active.element);
      this.activeAnimations.delete(name);
    }
  }

  stopAllAnimations() {
    this.activeAnimations.forEach((active, name) => {
      const animation = this.animations.get(name);
      animation.stop(active.instance, active.element);
    });
    this.activeAnimations.clear();
  }
}

// Message System with Priority Queue
class MessageSystem {
  constructor() {
    this.messages = new Map();
    this.positionStrategies = new Map();
    this.defaultConfig = {
      duration: 4000,
      maxWidth: 220
    };

    // Message queue for priority-based message handling
    this.highPriorityQueue = [];    // Errors, warnings
    this.mediumPriorityQueue = [];  // Success notifications
    this.lowPriorityQueue = [];     // Informational, idle state
    this.currentActiveMessage = null;

    // Message delay system
    this.messageDelayActive = false;
    this.delayedMessages = [];

    this.injectMessageStyles();
  }

  injectMessageStyles() {
    if (document.getElementById('velocity-message-styles')) return;

    const styleEl = document.createElement('style');
    styleEl.id = 'velocity-message-styles';
    styleEl.innerHTML = `
      .velocity-message-box {
        position: fixed;
        z-index: 999999;
        pointer-events: none;
        animation: fadeIn 0.3s ease-in-out;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 13px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        max-width: 220px;
        opacity: 1;
        visibility: visible;
        display: block;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `;
    document.head.appendChild(styleEl);
  }

  createMessage(config) {
    const element = document.createElement('div');
    element.className = 'velocity-message-box';
    element.dataset.velocityMessage = 'true';
    element.dataset.type = config.type || 'info';

    if (config.isHTML) {
      element.innerHTML = config.text;
    } else {
      element.textContent = config.text;
    }

    // Apply styles immediately
    this.applyMessageStyles(element, config);

    document.body.appendChild(element);

    // Force reflow to ensure styles are applied
    element.offsetHeight;

    return {
      element,
      config,
      timeoutId: null
    };
  }

  applyMessageStyles(element, config) {
    chrome.storage.local.get(['darkMode'], (result) => {
      const isDarkMode = result.darkMode === true;

      if (isDarkMode) {
        // Dark mode styling
        element.style.backgroundColor = 'hsl(197, 40%, 14%)';
        element.style.color = '#ffffff';
        element.style.border = '2px solid rgba(0, 0, 0, 0.85)';

        // Update Enter key icon filter for dark mode
        const enterIcon = element.querySelector('.velocity-enter-icon');
        if (enterIcon) {
          enterIcon.style.filter = 'invert(1)';
        }
      } else {
        // Light mode styling
        element.style.backgroundColor = 'hsl(190, 95%, 90%)';
        element.style.color = '#000000';
        element.style.border = '2px solid rgba(0, 0, 0, 0.85)';

        // Update Enter key icon filter for light mode
        const enterIcon = element.querySelector('.velocity-enter-icon');
        if (enterIcon) {
          enterIcon.style.filter = 'none';
        }
      }

      // Common styling for both modes
      element.style.fontFamily = "'DM Sans', system-ui, -apple-system, sans-serif";
      element.style.fontSize = '13px';
      element.style.fontWeight = '500';
      element.style.padding = '10px 20px';
      element.style.borderRadius = '10px';
      element.style.maxWidth = '280px';
      element.style.position = 'fixed';
      element.style.zIndex = '999999';
      element.style.pointerEvents = 'none';
      element.style.textAlign = 'left';
      element.style.animation = 'fadeIn 0.3s ease-in-out';
      element.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      element.style.display = 'block';
      element.style.visibility = 'visible';
      element.style.opacity = '1';
      element.style.transform = 'translateZ(0)';
      element.style.whiteSpace = 'pre-line';
    });
  }

  // New method to update the text of an existing message, supporting HTML
  updateMessageText(id, newText) {
    const message = this.messages.get(id);
    if (!message) return false;

    // If text is the same, don't update
    if (message.config.isHTML) {
      message.element.innerHTML = newText;
      message.config.text = newText;
    } else {
      // Apply smooth transition effect
      const element = message.element;

      // Only apply fade effect if we're not in hover box state
      if (!(window.velocityHoverBoxState && window.velocityHoverBoxState.isHoverBoxVisible)) {
        // Fade out
        element.style.transition = 'opacity 0.2s ease-in-out';
        element.style.opacity = '1';

        // Update text and fade back in after a small delay
        setTimeout(() => {
          if (message.config.isHTML) {
            element.innerHTML = newText;
          } else {
            element.textContent = newText;
          }
          message.config.text = newText;

          // Force reflow to ensure transition applies
          element.offsetHeight;

          // Fade back in
          element.style.opacity = '1';

          // Reset transition after completion
          setTimeout(() => {
            element.style.transition = '';
          }, 200);

          //// console.log(`[Velocity] Message text updated: ${newText}`);
        }, 200);
      } else {
        // Direct update if hover box is visible (no need for animation)
        if (message.config.isHTML) {
          element.innerHTML = newText;
        } else {
          element.textContent = newText;
        }
        message.config.text = newText;
      }
    }

    return true;
  }

  // Enable message delay (e.g., after button reinitialization)
  enableMessageDelay(delayMs = 5000) {
    // console.log(`[Velocity Message] 🕒 Enabling message delay for ${delayMs}ms`);
    this.messageDelayActive = true;

    // Clear any existing timeout
    if (this.messageDelayTimeout) {
      clearTimeout(this.messageDelayTimeout);
      // console.log('[Velocity Message] Cleared existing message delay timeout');
    }

    // Set timeout to disable the delay after specified time
    this.messageDelayTimeout = setTimeout(() => {
      this.messageDelayActive = false;
      // console.log('[Velocity Message] ⏰ Message delay period ended, processing delayed messages');

      // Process any delayed messages
      this.processDelayedMessages();
    }, delayMs);
  }

  // Process any messages that were delayed
  processDelayedMessages() {
    if (this.delayedMessages.length === 0) {
      // console.log('[Velocity Message] No delayed messages to process');
      return;
    }

    // console.log(`[Velocity Message] 📨 Processing ${this.delayedMessages.length} delayed messages`);

    // Process delayed messages in order of priority
    const highPriorityDelayed = this.delayedMessages.filter(m => m.priority === 'high');
    const mediumPriorityDelayed = this.delayedMessages.filter(m => m.priority === 'medium');
    const lowPriorityDelayed = this.delayedMessages.filter(m => m.priority === 'low');

    // console.log(`[Velocity Message] Delayed messages by priority: High=${highPriorityDelayed.length}, Medium=${mediumPriorityDelayed.length}, Low=${lowPriorityDelayed.length}`);

    // Process high priority first
    highPriorityDelayed.forEach(msg => {
      // console.log(`[Velocity Message] Processing delayed high priority message: ${msg.id}`);
      this.showMessageWithPriority(msg.id, msg.config, msg.priority);
    });

    // Then medium priority
    mediumPriorityDelayed.forEach(msg => {
      // console.log(`[Velocity Message] Processing delayed medium priority message: ${msg.id}`);
      this.showMessageWithPriority(msg.id, msg.config, msg.priority);
    });

    // Then low priority
    lowPriorityDelayed.forEach(msg => {
      // console.log(`[Velocity Message] Processing delayed low priority message: ${msg.id}`);
      this.showMessageWithPriority(msg.id, msg.config, msg.priority);
    });

    // Clear the delayed messages
    this.delayedMessages = [];
    // console.log('[Velocity Message] ✅ All delayed messages processed');
  }

  // Add a message to the appropriate queue based on priority
  enqueueMessage(id, message, priority) {
    // console.log(`[Velocity Message] 📥 Enqueueing message: ${id} with priority ${priority}`);

    // Remove any existing message with the same ID from all queues
    this.highPriorityQueue = this.highPriorityQueue.filter(m => m.id !== id);
    this.mediumPriorityQueue = this.mediumPriorityQueue.filter(m => m.id !== id);
    this.lowPriorityQueue = this.lowPriorityQueue.filter(m => m.id !== id);

    // Add to the appropriate queue
    if (priority === 'high') {
      this.highPriorityQueue.push({ id, message });
      // console.log(`[Velocity Message] Added to high priority queue, now ${this.highPriorityQueue.length} messages`);
    } else if (priority === 'medium') {
      this.mediumPriorityQueue.push({ id, message });
      // console.log(`[Velocity Message] Added to medium priority queue, now ${this.mediumPriorityQueue.length} messages`);
    } else {
      this.lowPriorityQueue.push({ id, message });
      // console.log(`[Velocity Message] Added to low priority queue, now ${this.lowPriorityQueue.length} messages`);
    }

    // Process the queue
    // console.log('[Velocity Message] Processing message queue');
    this.processMessageQueue();
  }

  // Process the message queue and show the highest priority message
  processMessageQueue() {
    // console.log(`[Velocity Message] 🔄 Processing message queue: High=${this.highPriorityQueue.length}, Medium=${this.mediumPriorityQueue.length}, Low=${this.lowPriorityQueue.length}`);

    // If there's already an active message being shown, don't interrupt it
    // unless we have a higher priority message
    if (this.currentActiveMessage) {
      // console.log(`[Velocity Message] Current active message: ${this.currentActiveMessage.id} with priority ${this.currentActiveMessage.priority}`);
    }

    let nextMessage = null;
    let nextId = null;
    let nextPriority = null;

    if (this.highPriorityQueue.length > 0) {
      const item = this.highPriorityQueue.shift();
      nextMessage = item.message;
      nextId = item.id;
      nextPriority = 'high';
      // console.log(`[Velocity Message] Selected high priority message: ${nextId}`);
    } else if (this.mediumPriorityQueue.length > 0 &&
               (!this.currentActiveMessage || this.currentActiveMessage.priority !== 'high')) {
      const item = this.mediumPriorityQueue.shift();
      nextMessage = item.message;
      nextId = item.id;
      nextPriority = 'medium';
      // console.log(`[Velocity Message] Selected medium priority message: ${nextId}`);
    } else if (this.lowPriorityQueue.length > 0 &&
               (!this.currentActiveMessage ||
                (this.currentActiveMessage.priority !== 'high' &&
                 this.currentActiveMessage.priority !== 'medium'))) {
      const item = this.lowPriorityQueue.shift();
      nextMessage = item.message;
      nextId = item.id;
      nextPriority = 'low';
      // console.log(`[Velocity Message] Selected low priority message: ${nextId}`);
    } else {
      // console.log('[Velocity Message] No suitable message found to display');
    }

    // If we have a message to show
    if (nextMessage) {
      // If there's a current message, remove it first
      if (this.currentActiveMessage) {
        const currentId = this.currentActiveMessage.id;
        const currentPriority = this.currentActiveMessage.priority;

        // Only remove if the current message is of lower priority
        if ((currentPriority === 'low' && (nextPriority === 'medium' || nextPriority === 'high')) ||
            (currentPriority === 'medium' && nextPriority === 'high')) {
          // console.log(`[Velocity Message] 🔄 Replacing lower priority message ${currentId} (${currentPriority}) with higher priority message ${nextId} (${nextPriority})`);
          this.removeMessage(currentId);
          this.currentActiveMessage = null;
        } else {
          // Re-queue the next message if it's not higher priority
          // console.log(`[Velocity Message] ⏳ Re-queueing message ${nextId} (${nextPriority}) because current message ${currentId} (${currentPriority}) has equal or higher priority`);
          this.enqueueMessage(nextId, nextMessage, nextPriority);
          return;
        }
      }

      // Show the next message
      // console.log(`[Velocity Message] 📢 Displaying message: ${nextId} with priority ${nextPriority}`);
      this.currentActiveMessage = {
        id: nextId,
        message: nextMessage,
        priority: nextPriority
      };

      // Ensure message is visible with proper styles
      nextMessage.element.style.display = 'block';
      nextMessage.element.style.visibility = 'visible';
      nextMessage.element.style.opacity = '1';
      nextMessage.element.style.transform = 'translateZ(0)';

      // Set timeout to remove the message if duration provided
      if (nextMessage.config.duration !== null) {
        if (nextMessage.timeoutId) {
          clearTimeout(nextMessage.timeoutId);
        }

        const duration = nextMessage.config.duration || this.defaultConfig.duration;
        // console.log(`[Velocity Message] ⏱️ Setting timeout to remove message ${nextId} after ${duration}ms`);

        nextMessage.timeoutId = setTimeout(() => {
          // console.log(`[Velocity Message] ⌛ Timeout expired for message ${nextId}, removing`);
          this.removeMessage(nextId);
          this.currentActiveMessage = null;
          this.processMessageQueue(); // Process next message in queue
        }, duration);
      } else {
        // console.log(`[Velocity Message] Message ${nextId} has no duration, will remain until explicitly removed`);
      }
    }
  }

  // Show a message with priority
  showMessageWithPriority(id, config, priority = 'low') {
    // Check if hover box is currently visible - if so, don't show the message
    if (window.velocityHoverBoxState && window.velocityHoverBoxState.isHoverBoxVisible) {
      //// console.log(`[Velocity] Message not shown because hover box is visible: ${config.text}`);
      return null;
    }

    // If a message with this ID already exists and we're just updating text,
    // update the existing message instead of recreating it
    if (this.messages.has(id) && config.updateTextOnly) {
      return this.updateMessageText(id, config.text) ? this.messages.get(id) : null;
    }

    if (this.messages.has(id)) {
      this.removeMessage(id);
    }

    const message = this.createMessage(config);
    this.messages.set(id, message);

    // Position immediately
    if (config.positionStrategy) {
      this.positionMessage(message, config.positionStrategy);
    }

    // Force a reflow to ensure styles are applied
    message.element.offsetHeight;

    // Add to the queue with the specified priority
    this.enqueueMessage(id, message, priority);

    return message;
  }

  // Main showMessage method - now with priority support
  showMessage(id, config, priority = 'low') {
    // console.log(`[Velocity Message] 📩 Request to show message: ${id} with priority ${priority}`);

    // If message delay is active and this is not a high priority message, queue it for later
    if (this.messageDelayActive && priority !== 'high') {
      // console.log(`[Velocity Message] ⏳ Message delayed due to active delay: ${id}`);
      this.delayedMessages.push({ id, config, priority });
      return null;
    }

    // console.log(`[Velocity Message] Showing message immediately: ${id}`);
    return this.showMessageWithPriority(id, config, priority);
  }

  registerPositionStrategy(name, strategy) {
    this.positionStrategies.set(name, strategy);
  }

  removeMessage(id) {
    // console.log(`[Velocity Message] 🗑️ Removing message: ${id}`);

    const message = this.messages.get(id);
    if (message) {
      if (message.timeoutId) {
        clearTimeout(message.timeoutId);
        // console.log(`[Velocity Message] Cleared timeout for message: ${id}`);
      }
      message.element.remove();
      this.messages.delete(id);
      // console.log(`[Velocity Message] Removed message element and deleted from map: ${id}`);

      // If this was the current active message, clear it
      if (this.currentActiveMessage && this.currentActiveMessage.id === id) {
        // console.log(`[Velocity Message] This was the current active message, clearing it: ${id}`);
        this.currentActiveMessage = null;

        // Process the next message in the queue
        // console.log(`[Velocity Message] Scheduling processing of next message in queue`);
        setTimeout(() => this.processMessageQueue(), 0);
      }
    } else {
      // console.log(`[Velocity Message] Message not found in tracked messages: ${id}`);
    }

    // Also remove from all queues
    const highBefore = this.highPriorityQueue.length;
    const mediumBefore = this.mediumPriorityQueue.length;
    const lowBefore = this.lowPriorityQueue.length;
    const delayedBefore = this.delayedMessages.length;

    this.highPriorityQueue = this.highPriorityQueue.filter(m => m.id !== id);
    this.mediumPriorityQueue = this.mediumPriorityQueue.filter(m => m.id !== id);
    this.lowPriorityQueue = this.lowPriorityQueue.filter(m => m.id !== id);
    this.delayedMessages = this.delayedMessages.filter(m => m.id !== id);

    const highRemoved = highBefore - this.highPriorityQueue.length;
    const mediumRemoved = mediumBefore - this.mediumPriorityQueue.length;
    const lowRemoved = lowBefore - this.lowPriorityQueue.length;
    const delayedRemoved = delayedBefore - this.delayedMessages.length;

    if (highRemoved + mediumRemoved + lowRemoved + delayedRemoved > 0) {
      // console.log(`[Velocity Message] Removed from queues: High=${highRemoved}, Medium=${mediumRemoved}, Low=${lowRemoved}, Delayed=${delayedRemoved}`);
    }
  }

  removeAllMessages() {
    // console.log('[Velocity Message] 🧹 Removing all messages');

    // First remove all tracked messages
    const messageCount = this.messages.size;
    this.messages.forEach((msg, id) => {
      if (msg.timeoutId) {
        clearTimeout(msg.timeoutId);
      }
      if (msg.element && msg.element.parentNode) {
        msg.element.remove();
      }
    });
    // console.log(`[Velocity Message] Removed ${messageCount} tracked message elements`);

    // Then ensure any untracked message boxes are also removed from the DOM
    // This handles cases where message boxes might have been created but not properly tracked
    const untrackedBoxes = document.querySelectorAll('.velocity-message-box');
    untrackedBoxes.forEach(box => {
      if (box && box.parentNode) {
        box.parentNode.removeChild(box);
      }
    });
    // console.log(`[Velocity Message] Removed ${untrackedBoxes.length} untracked message boxes from DOM`);

    // Clear all queues and state
    // console.log(`[Velocity Message] Clearing queues: High=${this.highPriorityQueue.length}, Medium=${this.mediumPriorityQueue.length}, Low=${this.lowPriorityQueue.length}, Delayed=${this.delayedMessages.length}`);

    this.messages.clear();
    this.highPriorityQueue = [];
    this.mediumPriorityQueue = [];
    this.lowPriorityQueue = [];
    this.delayedMessages = [];
    this.currentActiveMessage = null;

    // Clear any message delay timeout
    if (this.messageDelayTimeout) {
      clearTimeout(this.messageDelayTimeout);
      this.messageDelayTimeout = null;
      // console.log('[Velocity Message] Cleared message delay timeout');
    }

    // console.log('[Velocity Message] ✅ All messages and queues cleared');
  }

  positionMessage(message, strategyName) {
    const strategy = this.positionStrategies.get(strategyName);
    if (strategy) {
      strategy(message.element, message.config);
    }
  }
}

// Event Manager
class EventManager {
  constructor() {
    this.listeners = new Map();
    this.debounceTimers = new Map();
  }

  on(eventName, element, handler, options = {}) {
    if (!element) return;

    const key = `${eventName}-${element.id || Math.random()}`;

    let wrappedHandler = handler;
    if (options.debounce) {
      wrappedHandler = this.debounce(handler, options.debounce);
    }

    element.addEventListener(eventName, wrappedHandler);
    this.listeners.set(key, {element, handler: wrappedHandler, originalHandler: handler});

    return key;
  }

  off(key) {
    const listener = this.listeners.get(key);
    if (listener) {
      const [eventName] = key.split('-');
      listener.element.removeEventListener(eventName, listener.handler);
      this.listeners.delete(key);
    }
  }

  offAll() {
    this.listeners.forEach((listener, key) => {
      this.off(key);
    });
  }

  debounce(func, wait) {
    return (...args) => {
      const key = func.toString();
      clearTimeout(this.debounceTimers.get(key));

      const timer = setTimeout(() => {
        func.apply(this, args);
        this.debounceTimers.delete(key);
      }, wait);

      this.debounceTimers.set(key, timer);
    };
  }
}

// Animation Controller for managing state transitions
class AnimationController {
  constructor(buttonSystem) {
    this.buttonSystem = buttonSystem;
    this.currentState = null;
    this.isPromptInjectionInProgress = false;
    this.isReinitializationInProgress = false;
  }

  // Transition to a new state with awareness of special conditions
  transitionTo(newState, data) {
    // console.log(`[Velocity Animation] Attempting to transition to state: ${newState}`);

    // If prompt injection is in progress, only allow certain state transitions
    if (this.isPromptInjectionInProgress) {
      if (newState !== 'successIdle' && newState !== 'successWithReview' && newState !== 'idle') {
        // console.log(`[Velocity Animation] 🚫 Blocking state transition to ${newState} during prompt injection`);
        return; // Block other state transitions during prompt injection
      }
    }

    // Update current state
    this.currentState = newState;

    // Use the state machine to perform the actual transition
    this.buttonSystem.stateMachine.transition(newState, data);

    // console.log(`[Velocity Animation] ✅ Successfully transitioned to state: ${newState}`);
  }

  // Handle prompt injection
  handlePromptInjection(button) {
    // console.log('[Velocity Animation] 🔄 Handling prompt injection');
    this.isPromptInjectionInProgress = true;

    // Clear existing messages
    // console.log('[Velocity Animation] Clearing all existing messages');
    this.buttonSystem.messageSystem.removeAllMessages();

    // Enable message delay
    // console.log('[Velocity Animation] Enabling message delay for prompt injection');
    this.buttonSystem.messageSystem.enableMessageDelay();

    // Transition to success state
    // console.log('[Velocity Animation] Transitioning to successIdle state for prompt injection');
    this.transitionTo('successIdle', { button });

    // After injection is complete, allow normal operation
    setTimeout(() => {
      this.isPromptInjectionInProgress = false;
      // console.log('[Velocity Animation] ✅ Prompt injection handling complete');
    }, 2000);
  }

  // Handle button reinitialization
  handleButtonReinitialization(button) {
    // console.log('[Velocity Animation] 🔄 Handling button reinitialization');
    this.isReinitializationInProgress = true;

    // Clear existing messages
    // console.log('[Velocity Animation] Clearing all existing messages');
    this.buttonSystem.messageSystem.removeAllMessages();

    // Enable message delay
    // console.log('[Velocity Animation] Enabling 5-second message delay for reinitialization');
    this.buttonSystem.messageSystem.enableMessageDelay();

    // Immediately transition to idle state to start animations
    // console.log('[Velocity Animation] Immediately transitioning to idle state to start animations');
    this.transitionTo('idle', { button });

    // After reinitialization is complete, allow normal operation
    setTimeout(() => {
      this.isReinitializationInProgress = false;
      // console.log('[Velocity Animation] ✅ Button reinitialization handling complete');
    }, 1000);
  }

  // Get current state
  getCurrentState() {
    return this.currentState;
  }
}

// Main Button Animation System
class VelocityButtonSystem {
  constructor(config = {}) {
    this.config = {
      typingTimeout: 1000,
      idleTimeout: 0,
      messageDisplayTime: 4000,
      messageReappearTime: 20000,
      userExperienceKey: 'velocityUserExperience',
      maxWidth: 280,
      firstTimeStates: [3, 6, 7],
      loadingRings: 3,
      ringDelay: 500,
      messagePosition: 'relativeToButton',
      // New config option for empty input check interval
      emptyInputCheckInterval: 1000
    };

    this.stateMachine = new AnimationStateMachine();
    this.animationManager = new AnimationManager();
    this.messageSystem = new MessageSystem();
    this.eventManager = new EventManager();

    // Create the animation controller
    this.animationController = new AnimationController(this);

    this.state = {
      isFirstTimeUser: true,
      currentButton: null,
      buttonPosition: null,
      typingTimer: null,
      idleTimer: null,
      idleInterval: null,
      isSuccessState: false,
      loadingHandler: null,
      ringAnimationInterval: null,
      ringElements: [],
      disableAnimations: false,
      // New state variables for empty input checking
      emptyInputCheckIntervalId: null,
      currentInputElement: null,
      // Track if prompt was injected
      promptWasInjected: false
    };

    this.initialize();
  }

  initialize() {
    this.loadUserExperience();
    this.injectAnimationStyles();
    this.setupStates();
    this.setupAnimations();
    this.setupMessagePositionStrategies();
  }

  loadUserExperience() {
    chrome.storage.local.get([this.config.userExperienceKey], (result) => {
      this.state.isFirstTimeUser = !result[this.config.userExperienceKey];
      //// console.log('[Velocity] User experience:', this.state.isFirstTimeUser ? 'First time' : 'Returning');
    });
  }

  markAsReturningUser() {
    if (this.state.isFirstTimeUser) {
      this.state.isFirstTimeUser = false;
      chrome.storage.local.set({ [this.config.userExperienceKey]: true });
      //// console.log('[Velocity] User marked as returning user');
    }
  }

  shouldShowMessageForState(stateId) {
    return this.state.isFirstTimeUser || this.config.firstTimeStates.includes(stateId);
  }

  injectAnimationStyles() {
    if (document.getElementById('velocity-animation-styles')) return;

    const styleEl = document.createElement('style');
    styleEl.id = 'velocity-animation-styles';
    styleEl.innerHTML = `
      /* Core animations - same as original */
      @keyframes veloInnerPulseAndBounce {
        0% {
          box-shadow: inset 0 0 5px rgba(0, 136, 255, 0.3), 0 0 10px rgba(0, 136, 255, 0.5);
          transform: scale(1) translateX(0);
          border-radius: 50%;
        }
        50% {
          box-shadow: inset 0 0 15px rgba(0, 136, 255, 0.7), 0 0 10px rgba(0, 136, 255, 0.5);
          transform: scale(1.08) translateX(0px);
          border-radius: 50%;
        }
        100% {
          box-shadow: inset 0 0 5px rgba(0, 136, 255, 0.3), 0 0 10px rgba(0, 136, 255, 0.5);
          transform: scale(1) translateX(0);
          border-radius: 50%;
        }
      }

      @keyframes veloHalfCircleGlow {
        0% {
          background: radial-gradient(
            circle at center,
            rgba(0, 136, 255, 0.9) 0%,
            rgba(0, 136, 255, 0.5) 20%,
            rgba(0, 136, 255, 0.1) 45%,
            transparent 80%
          );
          transform: scale(0.7);
          opacity: 0.6;
        }
        50% {
          background: radial-gradient(
            circle at center,
            rgba(0, 136, 255, 0.9) 0%,
            rgba(0, 136, 255, 0.6) 20%,
            rgba(0, 136, 255, 0.3) 30%,
            rgba(0, 136, 255, 0.1) 45%,
            transparent 80%
          );
          transform: scale(1.0);
          opacity: 0.9;
        }
        100% {
          background: radial-gradient(
            circle at center,
            rgba(0, 136, 255, 0.9) 0%,
            rgba(0, 136, 255, 0.5) 20%,
            rgba(0, 136, 255, 0.1) 45%,
            transparent 80%
          );
          transform: scale(0.7);
          opacity: 0.6;
        }
      }

      @keyframes veloTypingScale {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }

      @keyframes veloIdleTypingShakeBounce {
        0% {
          transform: translateY(0) scale(1);
          box-shadow: inset 0 0 20px rgba(0, 136, 255, 0.5), 0 0 15px rgba(0, 136, 255, 0.7);
        }
        50% {
          transform: translateY(-4px) scale(1.03);
          box-shadow: inset 0 0 35px rgba(0, 136, 255, 0.55), 0 0 20px rgba(0, 136, 255, 0.7);
        }
        100% {
          transform: translateY(0) scale(1);
          box-shadow: inset 0 0 20px rgba(0, 136, 255, 0.5), 0 0 15px rgba(0, 136, 255, 0.7);
        }
      }

      @keyframes veloSuccessIdleShakeBounce {
        0% {
          transform: translateY(0) scale(1);
          box-shadow: inset 0 0 15px rgba(0, 136, 255, 0.5), 0 0 10px rgba(0, 136, 255, 0.3);
        }
        50% {
          transform: translateY(-4px) scale(1.03);
          box-shadow: inset 0 0 18px rgba(0, 136, 255, 0.55), 0 0 15px rgba(0, 136, 255, 0.4);
        }
        100% {
          transform: translateY(0) scale(1);
          box-shadow: inset 0 0 15px rgba(0, 136, 255, 0.5), 0 0 10px rgba(0, 136, 255, 0.3);
        }
      }

      @keyframes veloMultiRingPulse {
        0% {
          opacity: 1;
          transform: scale(1);
          border-width: 3px;
        }
        100% {
          opacity: 0;
          transform: scale(2);
          border-width: 1px;
        }
      }

      @keyframes veloRotation {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      /* Animation classes */
      .velocity-inner-pulse-bounce {
        animation: veloInnerPulseAndBounce 2s infinite cubic-bezier(.36,.07,.19,.97);
      }

      .velocity-half-circle-glow {
        position: relative !important;
        z-index: 0 !important;
      }

      .velocity-half-circle-glow::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        margin: 0%;
        background-position: center;
        z-index: -1;
        pointer-events: none;
        animation: veloHalfCircleGlow 1.5s infinite cubic-bezier(.25,.1,.25,1);
      }

      .velocity-typing-scale {
        animation: veloTypingScale 1.5s infinite cubic-bezier(.45,.05,.55,.95) !important;
      }

      .velocity-idle-typing-effect {
        animation: veloIdleTypingShakeBounce 2s infinite cubic-bezier(.36,.07,.19,.97);
      }

      .velocity-success-idle-effect {
        animation: veloSuccessIdleShakeBounce 2s infinite cubic-bezier(.36,.07,.19,.97);
      }

      .velocity-multi-ring-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        border-radius: 50%;
        overflow: visible;
        z-index: 10;
      }

      .velocity-multi-ring {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 3px solid rgba(77, 171, 247, 0.8);
        box-sizing: border-box;
        opacity: 0;
      }

      .velocity-loading-animation {
        background-color: black !important;
        animation: veloRotation 1.5s linear infinite;
      }

      .velocity-message-box {
        position: fixed;
        animation: fadeIn 0.3s ease-in-out;
        z-index: 999999 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
        font-size: 13px;
        font-weight: 500;
        padding: 10px 20px;
        border-radius: 10px;
        color: var(--message-text-color, #000000);
        background-color: var(--message-bg-color, hsl(190, 95%, 90%));
        pointer-events: none;
        max-width: 280px;
        text-align: left;
        border: 2px solid rgba(0, 0, 0, 0.85);
        white-space: pre-line;
      }

      /* Enter key icon styling */
      :root {
        --enter-key-filter: none;
      }

      .dark-theme, html[data-theme="dark"], .velocity-dark-mode {
        --enter-key-filter: invert(1);
      }

      .velocity-enter-icon {
        display: inline-block;
        vertical-align: middle;
        height: 14px;
        filter: var(--enter-key-filter, none);
      }

      /* Dark mode message box styles */
      .dark-theme .velocity-message-box,
      html[data-theme="dark"] .velocity-message-box,
      .velocity-dark-mode .velocity-message-box {
        background-color: hsl(197, 40%, 14%);
        color: #ffffff;
        border: 2px solid rgba(0, 0, 0, 0.85);
      }

      .dark-theme .velocity-enter-icon,
      html[data-theme="dark"] .velocity-enter-icon,
      .velocity-dark-mode .velocity-enter-icon {
        filter: invert(1);
      }

      /* Light mode message box styles */
      .light-theme .velocity-message-box,
      html[data-theme="light"] .velocity-message-box,
      .velocity-light-mode .velocity-message-box {
        background-color: hsl(190, 95%, 90%);
        color: #000000;
        border: 2px solid rgba(0, 0, 0, 0.85);
      }

      .light-theme .velocity-enter-icon,
      html[data-theme="light"] .velocity-enter-icon,
      .velocity-light-mode .velocity-enter-icon {
        filter: none;
      }
    `;
    document.head.appendChild(styleEl);
  }

  setupStates() {
    // States config will be loaded from animationConfig.js
  }

  setupAnimations() {
    // Animation registrations will be loaded from animationConfig.js
  }

  setupMessagePositionStrategies() {
    this.messageSystem.registerPositionStrategy('relativeToButton', (element, config) => {
      const button = config.button || this.state.currentButton;
      if (!button) {
        console.error('[Velocity] No button available for message positioning');
        return;
      }

      const buttonContainer = button.closest('.velocity-button-container');
      const rect = button.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const messageWidth = this.config.maxWidth;

      // Get existing styles to maintain them
      const styleProps = {
        backgroundColor: element.style.backgroundColor,
        color: element.style.color,
        border: element.style.border
      };

      // Common CSS for all positions
      const commonCss = `
        background-color: ${styleProps.backgroundColor || 'hsl(190, 95%, 90%)'};
        color: ${styleProps.color || '#000000'};
        border: ${styleProps.border || '2px solid rgba(0, 0, 0, 0.85)'};
        padding: 10px 20px;
        border-radius: 10px;
        font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
        font-size: 13px;
        font-weight: 500;
        max-width: ${messageWidth}px;
        position: fixed;
        z-index: 999999;
        pointer-events: none;
        text-align: left;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateZ(0);
        white-space: pre-line;
      `;

      // Determine button position
      let buttonPosition = 'right';
      if (buttonContainer && buttonContainer.dataset.anchorPosition) {
        const anchorPosition = buttonContainer.dataset.anchorPosition;
        buttonPosition = anchorPosition.includes('left') ? 'left' : 'right';
      }

      // Check if this is a prompt review message
      const isPromptReview = element.dataset && element.dataset.state === 'promptReview';

      // Special handling for prompt review messages
      if (isPromptReview) {
        if (buttonPosition === 'left' && rect.left > messageWidth + 20) {
          // Left side positioning for left-anchored buttons
          element.style.cssText = `
            ${commonCss}
            left: ${rect.left - messageWidth - 8}px;
            top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
          `;
        } else {
          // Right side positioning for right-anchored buttons
          element.style.cssText = `
            ${commonCss}
            left: ${rect.right + 15}px;
            top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
          `;

          // Make sure it doesn't go off-screen to the right
          if (rect.right + messageWidth + 20 >= viewportWidth) {
            element.style.cssText = `
              ${commonCss}
              left: ${rect.left - messageWidth - 8}px;
              top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
            `;
          }
        }
        return; // Exit early for prompt review messages
      }

      // Position based on button position and available space
      if (buttonPosition === 'left') {
        // Left-anchored button - prefer showing message on the left
        if (rect.left > messageWidth + 20) {
          // Left side positioning
          element.style.cssText = `
            ${commonCss}
            left: ${rect.left - messageWidth - 8}px;
            top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
          `;
        } else if (rect.right + messageWidth + 20 < viewportWidth) {
          // Right side positioning (fallback if not enough space on left)
          element.style.cssText = `
          ${commonCss}
          left: ${rect.right + 15}px;
          top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
        `;
      } else if (rect.top > 80) {
        // Top positioning (second fallback)
        element.style.cssText = `
          ${commonCss}
          left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
          top: ${rect.top - 60}px;
        `;
      } else {
        // Bottom positioning (last fallback)
        element.style.cssText = `
          ${commonCss}
          left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
          top: ${rect.bottom + 10}px;
        `;
      }
    } else {
      // Right-anchored button - prefer showing message on the right
      if (rect.right + messageWidth + 20 < viewportWidth) {
        // Right side positioning
        element.style.cssText = `
          ${commonCss}
          left: ${rect.right + 15}px;
          top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
        `;
      } else if (rect.left > messageWidth + 20) {
        // Left side positioning (fallback if not enough space on right)
        element.style.cssText = `
          ${commonCss}
          left: ${rect.left - messageWidth - 8}px;
          top: ${Math.max(10, Math.min(viewportHeight - 70, rect.top))}px;
        `;
      } else if (rect.top > 80) {
        // Top positioning (second fallback)
        element.style.cssText = `
          ${commonCss}
          left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
          top: ${rect.top - 60}px;
        `;
      } else {
        // Bottom positioning (last fallback)
        element.style.cssText = `
          ${commonCss}
          left: ${Math.max(10, Math.min(viewportWidth - messageWidth - 10, rect.left + (rect.width / 2) - (messageWidth / 2)))}px;
          top: ${rect.bottom + 10}px;
        `;
      }
    }

    // Force reflow to ensure styles are applied
    element.offsetHeight;
  });
}

findButton(platformConfig) {
  const observer = new MutationObserver(() => {
    const button = document.querySelector('.custom-injected-button button');
    if (!button || button === this.state.currentButton) return;

    this.state.currentButton = button;

    const buttonContainer = button.closest('.velocity-button-container');
    if (buttonContainer) {
      this.storeButtonPosition(buttonContainer);
    }

    const inputBox = document.querySelector(platformConfig.textAreaSelector);

    if (button && inputBox) {
      //// console.log('[Velocity] Found button and input element, initializing animations');

      this.setupEventListeners(inputBox, button);
      observer.disconnect();

      // Store reference to input element for checking
      this.state.currentInputElement = inputBox;

      // Start the empty input check interval
      this.startEmptyInputCheck(inputBox, button);

      setTimeout(() => {
        if (this.shouldShowMessageForState(1)) {
          this.showWelcomeMessage(button);
        } else {
          this.stateMachine.transition('idle', { button });
        }
      }, 1000);
    }
  });

  observer.observe(document.body, { childList: true, subtree: true });
}

// New method to start periodic empty input checking
startEmptyInputCheck(inputElement, button) {
  if (this.state.emptyInputCheckIntervalId) {
    clearInterval(this.state.emptyInputCheckIntervalId);
  }

  this.state.emptyInputCheckIntervalId = setInterval(() => {
    this.checkAndHandleEmptyInput(inputElement, button);
  }, this.config.emptyInputCheckInterval);

  //// console.log('[Velocity] Started empty input check interval');
}

// New method to check if input is empty and handle state change
checkAndHandleEmptyInput(inputElement, button) {
  if (!inputElement || !button) return;

  const hasContent = this.checkInputHasContent();
  const currentState = this.stateMachine.getCurrentState();

  // If input is empty and we're not already in idle state
  if (!hasContent && currentState !== 'idle') {
    //// console.log('[Velocity] Input is empty, transitioning to idle state from', currentState);

    // If we were in success state, reset it
    if (this.state.isSuccessState) {
      this.state.isSuccessState = false;
    }

    // Transition to idle state
    this.stateMachine.transition('idle', { button });
  }
  // If input has content and we're in idle state (and not just started)
  else if (hasContent && currentState === 'idle' && this.state.hasStartedTyping) {
    //// console.log('[Velocity] Input has content, transitioning to typingStopped state');
    this.stateMachine.transition('typingStopped', { button });
  }
}

setupEventListeners(inputElement, button) {
  if (!inputElement || !button) return;

  // Input event handling - modified to check for empty input
  const handleInput = () => {
    // Mark that user has started typing at least once
    this.state.hasStartedTyping = true;

    // Clear previous timers
    clearTimeout(this.state.typingTimer);
    clearTimeout(this.state.idleTimer);
    clearInterval(this.state.idleInterval);

    // Check if input is empty - immediately go to idle if it is
    const hasContent = this.checkInputHasContent();

    // If we're in success state, check if input became empty
    if (this.state.isSuccessState && !hasContent) {
      //// console.log('[Velocity] Success state with empty input, resetting to idle state');
      this.state.isSuccessState = false;
      this.stateMachine.transition('idle', { button });
      return;
    }

    // Don't change state if already in success state with content
    if (this.state.isSuccessState) return;

    // If empty, go to idle immediately
    if (!hasContent) {
      this.stateMachine.transition('idle', { button });
      return;
    }

    // Otherwise go to typing state
    this.stateMachine.transition('typing', { button });

    // Set timer for typing stopped state
    this.state.typingTimer = setTimeout(() => {
      if (this.state.isSuccessState) return;

      // Double check content before transitioning to typingStopped
      if (this.checkInputHasContent()) {
        this.stateMachine.transition('typingStopped', { button });
      } else {
        this.stateMachine.transition('idle', { button });
      }
    }, this.config.typingTimeout);
  };

  // Add keyboard event listeners
  this.eventManager.on('input', inputElement, handleInput);
  this.eventManager.on('keyup', inputElement, handleInput);

  // Special handling for backspace and delete keys
  const handleDelete = (e) => {
    if (e.key === 'Backspace' || e.key === 'Delete') {
      // Schedule a check after the key event is fully processed
      setTimeout(() => {
        const hasContent = this.checkInputHasContent();
        if (!hasContent && this.stateMachine.getCurrentState() !== 'idle') {
          //// console.log('[Velocity] Delete event made input empty, transitioning to idle state');

          // Reset success state if applicable
          if (this.state.isSuccessState) {
            this.state.isSuccessState = false;
          }

          this.stateMachine.transition('idle', { button });
        }
      }, 50);
    }
  };

  this.eventManager.on('keydown', inputElement, handleDelete);

  // Button event handling
  let pressTimer = null;
  let isDragging = false;

  this.eventManager.on('mousedown', button, () => {
    pressTimer = setTimeout(() => isDragging = true, 200);
  });

  this.eventManager.on('mouseup', button, () => {
    clearTimeout(pressTimer);
    if (!isDragging) {
      this.handleButtonClick(button);
    }
    isDragging = false;
  });

  this.eventManager.on('click', button, (e) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
    }
  });

  // Updated success handler to check for empty input
  window.markPromptSuccess = (promptReviewBoxVisible = false) => {
    // Check if input is empty - if so, go back to idle instead of success
    if (!this.checkInputHasContent()) {
      //// console.log('[Velocity] Input is empty after success, resetting to idle state');
      this.state.isSuccessState = false;
      this.stateMachine.transition('idle', { button });
    } else {
      this.handleSuccessState(button, promptReviewBoxVisible);
    }
  };

  window.updatePromptReviewStatus = (isVisible) => {
    this.updatePromptReviewStatus(isVisible);
  };
}

handleButtonClick(button) {
  if (!this.checkInputHasContent()) {
    this.messageSystem.showMessage('warning', {
      text: 'Please enter some text before enhancing!',
      type: 'warning',
      button,
      positionStrategy: 'relativeToButton',
      duration: 3000
    }, 'high'); // Use high priority for warnings
    return;
  }

  this.markAsReturningUser();

  // Use the animation controller for state transition
  this.animationController.transitionTo('loading', { button });
}

// Updated to check for empty content and use animation controller
handleSuccessState(button, promptReviewBoxVisible = false) {
  // First check if input is empty - if so, go to idle instead
  if (!this.checkInputHasContent()) {
    //// console.log('[Velocity] Input is empty in success handler, going to idle state');
    this.state.isSuccessState = false;
    this.animationController.transitionTo('idle', { button });
    return;
  }

  this.state.isSuccessState = true;

  // Explicitly clean up all loading animations first
  this.cleanupLoadingAnimation(button);
  this.animationManager.stopAnimation('loading');

  // Remove any loading classes
  button.classList.remove('velocity-loading-animation');

  // Then transition to the appropriate success state
  const state = promptReviewBoxVisible ? 'successWithReview' : 'successIdle';

  if (promptReviewBoxVisible) {
    // If review box is open, don't show messages
    this.messageSystem.removeMessage('successWithReview');
    this.messageSystem.removeMessage('successIdle');
    this.messageSystem.removeMessage('loading');

    // Just update state without showing messages
    this.state.isSuccessState = true;
    this.animationManager.startAnimation(state, button);
  } else {
    // If review box is not open, do normal transition
    this.animationController.transitionTo(state, { button });

    // Show success message with medium priority
    if (this.shouldShowMessageForState(7)) {
      this.messageSystem.showMessage('successIdle', {
        text: '<div style="font-size: 14px; font-weight: 600; color: var(--velocity-text-color, var(--dark-mode) ? "#ffffff" : "#000");">Prompt enhanced!</div><div style="font-size: 13px; color: var(--velocity-secondary-text-color, var(--dark-mode) ? "#9CA3AF" : "#0B0B0Bb7");">Hit <img src="' + chrome.runtime.getURL("assets/enterkey.png") + '" class="velocity-enter-icon" style="height: 20px; vertical-align: middle; filter: var(--enter-key-filter, none);"> if you are satisfied</div>',
        type: 'success',
        button,
        positionStrategy: 'relativeToButton',
        isHTML: true,
        customStyle: 'border: 1px solid var(--velocity-border-color, rgba(0, 0, 0, 0.1)); box-shadow: 4px 2px 10px var(--velocity-shadow-color, rgba(0, 0, 0, 0.6)); border-radius: 8px; background-color: var(--velocity-background-color, #fff);'
      }, 'medium');
    }
  }

  //// console.log('[Velocity] Success state activated:', state);
}

// New method to handle prompt injection from main UI
handlePromptInjection(button) {
  // Use the animation controller to handle the injection process
  this.animationController.handlePromptInjection(button);

  // Mark that a prompt was injected
  this.state.promptWasInjected = true;

  // After a delay, show a success message with medium priority
  setTimeout(() => {
    if (this.shouldShowMessageForState(7)) {
      this.messageSystem.showMessage('promptInjected', {
        text: 'Prompt successfully injected!',
        type: 'success',
        button,
        positionStrategy: 'relativeToButton',
        duration: 3000
      }, 'medium');
    }
  }, 2000);
}

// Updated to check for empty content and use animation controller
updatePromptReviewStatus(isVisible) {
  // Check if input is empty first
  if (!this.checkInputHasContent()) {
    //// console.log('[Velocity] Input is empty when updating prompt review status, going to idle state');
    this.state.isSuccessState = false;
    this.animationController.transitionTo('idle', { button: this.state.currentButton });
    return;
  }

  if (this.state.isSuccessState) {
    const state = isVisible ? 'successWithReview' : 'successIdle';

    // When review box is closed, show the message
    if (!isVisible) {
      this.animationController.transitionTo(state, { button: this.state.currentButton });

      // Show success message with medium priority when review box is closed
      if (this.shouldShowMessageForState(7)) {
        this.messageSystem.showMessage('successIdle', {
          text: '<div style="font-size: 14px; font-weight: 600; color: var(--velocity-text-color, var(--dark-mode) ? "#ffffff" : "#000");">Prompt enhanced!</div><div style="font-size: 13px; color: var(--velocity-secondary-text-color, var(--dark-mode) ? "#9CA3AF" : "#0B0B0Bb7");">Hit <img src="' + chrome.runtime.getURL("assets/enterkey.png") + '" class="velocity-enter-icon" style="height: 20px; vertical-align: middle; filter: var(--enter-key-filter, none);"> if you are satisfied</div>',
          type: 'success',
          button: this.state.currentButton,
          positionStrategy: 'relativeToButton',
          isHTML: true,
          customStyle: 'border: 1px solid var(--velocity-border-color, rgba(0, 0, 0, 0.1)); box-shadow: 4px 2px 10px var(--velocity-shadow-color, rgba(0, 0, 0, 0.6)); border-radius: 8px; background-color: var(--velocity-background-color, #fff);'
        }, 'medium');
      }
    } else {
      // When review box is opened, hide all messages
      this.messageSystem.removeAllMessages();

      // Just update state without showing messages
      this.state.isSuccessState = true;
      this.animationManager.startAnimation(state, this.state.currentButton);
    }
  }
}

checkInputHasContent() {
  try {
    const possibleInputs = document.querySelectorAll('textarea, div[contenteditable="true"], [role="textbox"]');

    for (const inputBox of possibleInputs) {
      let content = "";

      if (inputBox.tagName === "TEXTAREA") {
        content = inputBox.value.trim();
      } else if (inputBox.hasAttribute("contenteditable")) {
        content = inputBox.innerText.trim();
      } else {
        content = (inputBox.value || inputBox.innerText || inputBox.textContent || "").trim();
      }

      if (content.length > 0) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("[Velocity] Error checking input content:", error);
    return true;
  }
}

storeButtonPosition(buttonContainer) {
  if (!buttonContainer) return;

  this.state.buttonPosition = {
    top: buttonContainer.style.top,
    left: buttonContainer.style.left,
    right: buttonContainer.style.right,
    bottom: buttonContainer.style.bottom,
    transform: buttonContainer.style.transform,
    anchorPosition: buttonContainer.dataset.anchorPosition
  };
}

restoreButtonPosition(buttonContainer) {
  if (!buttonContainer || !this.state.buttonPosition) return;

  Object.assign(buttonContainer.style, this.state.buttonPosition);

  if (this.state.buttonPosition.anchorPosition) {
    buttonContainer.dataset.anchorPosition = this.state.buttonPosition.anchorPosition;
  }
}

showWelcomeMessage(button) {
  this.messageSystem.showMessage('welcome', {
    text: "Hey I'm Velocity! I can help you to enhance your prompt.",
    type: 'info',
    button,
    positionStrategy: 'relativeToButton',
    duration: 6000
  });
}

createMultiRingAnimation(button) {
  this.cleanupLoadingAnimation(button);

  let ringContainer = button.querySelector('.velocity-multi-ring-container');
  if (!ringContainer) {
    ringContainer = document.createElement('div');
    ringContainer.className = 'velocity-multi-ring-container';
    button.appendChild(ringContainer);
  }

  for (let i = 0; i < this.config.loadingRings; i++) {
    const ring = document.createElement('div');
    ring.className = 'velocity-multi-ring';
    ring.style.animation = `veloMultiRingPulse 2s ease-out ${i * this.config.ringDelay}ms infinite`;
    ringContainer.appendChild(ring);
    this.state.ringElements.push(ring);
  }

  this.startRingAnimationCycle(button);
}

startRingAnimationCycle(button) {
  // Make sure to clear any existing interval first
  if (this.state.ringAnimationInterval) {
    clearInterval(this.state.ringAnimationInterval);
  }

  this.state.ringAnimationInterval = setInterval(() => {
    // Continue animation until explicitly stopped, regardless of state
    const ringContainer = button.querySelector('.velocity-multi-ring-container');
    if (ringContainer) {
      const ring = document.createElement('div');
      ring.className = 'velocity-multi-ring';
      ring.style.animation = 'veloMultiRingPulse 2s ease-out 0ms forwards';
      ringContainer.appendChild(ring);

      setTimeout(() => {
        if (ring.parentNode) {
          ring.parentNode.removeChild(ring);
        }
      }, 2100);
    }
  }, 800);
}

cleanupLoadingAnimation(button) {
  if (this.state.ringAnimationInterval) {
    clearInterval(this.state.ringAnimationInterval);
    this.state.ringAnimationInterval = null;
  }

  const existingContainer = button.querySelector('.velocity-multi-ring-container');
  if (existingContainer) {
    button.removeChild(existingContainer);
  }

  this.state.ringElements = [];
}

// Public API
init(platformConfig) {
  this.findButton(platformConfig);
}

reset() {
  this.state.isSuccessState = false;
  this.animationManager.stopAllAnimations();
  this.messageSystem.removeAllMessages();
  this.eventManager.offAll();

  clearTimeout(this.state.typingTimer);
  clearTimeout(this.state.idleTimer);
  clearInterval(this.state.idleInterval);

  if (this.state.loadingHandler) {
    this.state.loadingHandler.clear();
    this.state.loadingHandler = null;
  }

  if (this.state.currentButton) {
    this.cleanupLoadingAnimation(this.state.currentButton);
  }

  // Also clear the empty input check interval
  if (this.state.emptyInputCheckIntervalId) {
    clearInterval(this.state.emptyInputCheckIntervalId);
    this.state.emptyInputCheckIntervalId = null;
  }

  this.stateMachine.transition('idle', { button: this.state.currentButton });
}

disableAnimations() {
  this.state.disableAnimations = true;
  this.animationManager.stopAllAnimations();
  this.messageSystem.removeAllMessages();

  if (this.state.currentButton) {
    this.state.currentButton.classList.remove(
      'velocity-loading-animation',
      'velocity-half-circle-glow',
      'velocity-inner-pulse-bounce',
      'velocity-idle-typing-effect',
      'velocity-success-idle-effect'
    );
  }
}

enableAnimations() {
  this.state.disableAnimations = false;
}
}

// Export the system
window.VelocityButtonSystem = VelocityButtonSystem;