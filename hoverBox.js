// hoverBox.js - Enhanced with self-contained functionality

// Global state for hoverBox
window.velocityHoverBoxState = {
  initialized: false,
  activeHoverBox: null,
  hoverTimeout: null
};

/**
 * Helper function to find the injected button with various selectors
 * @returns {HTMLElement} - The found button element or null if not found
 */
function findButtonElement() {
  const selectors = [
    '.custom-injected-button button',
    '.velocity-button',
    '.velocity-button-container button',
    'button.velocity-button',
    '.velocity-wrapper button',
  ];
  
  for (const selector of selectors) {
    const button = document.querySelector(selector);
    if (button) {
      // console.log(`[Velocity] Button found with selector: ${selector}`);
      return button;
    }
  }
  
  // console.log("[Velocity] Button not found with any selector");
  return null;
}

/**
 * Update hover box position when window resizes or content changes
 * @param {HTMLElement} button - The button element
 * @param {HTMLElement} hoverBox - The hover box element
 */
function updateHoverBoxPosition(button, hoverBox) {
  if (button && hoverBox && window.velocityHoverBoxState.initialized) {
    positionHoverBox(button, hoverBox);
  }
}

/**
 * Create a hover box for the specified platform
 * @param {string} platform - Platform identifier (e.g., 'chatgpt', 'claude')
 * @returns {HTMLElement} - The hover box element
 */
function createHoverBox(platform) {
  const hoverBox = document.createElement("div");
  hoverBox.className = "velocity-hover-box";

  // Platform-specific styles with dark mode support
  const platformStyles = {
    chatgpt: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #4b5563;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: rgba(0, 183, 235, 0.2);
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: rgba(0, 183, 235, 0.2);
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    claude: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #4b5563;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    gemini: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #4b5563;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    },
    grok: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #4b5563;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    perplexity: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    mistral: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    default: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #444;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #E6F7FA;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #2A3B4C;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: rgba(0, 183, 235, 0.2);
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: rgba(0, 183, 235, 0.2);
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
      `,
    },
    bolt: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    },
    vercelv0: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    },
    gamma: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    },
    lovable: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    },
    replit: {
      position: "bottom",
      hoverBoxCssLight: `
        background-color: #F0FBFF;
        color: black;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E0F7FA;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      hoverBoxCssDark: `
        background-color: #2A3B4C;
        color: white;
        padding: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        position: fixed;
        z-index: 999999;
        width: 160px;
      `,
      buttonActiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonActiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #00B7EB;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonInactiveCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: transparent;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssLight: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #B0E0E6;
        color: black;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
      buttonHoverCssDark: `
        width: 100%;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #4b5563;
        color: white;
        padding: 6px 10px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
        margin: 2px 0;
        border: none;
      `,
    }
  };

  // Function to apply styles based on dark mode
  function applyStyles(isDarkMode) {
    const styles = platformStyles[platform] || platformStyles.default;
    
    if (platform === "chatgpt" || platform === "claude" || platform === "gemini" || platform === "grok" || platform === "mistral" || platform === "perplexity" || platform === "bolt" || platform === "vercelv0" || platform === "gamma" || platform === "lovable" || platform === "replit") {
      hoverBox.style.cssText = `
        position: fixed;
        width: 160px;
        height: fit-content;
        font-size: 14px;
        border-radius: 8px;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;
        pointer-events: none;
        z-index: 999999;
        ${isDarkMode ? styles.hoverBoxCssDark : styles.hoverBoxCssLight}
      `;
    } else {
      hoverBox.style.cssText = `
        position: absolute;
        left: 100%;
        margin-left: 20px;
        width: 160px;
        height: fit-content;
        font-size: 14px;
        border-radius: 8px;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;
        pointer-events: none;
        ${styles.position === "bottom" ? "bottom: 100%;" : "top: 100%;"}
        ${isDarkMode ? styles.hoverBoxCssDark : styles.hoverBoxCssLight}
      `;
    }
    
    hoverBox.querySelectorAll("button").forEach(button => {
      const isActive = button.classList.contains("active");
      button.style.cssText = isActive 
        ? (isDarkMode ? styles.buttonActiveCssDark : styles.buttonActiveCssLight)
        : (isDarkMode ? styles.buttonInactiveCssDark : styles.buttonInactiveCssLight);
    });
  }

  // Initial style application
  let isDarkMode = false;
  try {
    chrome.storage.local.get(["darkMode"], (result) => {
      isDarkMode = result.darkMode === true;
      applyStyles(isDarkMode);
    });
  } catch (e) {
    // console.log("Error accessing chrome storage:", e);
    applyStyles(isDarkMode);
  }

  // Listen for dark mode changes
  try {
    chrome.storage.onChanged.addListener((changes, area) => {
      if (area === "local" && "darkMode" in changes) {
        applyStyles(changes.darkMode.newValue);
      }
    });
  } catch (e) {
    // console.log("Error setting up storage listener:", e);
  }

  // Icon definitions with updated SVGs to match the image
  const iconColor = (isDarkMode) => isDarkMode ? "#fff" : "#000";
  const styleIcons = {
    Descriptive: `<img src="chrome-extension://${chrome.runtime.id}/assets/descriptive.png" width="16" height="16" alt="Descriptive" style="filter: ${isDarkMode ? 'invert(1)' : 'none'}"/>`,
    Creative: `<img src="chrome-extension://${chrome.runtime.id}/assets/creative.png" width="16" height="16" alt="Creative" style="filter: ${isDarkMode ? 'invert(1)' : 'none'}"/>`,
    Professional: `<img src="chrome-extension://${chrome.runtime.id}/assets/professional.png" width="16" height="16" alt="Professional" style="filter: ${isDarkMode ? 'invert(1)' : 'none'}"/>`,
    Concise: `<img src="chrome-extension://${chrome.runtime.id}/assets/concise.png" width="16" height="16" alt="Concise" style="filter: ${isDarkMode ? 'invert(1)' : 'none'}"/>`,
  };

  // Create selectable styles with icons
  const styleOptions = ["Descriptive", "Creative", "Concise", "Professional"];
  let selectedStyle = "Descriptive";

  try {
    chrome.storage.local.get(["selectedStyle"], (result) => {
      if (result.selectedStyle) {
        selectedStyle = result.selectedStyle;
      }
    });
  } catch (e) {
    // console.log("Error loading saved style preference:", e);
  }

  try {
    chrome.storage.local.get(["darkMode"], (result) => {
      const isDarkMode = result.darkMode === true;
      const styles = platformStyles[platform] || platformStyles.default;
  
      styleOptions.forEach((style) => {
        const button = document.createElement("button");
        button.type = "button";
        
        button.style.cssText = style === selectedStyle
          ? (isDarkMode ? styles.buttonActiveCssDark : styles.buttonActiveCssLight)
          : (isDarkMode ? styles.buttonInactiveCssDark : styles.buttonInactiveCssLight);
        
        if (style === selectedStyle) {
          button.classList.add("active");
        }

        // Update icon color based on style selected state
        const isSelected = style === selectedStyle;
        const iconImg = styleIcons[style].replace(/style="filter: .*?"/, `style="filter: ${isSelected ? 'none' : (isDarkMode ? 'invert(1)' : 'none')}"`);

        button.innerHTML = `
          ${iconImg}
          <span style="display: inline-block; white-space: nowrap; color: ${isSelected ? 'white' : 'inherit'}; transition: color 0.2s;">${style}</span>
        `;
  
        button.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();
          selectedStyle = style;
  
          chrome.storage.local.get(["darkMode"], (result) => {
            const currentDarkMode = result.darkMode === true;
            hoverBox.querySelectorAll("button").forEach((btn) => {
              const btnText = btn.innerText.trim();
              btn.classList.remove("active");
              btn.style.cssText = currentDarkMode 
                ? styles.buttonInactiveCssDark 
                : styles.buttonInactiveCssLight;
              if (styleIcons[btnText]) {
                const iconImg = styleIcons[btnText].replace(/style="filter: .*?"/, `style="filter: ${currentDarkMode ? 'invert(1)' : 'none'}"`);
                btn.innerHTML = `
                  ${iconImg}
                  <span style="display: inline-block; white-space: nowrap; color: inherit; transition: color 0.2s;">${btnText}</span>
                `;
              }
            });
            
            button.classList.add("active");
            button.style.cssText = currentDarkMode 
              ? styles.buttonActiveCssDark 
              : styles.buttonActiveCssLight;
              
            const activeIconImg = styleIcons[style].replace(/style="filter: .*?"/, `style="filter: none"`);
            button.innerHTML = `
              ${activeIconImg}
              <span style="display: inline-block; white-space: nowrap; color: white; transition: color 0.2s;">${style}</span>
            `;
          });
  
          hoverBox.dispatchEvent(
            new CustomEvent("styleSelected", { detail: { selectedStyle } })
          );
          return false;
        };
  
        button.addEventListener("mouseenter", () => {
          if (style !== selectedStyle) {
            button.style.cssText = isDarkMode ? styles.buttonHoverCssDark : styles.buttonHoverCssLight;
            // Update the span color when hovering
            const span = button.querySelector('span');
            if (span) {
              span.style.color = 'inherit';
            }
          }
        });
  
        button.addEventListener("mouseleave", () => {
          if (style !== selectedStyle) {
            button.style.cssText = isDarkMode ? styles.buttonInactiveCssDark : styles.buttonInactiveCssLight;
            // Reset span color to default
            const span = button.querySelector('span');
            if (span) {
              span.style.color = 'inherit';
            }
          }
        });
  
        hoverBox.appendChild(button);
      });
    });
  } catch (e) {
    // console.log("Error setting up hover box buttons:", e);
  }

  return hoverBox;
}

/**
 * Initialize hover box functionality for the Velocity button
 */
function initializeVelocityHoverBox() {
  if (window.velocityHoverBoxState.initialized) {
    // console.log("[Velocity] Hover box already initialized");
    return;
  }
  
  // console.log("[Velocity] Initializing Velocity hover box");
  
  const button = findButtonElement();
  if (!button) {
    // console.log("[Velocity] Button not found, will retry hover box initialization");
    setTimeout(initializeVelocityHoverBox, 500);
    return;
  }
  
  const currentURL = window.location.href;
  let platform = "default";
  
  if (window.platforms) {
    for (const key in window.platforms) {
      if (window.platforms[key].urlPattern && window.platforms[key].urlPattern.test(currentURL)) {
        platform = key;
        break;
      }
    }
  }
  
  // console.log(`[Velocity] Detected platform for hover box: ${platform}`);
  
  const hoverBox = createHoverBox(platform);
  
  const buttonContainer = button.closest('.velocity-button-container');
  if (buttonContainer) {
    buttonContainer.appendChild(hoverBox);
  } else {
    document.body.appendChild(hoverBox);
  }
  
  window.velocityHoverBoxState.activeHoverBox = hoverBox;
  
  setupHoverEvents(button, hoverBox);
  
  hoverBox.addEventListener("styleSelected", (event) => {
    const selectedStyle = event.detail.selectedStyle;
    // console.log(`[Velocity] Style selected: ${selectedStyle}`);
    
    try {
      chrome.storage.local.set({ selectedStyle: selectedStyle }, function() {
        // console.log(`[Velocity] Style ${selectedStyle} saved to storage`);
      });
      
      try {
        localStorage.setItem('velocitySelectedStyle', selectedStyle);
      } catch (err) {
        //console.warn("[Velocity] Couldn't save style to localStorage:", err);
      }
      
      chrome.runtime.sendMessage({
        action: "styleSelected",
        style: selectedStyle,
        platform: platform
      });
    } catch (e) {
      // console.log("Error saving style preference:", e);
      try {
        localStorage.setItem('velocitySelectedStyle', selectedStyle);
      } catch (err) {
        // console.log("Also failed to use localStorage:", err);
      }
    }
  });
  
  chrome.storage.local.get(["selectedStyle"], (result) => {
    if (result.selectedStyle) {
      // console.log(`[Velocity] Setting initial style from storage: ${result.selectedStyle}`);
      
      const buttons = hoverBox.querySelectorAll("button");
      buttons.forEach((button) => {
        const buttonText = button.innerText.trim();
        if (buttonText === result.selectedStyle) {
          // console.log(`[Velocity] Found matching button for style: ${result.selectedStyle}`);
          setTimeout(() => button.click(), 100);
        }
      });
    }
  });
  
  window.velocityHoverBoxState.initialized = true;
  
  // console.log("[Velocity] Hover box successfully initialized");
}

/**
 * Set up hover events for button and hover box
 */
function setupHoverEvents(button, hoverBox) {
  button.addEventListener("mouseenter", function() {
    clearTimeout(window.velocityHoverBoxState.hoverTimeout);
    
    positionHoverBox(button, hoverBox);
    
    hoverBox.style.opacity = "1";
    hoverBox.style.visibility = "visible";
    hoverBox.style.pointerEvents = "auto";
  });
  
  button.addEventListener("mouseleave", function(e) {
    const buttonRect = button.getBoundingClientRect();
    const hoverBoxRect = hoverBox.getBoundingClientRect();
    
    if (isMovingTowards(e, buttonRect, hoverBoxRect)) {
      return;
    }
    
    window.velocityHoverBoxState.hoverTimeout = setTimeout(function() {
      hoverBox.style.opacity = "0";
      hoverBox.style.visibility = "hidden";
      hoverBox.style.pointerEvents = "none";
    }, 100);
  });
  
  hoverBox.addEventListener("mouseenter", function() {
    clearTimeout(window.velocityHoverBoxState.hoverTimeout);
    hoverBox.style.opacity = "1";
    hoverBox.style.visibility = "visible";
    hoverBox.style.pointerEvents = "auto";
  });
  
  hoverBox.addEventListener("mouseleave", function() {
    window.velocityHoverBoxState.hoverTimeout = setTimeout(function() {
      hoverBox.style.opacity = "0";
      hoverBox.style.visibility = "hidden";
      hoverBox.style.pointerEvents = "none";
    }, 100);
  });
  
  window.addEventListener("resize", function() {
    if (hoverBox.style.visibility === "visible") {
      positionHoverBox(button, hoverBox);
    }
  });
  
  window.addEventListener("scroll", function() {
    if (hoverBox.style.visibility === "visible") {
      positionHoverBox(button, hoverBox);
    }
  });
}

/**
 * Check if mouse is moving towards the hover box
 */
function isMovingTowards(event, fromRect, toRect) {
  const mouseX = event.clientX;
  const mouseY = event.clientY;
  
  if (toRect.left > fromRect.right && mouseX > fromRect.right) {
    return true;
  }
  
  if (toRect.right < fromRect.left && mouseX < fromRect.left) {
    return true;
  }
  
  if (toRect.bottom < fromRect.top && mouseY < fromRect.top) {
    return true;
  }
  
  if (toRect.top > fromRect.bottom && mouseY > fromRect.bottom) {
    return true;
  }
  
  return false;
}

/**
 * Position the hover box relative to the button based on platform and button position
 */
function positionHoverBox(button, hoverBox) {
  const buttonRect = button.getBoundingClientRect();
  const hoverBoxRect = hoverBox.getBoundingClientRect();
  
  const buttonContainer = button.closest('.velocity-button-container');
  const buttonContainerRect = buttonContainer ? buttonContainer.getBoundingClientRect() : buttonRect;
  
  const anchorPosition = buttonContainer && buttonContainer.dataset.anchorPosition 
    ? buttonContainer.dataset.anchorPosition 
    : 'top-right';
  
  const side = anchorPosition.includes('right') ? 'left' : 'right';
  
  const currentURL = window.location.href;
  let platform = "default";
  
  if (window.platforms) {
    for (const key in window.platforms) {
      if (window.platforms[key].urlPattern && window.platforms[key].urlPattern.test(currentURL)) {
        platform = key;
        break;
      }
    }
  }
  
  let top, left;
  
  if (platform === "chatgpt" || platform === "claude" || platform === "gemini" || platform === "grok"  || platform === "lovable" || platform === "replit") {
    hoverBox.style.position = "fixed";
    top = buttonRect.top - hoverBoxRect.height - 10;
    
    if (side === 'left') {
      left = buttonRect.left;
    } else {
      left = buttonRect.right - hoverBoxRect.width;
    }
  } else {
    hoverBox.style.position = "absolute";
    
    top = -hoverBoxRect.height - 10;
    
    if (side === 'left') {
      left = 0;
    } else {
      left = buttonContainerRect.width - hoverBoxRect.width;
    }
  }
  
  hoverBox.style.top = `${top}px`;
  hoverBox.style.left = `${left}px`;
  
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  const updatedRect = hoverBox.getBoundingClientRect();
  
  if (updatedRect.right > viewportWidth - 10) {
    left -= (updatedRect.right - viewportWidth + 10);
    hoverBox.style.left = `${left}px`;
  }
  
  if (updatedRect.left < 10) {
    left += (10 - updatedRect.left);
    hoverBox.style.left = `${left}px`;
  }
  
  if (updatedRect.top < 10) {
    if (platform === "chatgpt" || platform === "claude" || platform === "gemini" || platform === "grok"  || platform === "lovable" || platform === "replit") {
      top = buttonRect.bottom + 10;
    } else {
      top = buttonContainerRect.height + 10;
    }
    hoverBox.style.top = `${top}px`;
  }
}

/**
 * Get the currently selected style
 * @param {Function} callback - Optional callback to receive the style value
 * @returns {string} The selected style or "Descriptive" as fallback
 */
function getSelectedStyle(callback) {
  try {
    chrome.storage.local.get(["selectedStyle"], function(result) {
      const style = result.selectedStyle || "Descriptive";
      // console.log(`[Velocity] Retrieved style from storage: ${style}`);
      
      if (callback && typeof callback === 'function') {
        callback(style);
      }
    });
  } catch (e) {
    // console.error("[Velocity] Error getting selected style from storage:", e);
    
    try {
      const localStyle = localStorage.getItem('velocitySelectedStyle') || "Descriptive";
      // console.log(`[Velocity] Retrieved style from localStorage fallback: ${localStyle}`);
      
      if (callback && typeof callback === 'function') {
        callback(localStyle);
      }
      return localStyle;
    } catch (err) {
      // console.error("[Velocity] Error getting selected style from localStorage:", err);
      
      if (callback && typeof callback === 'function') {
        callback("Descriptive");
      }
      return "Descriptive";
    }
  }
}

/**
 * Update the selected style used for prompt enhancement
 * @param {string} newStyle - The new style to set
 * @returns {boolean} Success status
 */
function updateSelectedStyle(newStyle) {
  if (!["Descriptive", "Creative", "Concise", "Professional"].includes(newStyle)) {
    // console.error(`[Velocity] Invalid style: ${newStyle}`);
    return false;
  }
  
  // console.log(`[Velocity] Setting style to: ${newStyle}`);
  
  try {
    chrome.storage.local.set({ selectedStyle: newStyle }, function() {
      // console.log(`[Velocity] Style ${newStyle} saved to storage`);
    });
    
    try {
      localStorage.setItem('velocitySelectedStyle', newStyle);
    } catch (err) {
      //console.warn("[Velocity] Couldn't save style to localStorage:", err);
    }
    
    const hoverBox = window.velocityHoverBoxState.activeHoverBox;
    if (hoverBox) {
      const styleButtons = hoverBox.querySelectorAll("button");
      styleButtons.forEach(button => {
        const buttonText = button.innerText.trim();
        if (buttonText === newStyle) {
          button.click();
        }
      });
    }
    
    return true;
  } catch (e) {
    // console.error("[Velocity] Error updating selected style:", e);
    return false;
  }
}

/**
 * Reset hover box system
 */
function resetHoverBox() {
  if (window.velocityHoverBoxState.hoverTimeout) {
    clearTimeout(window.velocityHoverBoxState.hoverTimeout);
  }
  
  if (window.velocityHoverBoxState.activeHoverBox) {
    try {
      window.velocityHoverBoxState.activeHoverBox.remove();
    } catch (e) {
      // console.log("Error removing hover box:", e);
    }
  }
  
  window.velocityHoverBoxState.initialized = false;
  window.velocityHoverBoxState.activeHoverBox = null;
}

// Export functions to global scope
window.createHoverBox = createHoverBox;
window.initializeVelocityHoverBox = initializeVelocityHoverBox;
window.updateSelectedStyle = updateSelectedStyle;
window.getSelectedStyle = getSelectedStyle;
window.resetHoverBox = resetHoverBox;

// Fallback for // console.log
if (!window.console) {
  window.console = { log: function() {}, error: function() {} };
}

// console.log("[Velocity] HoverBox.js loaded successfully");
