/**
 * Writing Quality Analyzer
 *
 * This module handles real-time writing quality analysis via WebSocket connection.
 * It monitors input fields for text changes and updates a quality indicator UI.
 */

// Global state for the quality analyzer
window.velocityQualityState = {
  // WebSocket connection state
  socket: null,
  reconnectAttempts: 0,
  isConnected: false,
  messageQueue: [],

  // UI elements
  indicator: null,

  // Analysis state
  currentQuality: 'idle', // idle, bad, ok, good
  isAnalyzing: false,
  lastAnalyzedText: '',
  lastCheckedText: '',  // For tracking content changes in periodic checks
  debounceTimer: null,
  contentCheckInterval: null,  // For periodic content checks

  // Button interaction state
  hiddenByButtonClick: false,  // Flag to track if indicator was hidden by button click
  indicatorStateBeforeDrag: null, // Store indicator state before dragging

  // Configuration
  MAX_RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 5000,
  DEBOUNCE_DELAY: 1000,

  // Platform info
  platform: null
};

/**
 * Initialize the quality analyzer
 * @param {Object} config - Configuration options
 */
function initQualityAnalyzer(config = {}) {
  // Merge config with defaults
  const mergedConfig = {
    platform: window.velocityWrapperState?.platform || detectPlatform(),
    ...config
  };

  window.velocityQualityState.platform = mergedConfig.platform;

  // Create and inject the quality indicator
  createQualityIndicator();

  // Connect to WebSocket
  connectWebSocket();

  // Start monitoring input fields
  startInputMonitoring();

  // console.log("[Velocity Quality] Analyzer initialized for platform:", mergedConfig.platform);
}

/**
 * Create and inject the quality indicator UI
 */
function createQualityIndicator() {
  // First check if we already have an indicator
  if (window.velocityQualityState.indicator) {
    return;
  }

  // Try to get the button from the wrapper state first
  let buttonContainer = null;
  let velocityButton = null;

  // Check if we have a button in the wrapper state
  if (window.velocityWrapperState && window.velocityWrapperState.button) {
    buttonContainer = window.velocityWrapperState.button;
    velocityButton = buttonContainer.querySelector('button');
    // console.log("[Velocity Quality] Found button from wrapper state");
  }

  // If not found in wrapper state, try to find it in the DOM
  if (!velocityButton) {
    velocityButton = document.querySelector('.velocity-button-container button, .custom-injected-button button');
    if (velocityButton) {
      buttonContainer = velocityButton.closest('.velocity-button-container, .custom-injected-button');
      // console.log("[Velocity Quality] Found button from DOM query");
    }
  }

  // If still not found, retry after a short delay
  if (!velocityButton || !buttonContainer) {
    // console.log("[Velocity Quality] Button not found, will retry in 500ms");
    setTimeout(createQualityIndicator, 500);
    return;
  }

  // Create the indicator element
  const indicator = document.createElement('div');
  indicator.className = 'velocity-quality-indicator velocity-quality-idle';

  // Set explicit positioning styles to ensure it appears on the bottom left
  indicator.style.position = 'absolute';
  indicator.style.width = '12px';
  indicator.style.height = '12px';
  indicator.style.borderRadius = '50%';
  indicator.style.border = '2px solid #ffffff';
  indicator.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.2)';
  indicator.style.zIndex = '10000';

  // Append the indicator to the button element itself for better positioning
  velocityButton.style.position = 'relative';
  velocityButton.appendChild(indicator);
  // console.log("[Velocity Quality] Indicator attached directly to button element");

  // Store references
  window.velocityQualityState.indicator = indicator;

  // console.log("[Velocity Quality] Indicator created and attached to button");
}

/**
 * Check if the input field is empty and ensure idle state if it is
 * @returns {boolean} True if input is empty, false otherwise
 */
function ensureIdleStateForEmptyInput() {
  const platform = window.velocityQualityState.platform;
  if (!platform || !window.platforms) {
    return false;
  }

  const platformConfig = window.platforms[platform];
  if (!platformConfig || !platformConfig.textAreaSelector) {
    return false;
  }

  // Find the input field
  let inputField = window.velocityWrapperState?.inputBox;
  if (!inputField) {
    inputField = document.querySelector(platformConfig.textAreaSelector);
  }

  if (!inputField) {
    return false;
  }

  // Check if input is empty
  const text = getInputText(inputField);
  const isEmpty = !text || text.trim().length === 0;

  if (isEmpty) {
    // console.log("[Velocity Quality] Input is empty, forcing idle state");
    // Force update to idle state without calling updateQualityIndicator to avoid recursion
    const { indicator } = window.velocityQualityState;
    if (indicator) {
      // Remove all quality classes
      indicator.classList.remove(
        'velocity-quality-idle',
        'velocity-quality-bad',
        'velocity-quality-ok',
        'velocity-quality-good',
        'velocity-quality-analyzing'
      );

      // Add idle class
      indicator.classList.add('velocity-quality-idle');

      // Update state
      window.velocityQualityState.currentQuality = 'idle';
      window.velocityQualityState.isAnalyzing = false;
    }
  }

  return isEmpty;
}

/**
 * Update the quality indicator UI
 * @param {string} quality - The quality level (idle, bad, ok, good)
 * @param {boolean} isAnalyzing - Whether analysis is in progress
 */
function updateQualityIndicator(quality, isAnalyzing = false) {
  // First check if input is empty - if so, always use idle state
  if (ensureIdleStateForEmptyInput()) {
    return; // Exit early if input is empty
  }

  const { indicator } = window.velocityQualityState;

  if (!indicator) {
    return;
  }

  // Update state
  window.velocityQualityState.currentQuality = quality;
  window.velocityQualityState.isAnalyzing = isAnalyzing;

  // Remove all quality classes
  indicator.classList.remove(
    'velocity-quality-idle',
    'velocity-quality-bad',
    'velocity-quality-ok',
    'velocity-quality-good',
    'velocity-quality-analyzing'
  );

  // Add appropriate class
  indicator.classList.add(`velocity-quality-${quality}`);

  // Add analyzing class if needed
  if (isAnalyzing) {
    indicator.classList.add('velocity-quality-analyzing');
  }
}

/**
 * Connect to the WebSocket server
 */
function connectWebSocket() {
  const state = window.velocityQualityState;

  // console.log("[Velocity Quality] Attempting to connect to WebSocket server");
  // console.log("[Velocity Quality] Current reconnect attempts:", state.reconnectAttempts);

  if (state.reconnectAttempts >= state.MAX_RECONNECT_ATTEMPTS) {
    // console.error(`[Velocity Quality] Maximum reconnect attempts (${state.MAX_RECONNECT_ATTEMPTS}) reached!`);
    return;
  }

  if (state.socket) {
    try {
      // console.log("[Velocity Quality] Closing existing WebSocket connection");
      state.socket.close();
    } catch (e) {
      console.warn("[Velocity Quality] Error closing existing connection:", e);
    }
  }

  try {
    // Use the specified WebSocket endpoint for domain analysis
    // console.log("[Velocity Quality] Connecting to WebSocket at: wss://thinkvelocity.in/python-backend-D/ws/domain-analysis/ws");
    state.socket = new WebSocket("wss://thinkvelocity.in/python-backend-D/ws/domain-analysis");

    state.socket.onopen = () => {
      // console.log("[Velocity Quality] WebSocket connection established successfully");
      // console.log("[Velocity Quality] WebSocket state:", state.socket.readyState);
      state.isConnected = true;
      state.reconnectAttempts = 0;

      // Check if there's an input field and if it's empty, set to idle
      const platform = window.velocityQualityState.platform;
      if (platform && window.platforms) {
        const platformConfig = window.platforms[platform];
        if (platformConfig && platformConfig.textAreaSelector) {
          let inputField = window.velocityWrapperState?.inputBox;
          if (!inputField) {
            inputField = document.querySelector(platformConfig.textAreaSelector);
          }

          if (inputField) {
            const text = getInputText(inputField);
            if (!text || text.trim().length === 0) {
              updateQualityIndicator('idle', false);
              state.lastAnalyzedText = '';
            }
          }
        }
      }

      // Process any queued messages
      const queueLength = state.messageQueue.length;
      // console.log(`[Velocity Quality] Processing ${queueLength} queued messages`);

      while (state.messageQueue.length > 0) {
        const queuedMessage = state.messageQueue.shift();
        // console.log("[Velocity Quality] Sending queued message:", queuedMessage);
        state.socket.send(JSON.stringify(queuedMessage));
      }

      // Start heartbeat
      // console.log("[Velocity Quality] Starting heartbeat");
      startHeartbeat();
    };

    state.socket.onmessage = (event) => {
      try {
        const response = JSON.parse(event.data);

        // Log all responses received from WebSocket
        // console.log("[Velocity Quality] WebSocket response received:", response);

        // Log quality information
        if (response.quality) {
            // console.log("[Velocity Quality] Quality:", response.quality);
            // console.log("[Velocity Quality] Quality Score:", response.quality_score);
            if (response.quality_reasons && response.quality_reasons.length > 0) {
                // console.log("[Velocity Quality] Quality Reasons:", response.quality_reasons);
            }

            // Only update quality if input is not empty
            if (!ensureIdleStateForEmptyInput()) {
                // Update the quality indicator based on the response
                let quality = 'idle';

                if (response.quality === 'bad') {
                    quality = 'bad';
                } else if (response.quality === 'ok') {
                    quality = 'ok';
                } else if (response.quality === 'good') {
                    quality = 'good';
                }

                updateQualityIndicator(quality, false);
            }
        }

        // Check if this is a quality analysis response
        if (response.quality_analysis) {
          // console.log("[Velocity Quality] Quality analysis data:", response.quality_analysis);
          handleQualityAnalysisResponse(response.quality_analysis);
        }
      } catch (error) {
        // console.error("[Velocity Quality] Error processing message:", error);
      }
    };

    state.socket.onclose = (event) => {
      // console.log(`[Velocity Quality] Connection closed (code: ${event.code}, reason: ${event.reason || 'No reason provided'})`);
      // console.log(`[Velocity Quality] Was clean close: ${event.wasClean}`);
      state.isConnected = false;

      // Update UI to show disconnected state
      updateQualityIndicator('idle', false);

      // Attempt to reconnect
      state.reconnectAttempts++;
      // console.log(`[Velocity Quality] Connection closed, scheduling reconnect attempt ${state.reconnectAttempts} in ${state.RECONNECT_DELAY}ms`);
      setTimeout(connectWebSocket, state.RECONNECT_DELAY);
    };

    state.socket.onerror = (error) => {
      // console.error("[Velocity Quality] WebSocket error occurred:", error);
      // console.log("[Velocity Quality] WebSocket state at error:", state.socket.readyState);
      // console.log("[Velocity Quality] Closing socket due to error");
      if (state.socket) state.socket.close();
    };
  } catch (error) {
    // console.error("[Velocity Quality] Failed to create WebSocket connection:", error);
    // console.log("[Velocity Quality] Error details:", error.message);

    // Update UI to show error state
    updateQualityIndicator('idle', false);

    // Attempt to reconnect
    state.reconnectAttempts++;
    // console.log(`[Velocity Quality] Scheduling reconnect attempt ${state.reconnectAttempts} in ${state.RECONNECT_DELAY}ms`);
    setTimeout(connectWebSocket, state.RECONNECT_DELAY);
  }
}

/**
 * Send heartbeat to keep connection alive
 */
function startHeartbeat() {
  const state = window.velocityQualityState;

  // Clear any existing interval
  if (state.heartbeatInterval) {
    clearInterval(state.heartbeatInterval);
  }

  // Set up new interval
  state.heartbeatInterval = setInterval(() => {
    if (state.socket && state.socket.readyState === WebSocket.OPEN) {
      try {
        const heartbeatData = {
          type: "heartbeat",
          client_timestamp: Date.now(),
          module: "quality_analyzer"
        };
        state.socket.send(JSON.stringify(heartbeatData));
      } catch (error) {
        // console.error("[Velocity Quality] Error sending heartbeat:", error);
      }
    }
  }, 30000); // 30 second interval
}

/**
 * Handle quality analysis response from the server
 * @param {Object} analysis - The quality analysis data
 */
function handleQualityAnalysisResponse(analysis) {
  // Log the full analysis data
  // console.log("[Velocity Quality] Processing quality analysis:", analysis);

  // First check if input is empty - if so, always use idle state
  if (ensureIdleStateForEmptyInput()) {
    return; // Exit early if input is empty
  }

  // Update the quality indicator based on the analysis
  let quality = 'idle';

  if (analysis.score !== undefined) {
    // console.log("[Velocity Quality] Quality score:", analysis.score);

    if (analysis.score < 0.4) {
      quality = 'bad';
    } else if (analysis.score < 0.7) {
      quality = 'ok';
    } else {
      quality = 'good';
    }

    // console.log("[Velocity Quality] Determined quality level:", quality);
  } else {
    // console.log("[Velocity Quality] No score found in analysis data");
  }

  // Only update if input is still not empty (double-check)
  if (!ensureIdleStateForEmptyInput()) {
    updateQualityIndicator(quality, false);
  }
}

/**
 * Start monitoring input fields for text changes
 */
function startInputMonitoring() {
  const platform = window.velocityQualityState.platform;

  if (!platform || !window.platforms) {
    // console.error("[Velocity Quality] Cannot start monitoring: platform not detected");
    return;
  }

  const platformConfig = window.platforms[platform];
  if (!platformConfig || !platformConfig.textAreaSelector) {
    // console.error("[Velocity Quality] Invalid platform configuration");
    return;
  }

  // Find the input field
  let inputField = window.velocityWrapperState?.inputBox;

  if (!inputField) {
    inputField = document.querySelector(platformConfig.textAreaSelector);
  }

  if (!inputField) {
    // console.error("[Velocity Quality] Could not find input field to monitor");
    return;
  }

  // Check initial state - if input is empty, set to idle
  const initialText = getInputText(inputField);
  if (!initialText || initialText.trim().length === 0) {
    updateQualityIndicator('idle', false);
    window.velocityQualityState.lastAnalyzedText = '';
  }

  // Set up input event listeners
  const monitorInput = () => {
    const state = window.velocityQualityState;
    const text = getInputText(inputField);

    // Debug the input monitoring
    // console.log("[Velocity Quality] monitorInput called:", {
    //   textLength: text ? text.length : 0,
    //   textFirstChars: text ? text.substring(0, 30) + "..." : "null/empty",
    //   lastAnalyzedTextLength: state.lastAnalyzedText ? state.lastAnalyzedText.length : 0,
    //   lastAnalyzedTextFirstChars: state.lastAnalyzedText ? state.lastAnalyzedText.substring(0, 30) + "..." : "null/empty",
    //   isEmpty: !text || text.trim().length === 0,
    //   callStack: new Error().stack
    // });

    // Immediately check if text is empty and set to idle if it is
    if (!text || text.trim().length === 0) {
      // console.log("[Velocity Quality] Text is empty, setting to idle");
      updateQualityIndicator('idle', false);
      return; // Exit early if there's no text to analyze
    }

    // Clear existing timer
    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
    }

    // Show analyzing state
    updateQualityIndicator(state.currentQuality, true);

    // Debounce the analysis request
    state.debounceTimer = setTimeout(() => {
      const currentText = getInputText(inputField);

      // Check again if text is empty (might have changed during debounce)
      if (!currentText || currentText.trim().length === 0) {
        // Reset to idle if text is empty
        updateQualityIndicator('idle', false);
        state.lastAnalyzedText = '';
        return;
      }

      // Add detailed debugging to understand text comparison
      // console.log("[Velocity Quality] Text comparison debug:", {
      //   currentText: currentText ? currentText.substring(0, 50) + "..." : "null/empty",
      //   lastAnalyzedText: state.lastAnalyzedText ? state.lastAnalyzedText.substring(0, 50) + "..." : "null/empty",
      //   currentTextLength: currentText ? currentText.length : 0,
      //   lastAnalyzedTextLength: state.lastAnalyzedText ? state.lastAnalyzedText.length : 0,
      //   areEqual: currentText === state.lastAnalyzedText,
      //   currentTextTrimmed: currentText ? currentText.trim().substring(0, 50) + "..." : "null/empty",
      //   lastAnalyzedTextTrimmed: state.lastAnalyzedText ? state.lastAnalyzedText.trim().substring(0, 50) + "..." : "null/empty",
      //   areEqualAfterTrim: currentText && state.lastAnalyzedText ? currentText.trim() === state.lastAnalyzedText.trim() : false,
      //   source: "enhance button debug"
      // });

      // Only analyze if text has changed and is not empty
      if (currentText && currentText !== state.lastAnalyzedText) {
        // console.log("[Velocity Quality] Text changed, sending for analysis");
        // Trim the text to remove any leading/trailing whitespace
        const trimmedText = currentText.trim();
        sendTextForAnalysis(trimmedText);
        state.lastAnalyzedText = trimmedText;
      } else {
        // console.log("[Velocity Quality] Text unchanged or condition failed:", {
        //   hasCurrentText: !!currentText,
        //   isDifferentFromLast: currentText !== state.lastAnalyzedText
        // });
        // Text hasn't changed, stop analyzing animation
        updateQualityIndicator(state.currentQuality, false);
      }
    }, state.DEBOUNCE_DELAY);
  };

  // Add event listeners based on element type
  if (inputField.tagName === 'TEXTAREA' || inputField.tagName === 'INPUT') {
    // Listen for standard input events
    inputField.addEventListener('input', monitorInput);

    // Add clipboard event listeners to catch paste operations
    inputField.addEventListener('paste', monitorInput);

    // Add keyup listeners for specific keys that might modify content
    inputField.addEventListener('keyup', (e) => {
      // Check for keys that might modify content: Ctrl+V, Ctrl+X, Delete, Backspace
      if (e.key === 'v' && (e.ctrlKey || e.metaKey) ||
          e.key === 'x' && (e.ctrlKey || e.metaKey) ||
          e.key === 'Delete' ||
          e.key === 'Backspace') {
        monitorInput();
      }
    });

    // Set up a periodic check for content changes
    const periodicCheck = () => {
      const state = window.velocityQualityState;
      const currentText = getInputText(inputField);

      // Debug the periodic check
      // console.log("[Velocity Quality] Periodic check:", {
      //   currentTextLength: currentText ? currentText.length : 0,
      //   currentTextFirstChars: currentText ? currentText.substring(0, 30) + "..." : "null/empty",
      //   lastCheckedTextLength: state.lastCheckedText ? state.lastCheckedText.length : 0,
      //   lastCheckedTextFirstChars: state.lastCheckedText ? state.lastCheckedText.substring(0, 30) + "..." : "null/empty",
      //   hasChanged: currentText !== state.lastCheckedText,
      //   lastAnalyzedTextLength: state.lastAnalyzedText ? state.lastAnalyzedText.length : 0,
      //   source: "periodic check"
      // });

      // If text has changed but no events were triggered, run monitorInput
      if (currentText !== state.lastCheckedText) {
        // console.log("[Velocity Quality] Text changed in periodic check, running monitorInput");
        state.lastCheckedText = currentText;
        monitorInput();
      }
    };

    // Check every 2 seconds for changes that might have been missed
    const checkInterval = setInterval(periodicCheck, 2000);
    window.velocityQualityState.contentCheckInterval = checkInterval;

  } else if (inputField.getAttribute('contenteditable') === 'true') {
    // For contenteditable elements, add more comprehensive event listeners
    inputField.addEventListener('input', monitorInput);
    inputField.addEventListener('paste', monitorInput);
    inputField.addEventListener('cut', monitorInput);
    inputField.addEventListener('keyup', monitorInput);

    // Also use MutationObserver as a fallback
    const observer = new MutationObserver(monitorInput);
    observer.observe(inputField, {
      childList: true,
      characterData: true,
      subtree: true
    });

    // Store observer for cleanup
    window.velocityQualityState.inputObserver = observer;
  } else {
    // For other elements, use MutationObserver with more comprehensive config
    const observer = new MutationObserver(monitorInput);
    observer.observe(inputField, {
      childList: true,
      characterData: true,
      subtree: true,
      attributes: true,
      characterDataOldValue: true
    });

    // Store observer for cleanup
    window.velocityQualityState.inputObserver = observer;
  }

  // Try to find the enhance button and add a listener to it
  try {
    // Look for the enhance button in the DOM
    const enhanceButton = document.querySelector('.velocity-enhance-button, [data-testid="velocity-enhance-button"]');
    if (enhanceButton) {
      // console.log("[Velocity Quality] Found enhance button, adding click listener");
      enhanceButton.addEventListener('click', () => {
        // console.log("[Velocity Quality] Enhance button clicked, scheduling text check");
        // Schedule multiple checks after the enhance button is clicked
        // This helps catch the text change that happens after enhancement
        setTimeout(() => {
          // console.log("[Velocity Quality] Running post-enhance check (250ms)");
          // Force reset the lastAnalyzedText to ensure the condition passes
          window.velocityQualityState.lastAnalyzedText = '';
          monitorInput();
        }, 250);

        setTimeout(() => {
          // console.log("[Velocity Quality] Running post-enhance check (500ms)");
          window.velocityQualityState.lastAnalyzedText = '';
          monitorInput();
        }, 500);

        setTimeout(() => {
          // console.log("[Velocity Quality] Running post-enhance check (1000ms)");
          window.velocityQualityState.lastAnalyzedText = '';
          monitorInput();
        }, 1000);
      });
    } else {
      // console.log("[Velocity Quality] Enhance button not found");
    }
  } catch (error) {
    // console.error("[Velocity Quality] Error setting up enhance button listener:", error);
  }

  // console.log("[Velocity Quality] Input monitoring started");
}

/**
 * Get text from input field
 * @param {Element} inputField - The input field element
 * @returns {string} The text content
 */
function getInputText(inputField) {
  if (!inputField) {
    // console.log("[Velocity Quality] getInputText: Input field is null or undefined");
    return '';
  }

  let text = '';
  const tagName = inputField.tagName;
  const isContentEditable = inputField.getAttribute('contenteditable') === 'true';

  if (tagName === 'TEXTAREA' || tagName === 'INPUT') {
    text = inputField.value;
  } else if (isContentEditable) {
    text = inputField.innerText;
  } else {
    text = inputField.textContent || '';
  }

  // Debug the text retrieval (only log occasionally to avoid console spam)
  if (Math.random() < 0.1) { // Only log ~10% of the time
    // console.log("[Velocity Quality] getInputText:", {
    //   tagName: tagName,
    //   isContentEditable: isContentEditable,
    //   textLength: text ? text.length : 0,
    //   textFirstChars: text ? text.substring(0, 30) + "..." : "null/empty",
    //   source: "getInputText debug"
    // });
  }

  return text;
}

/**
 * Send text to the server for quality analysis
 * @param {string} text - The text to analyze
 */
function sendTextForAnalysis(text) {
  const state = window.velocityQualityState;

  // Log that we're sending text for analysis
  // console.log("[Velocity Quality] Sending text for analysis, length:", text.length);

  // Prepare message data
  const messageData = {
    text: text,
    prompt: text, // Add prompt field as the server might be expecting this
    client_timestamp: Date.now(),
    platform: state.platform || 'unknown',
    type: 'quality_analysis_request'
  };

  // Log the message data being sent
  // console.log("[Velocity Quality] Message data:", messageData);

  // Send if connected, queue if not
  if (state.socket && state.socket.readyState === WebSocket.OPEN) {
    try {
      // console.log("[Velocity Quality] WebSocket is open, sending data");
      state.socket.send(JSON.stringify(messageData));
    } catch (error) {
      // console.error("[Velocity Quality] Error sending text for analysis:", error);
      state.messageQueue.push(messageData);
      // console.log("[Velocity Quality] Added message to queue due to error");
    }
  } else {
    state.messageQueue.push(messageData);
    // console.log("[Velocity Quality] WebSocket not open, added message to queue");

    // Try to reconnect if not already connecting
    if (!state.isConnected && state.reconnectAttempts < state.MAX_RECONNECT_ATTEMPTS) {
      // console.log("[Velocity Quality] Attempting to reconnect WebSocket");
      connectWebSocket();
    }
  }
}

/**
 * Clean up the quality analyzer
 */
function cleanupQualityAnalyzer() {
  const state = window.velocityQualityState;

  // Close WebSocket
  if (state.socket) {
    try {
      state.socket.close();
    } catch (e) {
      console.warn("[Velocity Quality] Error closing WebSocket:", e);
    }
    state.socket = null;
  }

  // Clear intervals
  if (state.heartbeatInterval) {
    clearInterval(state.heartbeatInterval);
    state.heartbeatInterval = null;
  }

  if (state.contentCheckInterval) {
    clearInterval(state.contentCheckInterval);
    state.contentCheckInterval = null;
  }

  if (state.debounceTimer) {
    clearTimeout(state.debounceTimer);
    state.debounceTimer = null;
  }

  // Disconnect observer
  if (state.inputObserver) {
    state.inputObserver.disconnect();
    state.inputObserver = null;
  }

  // Remove indicator
  if (state.indicator && state.indicator.parentNode) {
    state.indicator.parentNode.removeChild(state.indicator);
    state.indicator = null;
  }

  // console.log("[Velocity Quality] Analyzer cleaned up");
}

/**
 * Detect the current platform
 * @returns {string|null} The detected platform or null
 */
function detectPlatform() {
  const currentURL = window.location.href;

  if (!window.platforms) {
    // console.error("[Velocity Quality] Platforms not available");
    return null;
  }

  for (const key in window.platforms) {
    if (window.platforms[key].urlPattern && window.platforms[key].urlPattern.test(currentURL)) {
      return key;
    }
  }

  return null;
}

// Export functions for use in content-script.js
window.velocityQualityAnalyzer = {
  init: initQualityAnalyzer,
  cleanup: cleanupQualityAnalyzer,
  updateIndicator: updateQualityIndicator
};
